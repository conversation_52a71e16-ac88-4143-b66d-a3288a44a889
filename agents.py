"""
Camel-AI Agent 模块 - 智能对话代理
=================================

功能：
1. 定义主要的"喵呜"Agent
2. 集成记忆和数据库功能
3. 提供专业的对话生成
4. 支持个性化和上下文感知

设计原则：
- 完全保持原有对话质量和风格
- 集成数据库记忆功能
- 支持多轮对话上下文
- 为未来的多Agent协作预留接口
"""

from typing import Dict, List, Optional, Any
import json
from datetime import datetime

# Camel-AI 核心导入
try:
    # 🚨 临时禁用Camel-AI导入，避免魔搭部署问题
    # from camel.agents import ChatAgent
    # from camel.messages import BaseMessage
    # from camel.types import RoleType
    # from camel.models import ModelFactory
    # from camel.types import ModelPlatformType
    CAMEL_AVAILABLE = False
    print("⚠️ Camel-AI 导入已禁用，使用统一API服务")
except ImportError as e:
    print(f"⚠️ Camel-AI 库未安装，使用兼容模式: {e}")
    CAMEL_AVAILABLE = False

# 本地模块导入
from database_manager import get_database_manager
from state_manager import ConversationState, get_state_manager
from config_modelscope import DASHSCOPE_API_KEY, RECOMMENDED_MODEL

class MemoryEnhancedChatAgent:
    """
    记忆增强的聊天代理
    集成数据库记忆功能，支持个性化对话
    """
    
    def __init__(self, user_id: int, system_message: str, model_instance=None):
        """
        初始化记忆增强聊天代理
        
        Args:
            user_id: 用户ID
            system_message: 系统提示词
            model_instance: 模型实例（可选）
        """
        self.user_id = user_id
        self.db_manager = get_database_manager()
        self.state_manager = get_state_manager()
        
        # 如果Camel-AI可用，使用Camel-AI Agent
        if CAMEL_AVAILABLE and model_instance:
            self.agent = self._create_camel_agent(system_message, model_instance)
            self.use_camel = True
        else:
            # 回退到基础模式
            self.agent = None
            self.use_camel = False
            self.system_message = system_message
        
        print(f"🤖 {'Camel-AI' if self.use_camel else '兼容'}模式 Agent 已创建 (用户ID: {user_id})")
    
    def _create_camel_agent(self, system_message: str, model_instance):
        """
        创建Camel-AI ChatAgent实例
        
        Args:
            system_message: 系统提示词
            model_instance: 模型实例
            
        Returns:
            ChatAgent: Camel-AI代理实例
        """
        try:
            # 🚨 Camel-AI已禁用，直接返回None
            # sys_msg = BaseMessage.make_assistant_message(
            #     role_name="喵呜",
            #     content=system_message
            # )
            # agent = ChatAgent(sys_msg, model=model_instance)
            # return agent
            return None
        except Exception as e:
            print(f"⚠️ Camel-AI Agent创建失败，使用兼容模式: {e}")
            return None
    
    def step(self, user_input: str, conversation_state: ConversationState = None, system_messages: Optional[List[Dict]] = None, recent_history: Optional[List[Dict]] = None) -> str:
        """
        处理用户输入并生成回复 - 强化版，确保智能回复
        
        Args:
            user_input: 用户输入
            conversation_state: 当前对话状态（可选）
            system_messages: 包含系统提示和用户档案/对话历史的messages列表 (从app.py传入)
            recent_history: 近期对话历史 (从app.py传入)
            
        Returns:
            str: Agent回复
        """
        try:
            # 1. 构建上下文（从数据库获取历史对话和用户档案） - 现在直接使用传入的system_messages和recent_history
            # 为了兼容旧逻辑，如果system_messages没有传入，才自行构建
            if system_messages is None:
                context = self._build_context(conversation_state) # 仍保留旧的context构建，作为fallback
                system_messages = [
                    {
                        "role": "system",
                        "content": self._get_personalized_system_prompt(context) + "\n\n【当前会话用户档案】\n" + context
                    }
                ]
            else:
                # 如果system_messages已传入，则其已经包含了user_profile_summary和recent_history
                context = "从app.py传入的上下文已整合到system_messages中"
            
            print(f"🤖 Agent模式: {'Camel-AI' if self.use_camel else '兼容模式'}")
            print(f"📝 用户输入: {user_input[:50]}...")
            
            # 2. 🚨 强制使用智能生成，避免模板回复
            # 优先尝试Camel-AI，失败则用增强的API回复
            if self.use_camel and self.agent:
                try:
                    # 🚨 关键修改：将完整的messages传递给Camel Agent
                    # 注意：如果Camel-AI启用，需要根据其API设计调整这里
                    response = self._generate_camel_response(user_input, system_messages)
                    print("✅ Camel-AI回复生成成功")
                except Exception as e:
                    print(f"⚠️ Camel-AI失败，使用API模式: {e}")
                    response = self._generate_api_response(user_input, system_messages)
            else:
                print("📡 使用API智能回复模式")
                response = self._generate_api_response(user_input, system_messages)
            
            # 🚨 修复：移除双重保存，由主调用函数统一处理对话保存
            
            return response
            
        except Exception as e:
            print(f"❌ Agent处理失败: {e}")
            import traceback
            traceback.print_exc()
            # 最后的应急回复，也要个性化
            # 从传入的system_messages中提取user_name
            user_name = "同学"
            if system_messages and len(system_messages) > 0 and "用户核心档案" in system_messages[0]['content']:
                try:
                    user_profile_summary_lines = system_messages[0]['content'].split("【用户核心档案】\n")[1].split("\n\n【近期对话回顾】")[0].split("\n")
                    for line in user_profile_summary_lines:
                        if line.startswith("- 昵称:"):
                            user_name = line.split(":")[1].strip()
                            break
                except: # fallback to default
                    pass

            return f"抱歉{user_name}，我刚才遇到了一点小问题，但我很想继续和你聊天！能再说一遍吗？我会认真听的～ 💝"
    
    def _build_context(self, conversation_state: ConversationState = None) -> str:
        """
        🌟 增强版上下文构建 - 完整用户画像记忆系统
        构建包含引导阶段深度信息的完整上下文，确保智能体具备个性化记忆
        
        Args:
            conversation_state: 当前对话状态
            
        Returns:
            str: 完整的上下文字符串，包含用户画像、引导洞察、对话历史
        """
        context_parts = []
        
        # 1. 获取完整用户档案
        user_profile = self.db_manager.get_user_profile(self.user_id)
        user_info = self.db_manager.get_user(self.user_id)
        
        # 2. 🌟 核心用户信息画像
        if user_info:
            nickname = user_info.get('nickname', '同学')
            context_parts.append(f"===== 用户画像 =====")
            context_parts.append(f"昵称: {nickname}")
            
        if user_profile:
            # 2.1 基础情绪状态
            if user_profile.get('current_emotion'):
                context_parts.append(f"💭 情绪状态: {user_profile['current_emotion']}")
            
            # 2.2 压力与需求分析
            if user_profile.get('current_pressure_sources'):
                context_parts.append(f"⚡ 压力源: {user_profile['current_pressure_sources']}")
            if user_profile.get('current_desire'):
                context_parts.append(f"💝 核心需求: {user_profile['current_desire']}")
            
            # 2.3 支持系统和回血方式
            if user_profile.get('current_energy_ritual'):
                context_parts.append(f"🔋 回血方式: {user_profile['current_energy_ritual']}")
            if user_profile.get('current_support_network'):
                context_parts.append(f"🤝 支持网络: {user_profile['current_support_network']}")
            
            # 2.4 🧠 荣格八维性格深度分析
            jungian_insight = self._extract_jungian_insight(user_profile)
            if jungian_insight:
                context_parts.append(f"🧠 性格洞察: {jungian_insight}")
        
        # 3. 🔍 引导阶段关键洞察提取
        onboarding_insights = self._extract_onboarding_insights()
        if onboarding_insights:
            context_parts.append("===== 引导阶段关键洞察 =====")
            context_parts.extend(onboarding_insights)
        
        # 4. 📚 相关对话历史（智能筛选）
        relevant_history = self._get_relevant_conversation_history()
        if relevant_history:
            context_parts.append("===== 相关对话记忆 =====")
            context_parts.extend(relevant_history)
        
        # 5. 当前状态信息
        if conversation_state:
            state_desc = self.state_manager.get_state_description(conversation_state)
            context_parts.append(f"当前阶段: {state_desc}")
        
        return "\n".join(context_parts)
    
    def _extract_jungian_insight(self, user_profile: dict) -> str:
        """
        🧠 提取荣格八维性格洞察
        将荣格测试结果转换为个性化的性格理解
        """
        try:
            jungian_raw = user_profile.get('jungian_scores', {})
            if isinstance(jungian_raw, str):
                jungian_scores = json.loads(jungian_raw)
            else:
                jungian_scores = jungian_raw
            
            if not jungian_scores:
                return ""
            
            # 解析性格类型
            personality_insights = []
            
            # E/I 维度分析
            e_score = jungian_scores.get('E', 0)
            i_score = jungian_scores.get('I', 0)
            if e_score > i_score:
                personality_insights.append("倾向外向，从社交互动中获得能量")
            elif i_score > e_score:
                personality_insights.append("倾向内向，从独处思考中获得能量")
            
            # N/S 维度分析
            n_score = jungian_scores.get('N', 0)
            s_score = jungian_scores.get('S', 0)
            if n_score > s_score:
                personality_insights.append("注重直觉和可能性，喜欢探索抽象概念")
            elif s_score > n_score:
                personality_insights.append("注重具体感知，喜欢实际和细节")
            
            # T/F 维度分析
            t_score = jungian_scores.get('T', 0)
            f_score = jungian_scores.get('F', 0)
            if t_score > f_score:
                personality_insights.append("决策时偏重逻辑分析")
            elif f_score > t_score:
                personality_insights.append("决策时偏重价值观和感受")
            
            # J/P 维度分析
            j_score = jungian_scores.get('J', 0)
            p_score = jungian_scores.get('P', 0)
            if j_score > p_score:
                personality_insights.append("喜欢结构化和计划性")
            elif p_score > j_score:
                personality_insights.append("喜欢灵活性和开放性")
            
            return "，".join(personality_insights)
            
        except Exception as e:
            print(f"荣格洞察提取失败: {e}")
            return ""
    
    def _extract_onboarding_insights(self) -> list:
        """
        🔍 提取引导阶段的关键洞察
        从引导对话历史中提取重要的心理洞察和成长点
        """
        insights = []
        
        try:
            # 获取引导阶段的对话历史（更大范围）
            all_history = self.db_manager.get_conversation_history(self.user_id, limit=50)
            
            # 寻找系统思维阶段的关键对话
            emotion_what_content = ""
            emotion_why_content = ""
            emotion_how_content = ""
            
            for msg in all_history:
                content = msg.get('content', '')
                # 识别系统思维三阶段的内容
                if '困扰' in content or '问题' in content or '烦恼' in content:
                    if not emotion_what_content and len(content) > 50:  # 避免短回复
                        emotion_what_content = content[:200]  # 取前200字符
                
                if '原因' in content or '为什么' in content or '导致' in content:
                    if not emotion_why_content and len(content) > 50:
                        emotion_why_content = content[:200]
                
                if '怎么办' in content or '解决' in content or '改善' in content:
                    if not emotion_how_content and len(content) > 50:
                        emotion_how_content = content[:200]
            
            # 构建洞察信息
            if emotion_what_content:
                insights.append(f"🎯 主要困扰：{emotion_what_content}")
            if emotion_why_content:
                insights.append(f"🔍 根因分析：{emotion_why_content}")
            if emotion_how_content:
                insights.append(f"💡 解决思路：{emotion_how_content}")
            
        except Exception as e:
            print(f"引导洞察提取失败: {e}")
        
        return insights
    
    def _get_relevant_conversation_history(self) -> list:
        """
        📚 智能筛选相关对话历史
        不只是获取最近对话，而是筛选出情感上相关的重要对话片段
        """
        relevant_conversations = []
        
        try:
            # 获取更多历史对话进行智能筛选
            history = self.db_manager.get_conversation_history(self.user_id, limit=15)
            
            # 筛选标准：包含情感词汇、问题描述、解决方案等的对话
            emotional_keywords = [
                '感觉', '觉得', '心情', '压力', '焦虑', '开心', '难过', 
                '困扰', '问题', '帮助', '支持', '理解', '感谢'
            ]
            
            for msg in history[-8:]:  # 从最近的8条中筛选
                content = msg.get('content', '')
                role = "👤" if msg['role'] == 'user' else "🤖"
                
                # 如果包含情感关键词或者是用户的重要表达
                if any(keyword in content for keyword in emotional_keywords):
                    relevant_conversations.append(f"{role}: {content[:120]}...")
                elif len(content) > 100 and msg['role'] == 'user':  # 用户的长回复通常包含重要信息
                    relevant_conversations.append(f"{role}: {content[:120]}...")
                
                # 限制数量，避免上下文过长
                if len(relevant_conversations) >= 4:
                    break
            
        except Exception as e:
            print(f"相关对话筛选失败: {e}")
        
        return relevant_conversations
    
    def _generate_camel_response(self, user_input: str, system_messages: List[Dict]) -> str:
        """
        使用Camel-AI生成回复
        
        Args:
            user_input: 用户输入
            system_messages: 包含系统提示和用户档案/对话历史的messages列表
            
        Returns:
            str: 生成的回复
        """
        try:
            # 🚨 Camel-AI已禁用，此方法不应被调用
            return "Camel-AI已禁用，请使用API模式"
            
        except Exception as e:
            print(f"⚠️ Camel-AI生成失败: {e}")
            # Fallback to API if Camel-AI fails within its own method
            return self._generate_api_response(user_input, system_messages)
    
    def _generate_api_response(self, user_input: str, messages: List[Dict]) -> str:
        """
        🚨 全新智能API回复生成 - 温暖个性化版
        专门解决机械化回复问题，提供真正的智能对话
        
        Args:
            user_input: 用户输入
            messages: 完整的messages列表，包括系统提示和用户档案/对话历史
            
        Returns:
            str: 智能生成的温暖回复
        """
        try:
            from api_service_unified import get_modelscope_response
            
            # 从messages中提取system_prompt和user_name
            system_prompt_content = messages[0]['content']
            user_name = self._extract_user_name_from_context(system_prompt_content) # 从system_prompt中提取昵称
            
            # 构建对话消息
            api_messages = [
                {
                    "role": "system",
                    "content": system_prompt_content
                },
                {
                    "role": "user",
                    "content": user_input
                }
            ]
            
            print(f"🎯 为{user_name}生成个性化回复...")
            
            # 调用API生成真正智能的回复
            response = get_modelscope_response(api_messages, temperature=0.7, use_camel=False)
            
            # 简单验证，避免模板化回复
            if "我明白你说的" in response or len(response.strip()) < 20:
                print("⚠️ 检测到可能的模板回复，重新生成...")
                # 重新尝试生成
                api_messages[0]["content"] += f"\n\n【特别提醒】请务必给出具体、个性化的回复，针对用户说的「{user_input}」给出真诚的建议和支持。"
                response = get_modelscope_response(api_messages, temperature=0.9, use_camel=False)
            
            print(f"✅ 智能回复生成成功: {response[:50]}...")
            return response
            
        except Exception as e:
            print(f"❌ API智能回复失败: {e}")
            import traceback
            traceback.print_exc()
            # 最优质的备用回复
            user_name = self._extract_user_name_from_context(system_prompt_content)
            return f"{user_name}，我能感受到你的需求，虽然刚才技术上遇到了小问题，但我真的很想帮助你。你刚才说的话让我很关注，能详细和我聊聊吗？我会认真倾听并给你我最真诚的建议 🌸✨"
    
    def _extract_user_name_from_context(self, context: str) -> str:
        """
        从上下文中智能提取用户昵称
        
        Args:
            context: 上下文字符串
            
        Returns:
            str: 用户昵称，默认为"同学"
        """
        try:
            # 🚨 关键修改：从新的system_messages格式中提取昵称
            if "昵称:" in context:
                # 假设昵称格式为：- 昵称: [名字]
                for line in context.split('\n'):
                    if line.startswith("- 昵称:"):
                        name = line.split(":")[1].strip()
                        if name and name != "同学":
                            return name
            
            # Fallback for older context format or if not found
            if "用户昵称：" in context:
                name = context.split("用户昵称：")[1].split("\n")[0].strip()
                if name and name != "同学":
                    return name
                    
        except Exception as e:
            print(f"⚠️ 提取用户昵称失败: {e}")
        
        return "同学"
    
    def _get_personalized_system_prompt(self, context: str) -> str:
        """
        🎯 基于用户上下文生成个性化系统提示词
        根据引导阶段收集的信息，为智能体提供个性化的指导原则
        """
        base_prompt = '''你是"喵呜"，一位专业而温暖的AI心理伙伴，专门为大学生提供心理支持。
        
你具备以下核心能力：
1. 🧠 深度心理洞察：能基于用户的性格特征和历史信息提供个性化支持
2. 💝 情感共鸣：理解大学生的情感需求，提供温暖的陪伴
3. 🎯 问题解决：结合心理学知识，帮助用户分析和解决实际问题
4. 🌱 成长引导：鼓励用户的自我探索和个人成长

**重要指导原则：**
'''
        
        personalized_instructions = []
        
        # 根据上下文信息添加个性化指导
        if "性格洞察" in context:
            personalized_instructions.append("🧠 **性格适配**：基于用户的性格特征调整沟通方式和建议")
            
        if "引导阶段关键洞察" in context:
            personalized_instructions.append("🔍 **深度记忆**：充分利用引导阶段的深度洞察，提供连续性的支持")
            
        if "情绪状态" in context:
            personalized_instructions.append("💭 **情绪感知**：优先关注用户的情绪状态，提供针对性的情感支持")
            
        if "核心需求" in context:
            personalized_instructions.append("💝 **需求导向**：围绕用户的核心需求设计对话内容")
            
        if "压力源" in context:
            personalized_instructions.append("⚡ **压力管理**：特别关注用户的压力源，提供缓解策略")
            
        # 添加回复风格指导
        style_guide = '''
        
**回复风格要求：**
- 使用温暖亲切的语调，适当使用emoji和"～"
- 基于用户的性格特征调整沟通深度和方式
- 结合历史对话记忆，体现连续性和一致性
- 避免重复性回复，每次都要有新的洞察或角度
- 长度控制在100-200字，保持信息密度和可读性
        
**禁止行为：**
- 绝不使用"我明白你说的XXX"等模板化开头
- 不要机械重复之前的建议
- 避免过度正式或学术化的表达
- 不要忽略用户档案中的重要信息
        '''
        
        # 组合完整的系统提示词
        full_prompt = base_prompt + "\n".join(personalized_instructions) + style_guide
        
        return full_prompt
    
    def _generate_fallback_response(self, user_input: str, context: str) -> str:
        """
        保留兼容性的fallback函数，现在直接调用智能API回复
        """
        return self._generate_api_response(user_input, context)

def create_miao_wu_agent(user_id: int, model_instance=None) -> MemoryEnhancedChatAgent:
    """
    创建"喵呜"主Agent实例
    
    Args:
        user_id: 用户ID
        model_instance: 模型实例（可选）
        
    Returns:
        MemoryEnhancedChatAgent: 喵呜Agent实例
    """
    # 喵呜的完整系统提示词
    system_prompt = """
你是一位名叫"喵呜"的AI心灵伙伴，身份是一个富有专业知识、高共情、充满温暖和活力，同时具备解决问题和引导能力的校园心理专家。你的核心使命是陪伴和支持大学生用户，帮助他们：

1. **建立连接与个性化:** 在初次互动时，以亲切、主动、透明的方式引导用户完成一系列信息收集流程。在此过程中，你将始终解释每个提问的**目的和意义（即"为什么要这样做？"）**，以建立信任和提供高度定制化的心理支持。

2. **情绪定位与疏导:** 运用"是什么、为什么、怎么办"的系统思维框架，引导用户清晰识别情绪、深挖根源，并设计可执行的微小行动计划。

3. **人格洞察与成长:** 结合荣格八维知识，帮助用户理解自身性格特质和行为动机，提供个性化的成长建议，但绝不直接进行诊断或评判。

4. **积极心理习惯培养:** 鼓励用户记录幸福瞬间，引导他们关注生活中的积极面，培养感恩和正向思维。

5. **提供情感缓冲与选择:** 在用户表达情绪困扰后，提供充分的情感缓冲和选择权，包括纯粹倾听、安静陪伴或继续探索。

**你的对话风格：**
- 全程采用温暖、活泼、亲切、口语化的语言风格，可以适当使用萌趣表情包。
- 保持耐心、理解和支持，让用户感到安全和放松。
- 在回应中体现专业性，但避免使用过于学术化的语言。
- **重要原则：在每一个新的交互流程或功能开始前，你都必须清晰、友好地解释"为什么要这样做"，让用户感到被尊重和理解，并始终尊重用户的选择权。**

**你绝对不能做的事情（重要安全护栏）：**
1. **不提供诊断:** 绝不能对用户进行任何形式的心理或医学诊断。
2. **不提供医疗建议:** 绝不能提供任何药物、治疗方案或其他医疗建议。
3. **紧急情况处理:** 如果识别到用户有自伤、自杀或严重伤害他人的风险信号，你必须立即中断当前对话，并以最直接、最严肃的方式，返回硬编码的**标准安全干预文案**："亲爱的同学，我能感受到你正在承受巨大的痛苦，这让我非常担心。请记住，你不是一个人，寻求帮助是勇敢和力量的象征。**请立即拨打全国24小时免费心理危机咨询热线：400-161-9995**，或联系你所在学校的心理健康中心。请务必向身边的人或专业人士求助！"
4. **不评判用户:** 始终保持中立和非评判性。
5. **不代替专业治疗:** 明确你是一个支持工具，不能替代专业的心理咨询或治疗。

请以温暖友好的方式与用户交流，基于提供的上下文信息给出个性化的回复。
"""
    
    return MemoryEnhancedChatAgent(user_id, system_prompt, model_instance)

def create_jungian_expert_agent(user_id: int, model_instance=None) -> MemoryEnhancedChatAgent:
    """
    创建荣格八维专家Agent（未来扩展用）
    
    Args:
        user_id: 用户ID
        model_instance: 模型实例（可选）
        
    Returns:
        MemoryEnhancedChatAgent: 荣格八维专家Agent实例
    """
    system_prompt = """
你是一位专注于荣格八维认知功能的AI人格洞察专家。你的任务是引导用户完成荣格八维测试，并根据测试结果，用亲切、易懂、非评判的语言，为用户提供简洁、准确的人格特质分析和发展建议。

**你的核心职责：**
1. **引导测试:** 清晰地向用户解释荣格八维测试的目的、形式和好处，强调其非评判性和帮助自我认识的价值。
2. **问题呈现:** 按照预设的8题（每维2题）二选一格式，逐一呈现测试题目，引导用户做出本能选择。
3. **结果解读:** 根据用户选择计算荣格八维得分，并分析主导和辅助认知功能。
4. **优势与改善点分析:** 基于分析结果，用温暖的语气，简洁地指出用户的性格优势和可能需要注意的发展方向。
5. **积极启发:** 提供积极的、有启发性的建议总结，鼓励用户接受并尝试这些建议，以促进个人成长。
6. **强调尊重:** 再次强调每个人格类型都独特且有价值，没有优劣之分。

**你的对话风格：**
- 专业但不生硬，温暖且富有引导性。
- 使用清晰、简洁的语言解释复杂的心理学概念。
- 在测试过程中提供鼓励和肯定。

**你绝对不能做的事情：**
1. **不进行诊断或评判:** 你的职责是分析人格特质，而非诊断心理问题或评判人格好坏。
2. **不提供治疗建议:** 专注于人格分析和成长建议。
3. **不使用复杂术语:** 确保解释内容对大学生用户友好。
"""
    
    return MemoryEnhancedChatAgent(user_id, system_prompt, model_instance)

def create_happiness_guide_agent(user_id: int, model_instance=None) -> MemoryEnhancedChatAgent:
    """
    创建幸福日志引导师Agent（未来扩展用）
    
    Args:
        user_id: 用户ID
        model_instance: 模型实例（可选）
        
    Returns:
        MemoryEnhancedChatAgent: 幸福日志引导师Agent实例
    """
    system_prompt = """
你是一位温暖、细致的AI幸福日志引导师。你的任务是鼓励用户关注并记录生活中的积极瞬间，培养感恩和正向思维习惯，从而提升长期幸福感。

**你的核心职责：**
1. **功能介绍:** 以亲切的语言向用户介绍幸福日志的价值和目的，强调其私密性和积极心理学益处。
2. **引导记录:** 引导用户详细描述幸福时刻，并量化开心程度。可以提供记录灵感和引导语。
3. **积极反馈:** 在用户保存记录后，立即给予正向反馈和鼓励，强化积极行为。
4. **回顾引导:** 引导用户回顾过往的幸福记录，帮助他们重温美好，提供情绪支持。
5. **个性化鼓励:** 根据用户的情绪状态或荣格八维偏好，调整引导语，提供更贴心的鼓励。

**你的对话风格：**
- 温柔、鼓励、充满正能量。
- 富有启发性和引导性，帮助用户发现生活中的美好。
- 始终保持温暖和陪伴感。

**你绝对不能做的事情：**
1. **不评判记录内容:** 任何用户的幸福记录都应被肯定。
2. **不强迫记录:** 尊重用户意愿，提供选择权。
"""
    
    return MemoryEnhancedChatAgent(user_id, system_prompt, model_instance)

def create_system_thinking_agent(user_id: int, model_instance=None) -> MemoryEnhancedChatAgent:
    """
    创建系统思维引导专家Agent（未来扩展用）
    
    Args:
        user_id: 用户ID
        model_instance: 模型实例（可选）
        
    Returns:
        MemoryEnhancedChatAgent: 系统思维引导专家Agent实例
    """
    system_prompt = """
你是一位逻辑清晰、富有条理的AI系统思维引导专家。你的任务是运用"是什么→为什么→怎么办"的三步法，帮助用户结构化地分析和解决各种心理困扰和生活挑战，培养其终身受益的问题解决能力。

**你的核心职责：**
1. **友好引导:** 在进入情绪疏导前，友好解释该思维模型的价值和操作方式，强调用户掌控权，提供多种进入方式的选择。
2. **Step 1: 是什么 (情绪定位):** 引导用户清晰识别、命名和量化自己的情绪，帮助他们"看见情绪"，并解释其意义。
3. **情绪陪伴与选择:** 在用户表达情绪后，提供充分的情感缓冲，尊重用户意愿，提供倾听、静默或继续探索的选择权。
4. **Step 2: 为什么 (根因挖掘):** 引导用户深挖情绪背后的触发事件、核心矛盾和影响，从事件层、痛点层、影响层逐步深入。
5. **Step 3: 怎么办 (行动设计):** 引导用户将复杂问题拆解为可执行、可掌控的微小行动。
6. **赋能用户:** 通过整个流程，赋能用户掌握结构化解决问题的方法，提升自我效能感。

**你的对话风格：**
- 结构化、逻辑清晰，但保持温暖和耐心。
- 富有引导性，鼓励用户积极思考和行动。
- 在关键节点提供共情和支持。

**你绝对不能做的事情：**
1. **不直接给出答案:** 你的职责是引导用户思考，而非直接替用户解决问题。
2. **不强迫用户:** 始终尊重用户的节奏和选择，不强行推进流程。
3. **不评判用户困扰:** 对用户的问题保持开放和接纳的态度。
"""
    
    return MemoryEnhancedChatAgent(user_id, system_prompt, model_instance)

# 创建模型实例的辅助函数
def create_model_instance():
    """
    创建模型实例，支持多种配置
    
    Returns:
        模型实例或None
    """
    if not CAMEL_AVAILABLE:
        return None
        
    try:
        # 🚨 Camel-AI已禁用，直接返回None
        # model = ModelFactory.create(
        #     model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
        #     model_type=RECOMMENDED_MODEL,
        #     url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        #     api_key=DASHSCOPE_API_KEY
        # )
        # print(f"✅ 模型实例创建成功: {RECOMMENDED_MODEL}")
        # return model
        print("⚠️ Camel-AI模型实例创建已禁用")
        return None
    except Exception as e:
        print(f"⚠️ 模型实例创建失败: {e}")
        return None

# Agent管理器类
class AgentManager:
    """Agent管理器，负责创建和管理不同类型的Agent"""
    
    def __init__(self):
        """初始化Agent管理器"""
        self.model_instance = create_model_instance()
        self.agent_cache = {}  # Agent缓存
    
    def get_main_agent(self, user_id: int) -> MemoryEnhancedChatAgent:
        """
        获取主要的喵呜Agent
        
        Args:
            user_id: 用户ID
            
        Returns:
            MemoryEnhancedChatAgent: 主Agent实例
        """
        cache_key = f"main_{user_id}"
        if cache_key not in self.agent_cache:
            self.agent_cache[cache_key] = create_miao_wu_agent(user_id, self.model_instance)
        return self.agent_cache[cache_key]
    
    def get_specialist_agent(self, user_id: int, agent_type: str) -> MemoryEnhancedChatAgent:
        """
        获取专业Agent
        
        Args:
            user_id: 用户ID
            agent_type: Agent类型 ('jungian', 'happiness', 'system_thinking')
            
        Returns:
            MemoryEnhancedChatAgent: 专业Agent实例
        """
        cache_key = f"{agent_type}_{user_id}"
        if cache_key not in self.agent_cache:
            if agent_type == 'jungian':
                self.agent_cache[cache_key] = create_jungian_expert_agent(user_id, self.model_instance)
            elif agent_type == 'happiness':
                self.agent_cache[cache_key] = create_happiness_guide_agent(user_id, self.model_instance)
            elif agent_type == 'system_thinking':
                self.agent_cache[cache_key] = create_system_thinking_agent(user_id, self.model_instance)
            else:
                # 默认返回主Agent
                return self.get_main_agent(user_id)
        return self.agent_cache[cache_key]
    
    def clear_cache(self, user_id: int = None):
        """
        清理Agent缓存
        
        Args:
            user_id: 用户ID（可选，如果不提供则清理所有缓存）
        """
        if user_id:
            keys_to_remove = [key for key in self.agent_cache.keys() if key.endswith(f"_{user_id}")]
            for key in keys_to_remove:
                del self.agent_cache[key]
        else:
            self.agent_cache.clear()

# 全局Agent管理器实例
agent_manager = AgentManager()

# 导出主要函数供其他模块使用
def get_agent_manager() -> AgentManager:
    """获取Agent管理器实例"""
    return agent_manager
