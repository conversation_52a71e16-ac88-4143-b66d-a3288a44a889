"""
八维认知功能专业内容数据库
===============================

基于荣格理论的专业八维认知功能详细解释
包含趣味比喻和实用发展建议，确保内容专业且易懂

设计原则：
1. 专业性：基于认知功能理论，确保理论准确性
2. 趣味性：使用生动比喻，让抽象概念具体化
3. 实用性：提供具体的发展建议和行动指导
4. 个性化：针对每个功能的独特特点

Version: v1.0
Author: 心理Agent团队 + Claude助手
"""

# ========================================================================
# 📚 八维认知功能详细内容数据库
# ========================================================================

COGNITIVE_FUNCTIONS_CONTENT = {
    "Si": {
        "name": "内向实感",
        "english_name": "Introverted Sensing", 
        "definition": "专注于过去的具体经验，通过详细、可靠的内部数据库来理解和构建世界。",
        "analogy": "像一位**'记忆宫殿的图书管理员'**，他把你亲身经历的所有细节、感受和事实都精心归档，需要时总能精准地调出档案。",
        "strengths": "你值得信赖，注重稳定和程序，拥有惊人的细节记忆力，能从过去的经验中汲取智慧。",
        "development_tips": "尝试主动体验一些全新的、不可预测的活动（比如一次即兴的短途旅行），为你的'记忆宫殿'增添一些新奇的馆藏。",
        "emoji": "📚",
        "color": "#8B4513"
    },
    
    "Se": {
        "name": "外向实感",
        "english_name": "Extraverted Sensing",
        "definition": "活在当下，全身心投入物理世界，通过五官直接感知和体验周围环境。",
        "analogy": "像一位**'顶级极限运动员'**，他渴望与现实世界进行最直接、最刺激的互动，享受每一个瞬间的感官冲击。",
        "strengths": "你充满活力、适应力强、行动力超群，能敏锐地抓住眼前的机会，并享受生活中的美与乐。",
        "development_tips": "试着在行动之余，留出一点时间给'未来'，花5分钟简单规划下明天或下周，让你的行动更具方向感。",
        "emoji": "🏃‍♂️",
        "color": "#FF6B35"
    },
    
    "Ni": {
        "name": "内向直觉",
        "english_name": "Introverted Intuition",
        "definition": "聚焦于未来的可能性和深层含义，通过洞察事物背后的模式和联系来理解世界。",
        "analogy": "像一位**'深海潜航的探险家'**，他能潜入意识的深海，将看似不相关的想法和线索串联起来，最终发现隐藏在海底的宏伟蓝图。",
        "strengths": "你富有远见和洞察力，善于进行长期战略思考，能看到别人看不到的未来趋势和可能性。",
        "development_tips": "尝试将你的宏大构想，用最简单的语言或图画分享给一个朋友。这能帮你把'深海的宝藏'更好地带回'陆地'。",
        "emoji": "🔮",
        "color": "#6A4C93"
    },
    
    "Ne": {
        "name": "外向直觉",
        "english_name": "Extraverted Intuition",
        "definition": "对外部世界的新奇可能性和创意联系保持开放，像探索网一样向外发散。",
        "analogy": "像一位**'思维的超级蜘蛛侠'**，他能在不同的概念、想法和可能性之间发射'点子蛛丝'，瞬间编织出一张充满创意的灵感大网。",
        "strengths": "你极富创造力、好奇心和头脑风暴能力，总能从平凡中发现新奇，为团队带来无限可能。",
        "development_tips": "在你迸发的众多灵感中，尝试挑选一个你最心动的，并为它制定一个微小的、能在24小时内完成的行动步骤。",
        "emoji": "💡",
        "color": "#FFD93D"
    },
    
    "Ti": {
        "name": "内向思考",
        "english_name": "Introverted Thinking",
        "definition": "追求精确和逻辑自洽，通过构建内在的、严谨的个人理论框架来做决策。",
        "analogy": "像一位**'精密的机械钟表匠'**，他会把你接收到的所有信息都拆解成最小的零件，仔细检查每个齿轮是否吻合，以确保整个系统精准无误、逻辑自洽。",
        "strengths": "你逻辑严谨、分析能力强、追求真理，能快速发现系统中的矛盾和漏洞。",
        "development_tips": "尝试向他人解释一个你深思熟虑过的复杂概念时，多用一些情感化的比喻和故事，而不仅仅是逻辑推导。",
        "emoji": "⚙️",
        "color": "#4A90E2"
    },
    
    "Te": {
        "name": "外向思考",
        "english_name": "Extraverted Thinking", 
        "definition": "致力于组织和规划外部世界，以最高效、最有序的方式达成目标。",
        "analogy": "像一位**'雷厉风行的总指挥官'**，他擅长制定清晰的作战计划，分配任务，调度资源，并监督整个流程，确保'战役'能按时、高效地取得胜利。",
        "strengths": "你目标导向、执行力强、天生高效，是出色的组织者和领导者。",
        "development_tips": "在推进任务时，偶尔停下来问问团队成员的感受，或者花点时间庆祝一个小的阶段性胜利，这会让你的领导更具温度。",
        "emoji": "⚡",
        "color": "#E74C3C"
    },
    
    "Fi": {
        "name": "内向情感",
        "english_name": "Introverted Feeling",
        "definition": "依据内心深处的价值观和个人情感来做决策，追求真实和内外和谐。",
        "analogy": "像一位**'内心的指南针守护者'**，无论外界如何变化，他始终校准着你内心最核心的价值观和道德方向，确保你走的每一步都'忠于自己'。",
        "strengths": "你真诚、有同理心、信念坚定，能深刻地理解自己和他人的情感，并坚守自己的原则。",
        "development_tips": "尝试将你内心坚信的一个价值观，通过一个具体的、帮助他人的小行动表达出来，让你的信念在现实世界中开花。",
        "emoji": "💖",
        "color": "#FF69B4"
    },
    
    "Fe": {
        "name": "外向情感",
        "english_name": "Extraverted Feeling",
        "definition": "关注他人的感受和需求，致力于在外部环境中建立和维护和谐的人际关系。",
        "analogy": "像一位**'温暖的社交场调音师'**，他能敏锐地感知到环境中的人际氛围，并主动调整'音量'和'音调'，让每个人都感到舒适与和谐。",
        "strengths": "你有魅力、擅长交际、关怀他人，是维系团队和谐、照顾大家情绪的天然粘合剂。",
        "development_tips": "在关心他人需求的同时，也要记得每天留出15分钟的'自我关怀'时间，问问自己：'今天，我需要什么？'",
        "emoji": "🤗",
        "color": "#2ECC71"
    }
}

# ========================================================================
# 🎯 MBTI类型与八维功能对应关系
# ========================================================================

MBTI_COGNITIVE_STACK = {
    "INTJ": ["Ni", "Te", "Fi", "Se"],  # 主导、辅助、第三、劣势功能
    "INTP": ["Ti", "Ne", "Si", "Fe"],
    "ENTJ": ["Te", "Ni", "Se", "Fi"],
    "ENTP": ["Ne", "Ti", "Fe", "Si"],
    "INFJ": ["Ni", "Fe", "Ti", "Se"],
    "INFP": ["Fi", "Ne", "Si", "Te"],
    "ENFJ": ["Fe", "Ni", "Se", "Ti"],
    "ENFP": ["Ne", "Fi", "Te", "Si"],
    "ISTJ": ["Si", "Te", "Fi", "Ne"],
    "ISFJ": ["Si", "Fe", "Ti", "Ne"],
    "ESTJ": ["Te", "Si", "Ne", "Fi"],
    "ESFJ": ["Fe", "Si", "Ne", "Ti"],
    "ISTP": ["Ti", "Se", "Ni", "Fe"],
    "ISFP": ["Fi", "Se", "Ni", "Te"],
    "ESTP": ["Se", "Ti", "Fe", "Ni"],
    "ESFP": ["Se", "Fi", "Te", "Ni"]
}

# ========================================================================
# 🔧 辅助函数
# ========================================================================

def get_cognitive_function_content(function_code: str) -> dict:
    """
    获取指定认知功能的详细内容
    
    Args:
        function_code (str): 功能代码，如 "Ni", "Te" 等
        
    Returns:
        dict: 包含该功能完整信息的字典，如果功能不存在则返回空字典
    """
    return COGNITIVE_FUNCTIONS_CONTENT.get(function_code, {})

def get_user_cognitive_stack(mbti_type: str) -> list:
    """
    根据MBTI类型获取用户的认知功能栈
    
    Args:
        mbti_type (str): MBTI类型，如 "INTJ"
        
    Returns:
        list: 按主导、辅助、第三、劣势顺序排列的功能列表
    """
    return MBTI_COGNITIVE_STACK.get(mbti_type.upper(), [])

def format_cognitive_function_card(function_code: str) -> str:
    """
    格式化单个认知功能为精美的卡片展示
    
    Args:
        function_code (str): 功能代码
        
    Returns:
        str: 格式化后的Markdown卡片内容
    """
    content = get_cognitive_function_content(function_code)
    if not content:
        return f"抱歉，找不到 {function_code} 功能的信息。"
    
    return f"""## {content['emoji']} **{content['name']} ({function_code}) - {content['english_name']}**

### 🔍 **核心定义**
{content['definition']}

### 🎭 **趣味比喻**
{content['analogy']}

### ✨ **你的天赋**
{content['strengths']}

### 🌱 **发展贴士**
{content['development_tips']}

---
*每一种认知功能都是珍贵的天赋，没有高低之分，只有不同的精彩！* 💫"""

def format_user_cognitive_analysis(mbti_type: str) -> str:
    """
    为特定用户生成个性化的认知功能分析
    
    Args:
        mbti_type (str): 用户的MBTI类型
        
    Returns:
        str: 格式化的个性化分析内容
    """
    cognitive_stack = get_user_cognitive_stack(mbti_type)
    if not cognitive_stack:
        return "抱歉，无法获取你的认知功能信息。"
    
    # 获取主导和辅助功能的信息
    dominant = get_cognitive_function_content(cognitive_stack[0])
    auxiliary = get_cognitive_function_content(cognitive_stack[1])
    
    return f"""## 🌟 **你的专属认知功能组合 - {mbti_type}**

作为 **{mbti_type}** 类型，你的内在'认知引擎'是这样工作的：

### 🎯 **主导功能：{dominant['emoji']} {dominant['name']} ({cognitive_stack[0]})**
这是你的'超级英雄技能'！{dominant['analogy']}

### 🤝 **辅助功能：{auxiliary['emoji']} {auxiliary['name']} ({cognitive_stack[1]})**  
这是你的'最佳拍档'！{auxiliary['analogy']}

### 💡 **你的独特优势**
- **{dominant['name']}** 让你擅长：{dominant['strengths']}
- **{auxiliary['name']}** 让你擅长：{auxiliary['strengths']}

### 🌱 **成长建议**
- 发展{dominant['name']}：{dominant['development_tips']}
- 平衡{auxiliary['name']}：{auxiliary['development_tips']}

---
*想了解你的第三和劣势功能吗？每一层认知功能都藏着你成长的密码！* ✨"""

def get_all_functions_overview() -> str:
    """
    生成所有八维功能的概览介绍
    
    Returns:
        str: 八维功能总览的Markdown内容
    """
    return """# 🧠 **荣格八维认知功能宝典**

欢迎来到心智探索的奇妙世界！这里收录了构成每个人独特个性的8种'心智精灵'。

## 📖 **什么是认知功能？**
认知功能是荣格心理学理论的核心概念，描述了我们的大脑如何**感知信息**、**处理决策**和**与世界互动**的不同方式。每个人都拥有这8种功能，但使用的偏好和熟练度不同，这构成了我们独特的个性。

## 🎭 **四对认知功能**

### 🌊 **感知功能** - 你如何获取信息
- **Si (内向实感)** vs **Se (外向实感)** 
- **Ni (内向直觉)** vs **Ne (外向直觉)**

### ⚖️ **判断功能** - 你如何做决策  
- **Ti (内向思考)** vs **Te (外向思考)**
- **Fi (内向情感)** vs **Fe (外向情感)**

## 🎯 **如何使用这个宝典？**
点击下方任意一个功能卡片，深入了解它的'超能力'！每个功能都有独特的天赋和发展建议，帮你更好地认识自己，发挥潜能。

**记住：没有好坏之分，只有不同的精彩！** 🌈"""