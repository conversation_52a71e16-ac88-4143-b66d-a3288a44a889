"""
大学生心理小助手"喵呜" - 魔搭部署版（清理版）
====================================

保持原版所有优秀功能的魔搭平台专用版本
包含完整的对话流程、荣格八维测试、危机检测等功能

核心功能（与原版完全相同）：
1. 友好的喵呜了解用户
2. 专业的荣格八维性格测试（8题深度版） 
3. 结构化的情绪疏导（是什么→为什么→怎么办）
4. 积极的幸福日志功能
5. 个性化的日常陪伴聊天
6. 双重危机检测机制
7. ChatGPT风格界面布局
8. 阳光明日香风格UI

版本: ModelScope v1.1 (功能完整版 - 样式分离版)
作者: AI产品经理新手 + Claude助手
"""

import gradio as gr
import socket
from contextlib import closing

# 导入配置
from config_modelscope import (
    APP_TITLE, 
    APP_DESCRIPTION, 
    CHAT_HEIGHT, 
    SERVER_HOST, 
    SERVER_PORT,
    SERVER_SHARE, 
    SERVER_DEBUG, 
    SERVER_SHOW_ERROR, 
    AUTO_FIND_PORT,
    PORT_RANGE_START,
    PORT_RANGE_END,
    RECENT_CONVERSATION_LIMIT # 🚨 新增：导入对话记忆限制
)

# 导入核心模块
from conversation_flow_modelscope import ConversationManager, process_conversation

# 🐪 Camel-AI优化模块 - 数据库、状态机、智能Agent
from database_manager import get_database_manager
from state_manager import get_state_manager, ConversationState, get_conversation_state_from_string
from agents import get_agent_manager

# 🐱 喵呜社区模块
from community_manager import get_community_manager

# 统一API服务 - 完全使用新的融合版本
from api_service_unified import (
    get_modelscope_response, 
    emergency_check, 
    generate_comfort,
    get_service_status,
    get_usage_stats
)
# 🎨 导入样式模块
import styles

print("🚀 统一API服务已启用，融合Camel-AI增强与传统API优点")

# 初始化Camel-AI优化组件
print("🔍 [APP] 开始初始化数据库管理器...")
try:
    db_manager = get_database_manager()
    print("✅ [APP] 数据库管理器初始化成功。")
except Exception as e:
    print(f"❌ [APP] 数据库管理器初始化失败: {e}")
    import traceback
    traceback.print_exc()
    # 可以在这里选择退出或使用降级方案

print("🔍 [APP] 开始初始化状态管理器...")
try:
    state_manager = get_state_manager()
    print("✅ [APP] 状态管理器初始化成功。")
except Exception as e:
    print(f"❌ [APP] 状态管理器初始化失败: {e}")
    import traceback
    traceback.print_exc()

print("🔍 [APP] 开始初始化Agent管理器...")
try:
    agent_manager = get_agent_manager()
    print("✅ [APP] Agent管理器初始化成功。")
except Exception as e:
    print(f"❌ [APP] Agent管理器初始化失败: {e}")
    import traceback
    traceback.print_exc()

print("🚀 Camel-AI优化组件已初始化：数据库持久化 + 状态管理 + 智能Agent")

# 🚀 性能优化：延迟初始化社区管理器
community_manager = None  # 延迟初始化，在需要时才加载

print("🐱 喵呜社区管理器准备就绪")

# 导入八维认知功能模块
try:
    from cognitive_functions_content import (
        get_all_functions_overview, 
        COGNITIVE_FUNCTIONS_CONTENT,
        format_cognitive_function_card
    )
    COGNITIVE_FUNCTIONS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 八维认知功能模块导入失败: {e}")
    COGNITIVE_FUNCTIONS_AVAILABLE = False

# 导入幸福日志所需模块
import json
import os
from datetime import datetime, date, timedelta
import random
from collections import defaultdict
import calendar
import pandas as pd

# 确保幸福日志数据目录存在
LOG_DIR = "data"
LOG_FILE = os.path.join(LOG_DIR, "happiness_logs.json")

if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

# 猫咪风格的提示语
CAT_PROMPTS = [
    "喵~ 今天有什么让你开心的事情呀？😺",
    "呼噜呼噜~ 记录一下今天的小确幸吧~ 🐾",
    "喵呜~ 今天有没有什么温暖的瞬间？😻",
    "咪~ 把开心的事情记下来，就像藏小鱼干一样安全~ 🐟",
    "喵~ 分享一下今天的快乐，让我也开心开心~ 😽"
]

# 按开心程度分类的猫咪回应
CAT_RESPONSES = {
    5: [
        "哇！超开心的吗？太棒啦！我也跟着高兴起来了！😻",
        "喵~ 听到你这么开心，我也忍不住想蹭蹭你呢~ 🐾",
        "太好啦！这种感觉就像找到了一大箱小鱼干！😺",
        "呼噜呼噜~ 能感受到你的快乐都要溢出来啦！😊"
    ],
    4: [
        "喵~ 很开心呢！真好呀~ 😺",
        "听到你开心我也很高兴，给你一个猫咪赞~ 👍",
        "不错不错，这种开心的感觉要好好记住呀~ 😊",
        "咪~ 为你感到开心，今天真是美好的一天~ 🐾"
    ],
    3: [
        "喵~ 还不错呀，平淡中的小幸福也很珍贵呢~ 😊",
        "这样就很好啦，每天有点小开心就足够了~ 😺",
        "呼噜~ 平静的幸福也很温暖呢~ 🐾",
        "嗯嗯，还不错就是最好的状态呀~ 😌"
    ],
    2: [
        "喵~ 一般般吗？没关系，明天也许会更好呢~ 😐",
        "别担心，总会有开心的事情在等着你的~ 😺",
        "没关系呀，有时候平淡也是一种幸福呢~ 🐾",
        "咪~ 也许下一件事就能让你更开心一点哦~ 😊"
    ],
    1: [
        "喵呜~ 有点低落吗？要不要摸摸猫咪开心一下？😿",
        "别难过呀，我会一直陪着你的~ 🐾",
        "没关系，坏情绪总会过去的，明天会更好~ 😺",
        "摸摸头~ 一切都会好起来的，相信我~ 😊"
    ]
}

def load_logs():
    """加载已保存的幸福日志"""
    if os.path.exists(LOG_FILE):
        with open(LOG_FILE, "r", encoding="utf-8") as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                return []
    return []

def save_logs(logs):
    """保存幸福日志到文件"""
    with open(LOG_FILE, "w", encoding="utf-8") as f:
        json.dump(logs, f, ensure_ascii=False, indent=2)

def group_logs_by_date(logs):
    """按日期分组日志"""
    grouped = defaultdict(list)
    for entry in logs:
        grouped[entry["date"]].append(entry)
    # 按时间排序每个日期的记录
    for date in grouped:
        grouped[date].sort(key=lambda x: x["time"], reverse=True)
    return grouped

def get_dates_with_entries():
    """获取所有有记录的日期，用于在日历上标记"""
    global current_user_id
    if current_user_id is None:
        return []
    
    try:
        # 🚨 修复：从数据库获取幸福日志日期
        happiness_logs = db_manager.get_happiness_logs(current_user_id)
        dates = []
        for log in happiness_logs:
            # 转换日期格式：从YYYY-MM-DD到YYYY年MM月DD日
            date_obj = datetime.strptime(log['date'], '%Y-%m-%d')
            formatted_date = date_obj.strftime('%Y年%m月%d日')
            dates.append(formatted_date)
        return dates
    except Exception as e:
        print(f"⚠️ 获取幸福日志日期失败: {e}")
        # 降级到文件存储
        logs = load_logs()
        grouped = group_logs_by_date(logs)
        return list(grouped.keys())

def generate_calendar_data(year=None, month=None):
    """生成日历数据"""
    if year is None:
        year = datetime.now().year
    if month is None:
        month = datetime.now().month
    
    # 获取有记录的日期
    marked_dates = get_dates_with_entries()
    
    # 创建日历
    cal = calendar.monthcalendar(year, month)
    calendar_data = []
    
    # 星期几的标题
    weekdays = ["日", "一", "二", "三", "四", "五", "六"]
    
    # 添加星期几标题行
    calendar_data.append(weekdays)
    
    # 添加日期行
    for week in cal:
        week_data = []
        for day in week:
            if day == 0:
                week_data.append("")
            else:
                date_str = f"{year}年{month:02d}月{day:02d}日"
                if date_str in marked_dates:
                    week_data.append(f"{day}🐾")
                else:
                    week_data.append(str(day))
        calendar_data.append(week_data)
    
    return calendar_data

def get_prev_month(year, month):
    """获取上一个月的年月"""
    if month == 1:
        return year - 1, 12
    return year, month - 1

def get_next_month(year, month):
    """获取下一个月的年月"""
    if month == 12:
        return year + 1, 1
    return year, month + 1

def update_calendar_display(year, month):
    """更新日历视图"""
    calendar_data = generate_calendar_data(year, month)
    month_name = calendar.month_name[month]
    title = f"{year}年 {month_name}"
    return calendar_data, year, month, title

def show_date_entries(selected_date):
    """显示选定日期的记录"""
    global current_user_id
    if not selected_date or selected_date == "未选择日期":
        return "喵~ 请从日历上选择一个日期查看记录哦~ 😺"
    
    if current_user_id is None:
        return "喵~ 请先登录才能查看记录哦~ 😺"
    
    try:
        # 🚨 修复：从数据库获取指定日期的幸福日志
        # 将日期格式从"YYYY年MM月DD日"转换为"YYYY-MM-DD"
        try:
            date_obj = datetime.strptime(selected_date, '%Y年%m月%d日')
            db_date = date_obj.strftime('%Y-%m-%d')
        except ValueError:
            # 如果格式不匹配，尝试其他格式
            db_date = selected_date
        
        happiness_logs = db_manager.get_happiness_logs(current_user_id, start_date=db_date, end_date=db_date)
        
        if happiness_logs:
            formatted = f"🐾 {selected_date} 的幸福记录 🐾\n\n"
            
            for i, entry in enumerate(happiness_logs, 1):
                level = entry.get('level', 3)
                if level == 5:
                    mood_emoji = "😻 超开心！"
                elif level == 4:
                    mood_emoji = "😺 很开心"
                elif level == 3:
                    mood_emoji = "😊 还不错"
                elif level == 2:
                    mood_emoji = "😐 一般般"
                else:
                    mood_emoji = "😿 有点低落"
                
                # 从timestamp中提取时间
                timestamp = entry.get('timestamp', '')
                if timestamp:
                    try:
                        time_obj = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        time_str = time_obj.strftime('%H:%M')
                    except:
                        time_str = "未知时间"
                else:
                    time_str = "未知时间"
                    
                formatted += f"【{time_str}】{mood_emoji}\n"
                formatted += f"{entry['content']}\n\n"

                if i < len(happiness_logs):
                    formatted += "---\n\n"
            
            return formatted
        else:
            return f"喵~ {selected_date} 还没有记录呢~ 那天有什么开心的事可以补充记录呀~ 😿"
            
    except Exception as e:
        print(f"⚠️ 获取日期记录失败: {e}")
        # 降级到文件存储
        logs = load_logs()
        grouped = group_logs_by_date(logs)
        
        if selected_date in grouped:
            entries = grouped[selected_date]
            formatted = f"🐾 {selected_date} 的幸福记录 🐾\n\n"
            
            for i, entry in enumerate(entries, 1):
                if entry["mood"] == 5:
                    mood_emoji = "😻 超开心！"
                elif entry["mood"] == 4:
                    mood_emoji = "😺 很开心"
                elif entry["mood"] == 3:
                    mood_emoji = "😊 还不错"
                elif entry["mood"] == 2:
                    mood_emoji = "😐 一般般"
                else:
                    mood_emoji = "😿 有点低落"
                    
                formatted += f"【{entry['time']}】{mood_emoji}\n"
                formatted += f"{entry['text']}\n\n"

                if i < len(entries):
                    formatted += "---\n\n"
            
            return formatted
        
        return f"喵~ {selected_date} 还没有记录呢~ 那天有什么开心的事可以补充记录呀~ 😿"

def add_happiness_entry(happiness_text, mood_rating, selected_date_text):
    """添加新的幸福日志条目 - 性能优化版"""
    if not happiness_text.strip():
        return "喵~ 请告诉我一些开心的事情呀~ 😿", random.choice(CAT_PROMPTS), date.today().strftime("%Y-%m-%d")
    
    try:
        date_obj = datetime.strptime(selected_date_text, "%Y-%m-%d").date()
    except ValueError:
        date_obj = date.today()
        selected_date_text = date_obj.strftime("%Y-%m-%d")
    
    # 🐪 保存到数据库（优先）
    try:
        global current_user_id
        
        # 分析幸福来源
        source = "日常"
        if "（" in happiness_text and "）" in happiness_text:
            content, source_part = happiness_text.rsplit("（", 1)
            source = source_part.replace("）", "").strip()
            happiness_text = content.strip()
        
        db_manager.save_happiness_log(
            user_id=current_user_id,
            date=selected_date_text,
            content=happiness_text,
            level=mood_rating,
            source=source
        )
        print(f"✅ 幸福日志已保存到数据库 (用户ID: {current_user_id})")
    except Exception as e:
        print(f"⚠️ 幸福日志保存到数据库失败: {e}")
    
    # 兼容原有逻辑（文件存储作为备份）
    date_str = date_obj.strftime("%Y年%m月%d日")
    time_str = datetime.now().strftime("%H:%M")
    
    new_entry = {
        "date": date_str,
        "time": time_str,
        "text": happiness_text,
        "mood": mood_rating
    }
    
    current_logs = load_logs()
    current_logs.insert(0, new_entry)
    save_logs(current_logs)
    
    response = random.choice(CAT_RESPONSES[mood_rating])
    return response, random.choice(CAT_PROMPTS), date.today().strftime("%Y-%m-%d")

def format_all_happiness_logs():
    """格式化所有日志以便显示"""
    logs = load_logs()
    if not logs:
        return "喵~ 还没有记录呢，快来写下今天的幸福吧~ 😺"
    
    grouped = group_logs_by_date(logs)
    formatted = ""
    
    for date in sorted(grouped.keys(), reverse=True):
        formatted += f"📅 {date}\n"
        entries = grouped[date]
        
        for entry in entries:
            if entry["mood"] == 5:
                mood_emoji = "😻"
            elif entry["mood"] == 4:
                mood_emoji = "😺"
            elif entry["mood"] == 3:
                mood_emoji = "😊"
            elif entry["mood"] == 2:
                mood_emoji = "😐"
            else:
                mood_emoji = "😿"
                
            formatted += f"[{entry['time']}] {mood_emoji} {entry['text']}\n"
        
        formatted += "\n"
    
    return formatted

def clear_happiness_entries():
    """清空所有日志"""
    if os.path.exists(LOG_FILE):
        os.remove(LOG_FILE)
    return "喵~ 所有记录都清空啦~ 可以重新开始记录幸福啦~ 😺"

# ========================================================================
# 🔧 工具函数
# ========================================================================

def find_free_port():
    """
    查找一个当前未被占用的TCP端口，用于动态启动Gradio服务。
    """
    with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as s:
        s.bind(('', 0))
        s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        return s.getsockname()[1]

# ========================================================================
# 🎯 核心聊天机器人响应函数
# ========================================================================

# 全局对话管理器 - 兼容原有逻辑
conversation_manager = ConversationManager()

# 全局用户状态追踪（临时方案，等待Gradio集成）
current_user_id = None  # 将在启动时初始化
current_conversation_state = ConversationState.INITIAL_GREETING

# 🚀 性能优化：用户状态缓存
_user_cache = {}

def chatbot_response(message, history):
    """
    核心聊天机器人响应函数 - Camel-AI优化版
    
    这是整个应用的大脑，现在集成了：
    - 数据库持久化存储
    - 智能状态管理
    - Camel-AI Agent对话生成
    - 完整保持原有所有功能和流程
    
    Args:
        message (str): 用户输入的消息内容
        history (list): Gradio聊天历史记录（在type="messages"时为字典列表）
        
    Returns:
        str: 机器人的回复消息，包含情感支持、心理分析或引导性问题
    """
    global conversation_manager, current_user_id, current_conversation_state
    
    # 如果用户输入为空，给出提示
    if not message.strip():
        return "嗨～ 我是你的校园小树洞喵呜🌳，很高兴在这里遇到你~ 来，和我说说心里话吧！"
    
    try:
        # 🚨 修复：优先使用原版对话流程，确保八维测试分析正确显示
        response = process_conversation(
            message=message,
            manager=conversation_manager,
            api_response_func=get_modelscope_response,
            api_comfort_func=generate_comfort,
            api_emergency_func=emergency_check
        )
        
        # 🚨 关键修复：保存对话到数据库（Camel-AI优化）
        global current_user_id
        if current_user_id is None:
            current_user_id = 1  # 默认用户
        
        try:
            db_manager.save_conversation(current_user_id, "user", message)
            db_manager.save_conversation(current_user_id, "assistant", response)
        except Exception as e:
            print(f"⚠️ 对话保存失败: {e}")
        
        return response
        
    except Exception as e:
        print(f"⚠️ 对话处理出错，使用Camel-AI备用模式: {e}")
        # 备用：使用Camel-AI智能回复
        return chatbot_response_with_camel_ai(message, history)

def chatbot_response_with_camel_ai(message, history):
    """
    使用Camel-AI优化的对话响应函数 - 逻辑重构版
    清晰分离引导流程和自由对话，解决流程中断问题
    """
    global current_user_id, current_conversation_state
    
    # 0. 确保user_id已初始化
    if current_user_id is None:
        current_user_id = 1  # 默认用户

    # 1. 紧急情况检测（安全第一）
    if state_manager._is_emergency(message):
        current_conversation_state = ConversationState.EMERGENCY_MODE
        db_manager.update_user_state(current_user_id, current_conversation_state.value)
        return """亲爱的同学，我能感受到你正在承受巨大的痛苦，这让我非常担心。请记住，你不是一个人，寻求帮助是勇敢和力量的象征。

**请立即拨打全国24小时免费心理危机咨询热线：400-161-9995**，或联系你所在学校的心理健康中心。请务必向身边的人或专业人士求助！

我会一直在这里支持你，但现在最重要的是寻求专业帮助。你的生命很珍贵，值得被好好照顾。💙"""

    # 2. 获取用户档案和当前状态
    user_profile = db_manager.get_user_profile(current_user_id)
    user_info = db_manager.get_user(current_user_id)

    print(f"🔍 调试：进入 chatbot_response_with_camel_ai，用户 {current_user_id}，输入: '{message}'") # 新增调试日志
    print(f"🔍 调试：当前状态 current_conversation_state: {current_conversation_state}") # 新增调试日志
    print(f"🔍 调试：当前用户档案 user_profile (jungian_test_answers): {user_profile.get('jungian_test_answers', [])}") # 新增调试日志

    # 🚨 关键改进：获取用户档案摘要和近期对话历史
    user_profile_summary = db_manager.get_summarized_user_profile(current_user_id)
    recent_history = db_manager.get_conversation_history(current_user_id, limit=RECENT_CONVERSATION_LIMIT)

    # 构建messages列表，将用户档案摘要和近期对话历史作为系统消息的一部分
    # 🚨 关键修复：重构system_messages的content构建方式，避免f-string解析问题
    system_messages_content_parts = [
        "你是喵呜，温暖专业的AI心理陪伴者，你的目标是提供结构化、温暖且个性化的心理支持和陪伴。你的对话应遵循以下原则：",
        "", # 空行
        "1. **共情与接纳**：始终以温暖、支持性的语气回应用户，表达理解和尊重，建立安全信任的对话环境。",
        "2. **引导式对话**：在用户需要时，主动引导用户深入思考，鼓励自我探索，特别是运用\"是什么→为什么→怎么办\"的系统思维框架。",
        "3. **个性化回应**：基于用户档案信息和历史对话，提供高度个性化的建议和互动，避免通用模板。",
        "4. **积极正向**：引导用户关注积极面，培养感恩习惯，激发内在成长动力。",
        "5. **专业性与温度**：融合心理学知识与AI的优势，确保回复的专业性，同时不失人情味。",
        "", # 空行
        "【用户核心档案摘要】",
        user_profile_summary if user_profile_summary else "暂无",
        "", # 空行
        "【近期对话回顾】"
    ]

    if recent_history:
        # 将近期对话历史格式化为字符串列表
        formatted_recent_history = ["**{}**: {}".format(entry['role'], entry['content']) for entry in recent_history]
        system_messages_content_parts.append("\n".join(formatted_recent_history))
    else:
        system_messages_content_parts.append("暂无")

    final_system_content = "\n".join(system_messages_content_parts)

    system_messages = [
        {
            "role": "system",
            "content": final_system_content
        }
    ]

    # 如果是初始问候或新用户，则不加载历史对话到messages，而是由引导流程处理
    if not user_info or current_conversation_state == ConversationState.INITIAL_GREETING:
        messages = [] # 空列表，由引导流程生成初始消息
    else:
        messages = system_messages + [{'role': 'user', 'content': message}]

    if not user_info:
        current_user_id = db_manager.create_user("同学")
        user_profile = db_manager.get_user_profile(current_user_id)
        user_info = db_manager.get_user(current_user_id)
    
    current_conversation_state = get_conversation_state_from_string(
        user_info.get('current_state', 'initial_greeting')
    )

    # 3. 核心流程控制：区分引导模式和自由对话模式
    
    # 🚨 关键优化：检查用户是否已完成引导流程
    is_onboarding_complete = user_profile.get('is_onboarding_complete', False)
    
    # 如果用户已完成引导流程，直接进入自由对话模式
    if is_onboarding_complete:
        print(f"🎯 用户{current_user_id}已完成引导流程，进入自由对话模式")
        # 🚨 新增：在自由聊天模式下进行情绪触发检测
        if state_manager._has_emotion_trigger(message):
            current_conversation_state = ConversationState.GUIDED_EMOTION_WHAT
            db_manager.update_user_state(current_user_id, current_conversation_state.value)
            response = state_manager.get_state_prompt_template(current_conversation_state, user_profile)
            print(f"🔍 调试：检测到情绪触发，进入GUIDED_EMOTION_WHAT状态。回复: {response[:100]}...")
            return response

        try:
            # 🚨 修复：增强的Agent创建和错误处理
            main_agent = agent_manager.get_main_agent(current_user_id)
            if main_agent:
                print("✅ Agent创建成功，开始生成回复...")
                # 🚨 关键改进：将包含上下文的messages传递给Agent
                response = main_agent.step(message, current_conversation_state, system_messages=system_messages, recent_history=recent_history)
                print(f"✅ Agent回复生成完成: {response[:50]}...")
                return response
            else:
                print("⚠️ Agent创建失败，使用兼容模式...")
                # 降级到直接API调用
                return generate_emergency_intelligent_response(message, user_profile)
        except Exception as e:
            print(f"⚠️ Agent处理失败，使用兼容模式: {e}")
            return generate_emergency_intelligent_response(message, user_profile)
    
    # 如果当前状态不是自由聊天且用户未完成引导，则执行引导流程
    if not state_manager.is_terminal_state(current_conversation_state):
        # Case 1: 对话开始 (INITIAL_GREETING)
        if current_conversation_state == ConversationState.INITIAL_GREETING:
            # 获取欢迎语模板
            response = state_manager.get_state_prompt_template(current_conversation_state, user_profile)
            # 转换到下一个状态（收集昵称），为用户的下一步输入做准备
            next_state = state_manager.get_next_state(current_conversation_state, message, user_profile)
            db_manager.update_user_state(current_user_id, next_state.value)
            # 🚨 关键修复：同步内存状态
            current_conversation_state = next_state
            return response
        
        # Case 2: 引导流程进行中 (所有非初始、非终端状态)
        else:
            # a. 根据当前状态，用用户的输入更新用户档案
            update_user_profile_from_state(message, current_conversation_state, user_profile)
            
            # 🚨 关键修复：立即重新获取最新的用户档案，确保昵称等信息被刷新
            user_profile = db_manager.get_user_profile(current_user_id)
            
            # b. 获取下一个状态
            next_state = state_manager.get_next_state(current_conversation_state, message, user_profile)
            print(f"🔍 调试：get_next_state 返回的 next_state: {next_state}") # 新增调试日志
            
            # 🚨 新增：在收集压力后，进入下一步前，插入人性化安抚语
            comfort_message = ""
            if current_conversation_state == ConversationState.COLLECTING_PRESSURE:
                pressure_sources = message
                comfort_message = generate_comfort(pressure_sources) + "\n\n---\n\n"

            db_manager.update_user_state(current_user_id, next_state.value)
            # 🚨 关键修复：同步内存状态
            current_conversation_state = next_state
            
            # c. 如果下一个状态仍然是引导流程的一部分，获取并返回其引导模板 (并附加上安抚语)
            if not state_manager.is_terminal_state(next_state):
                response = comfort_message + state_manager.get_state_prompt_template(next_state, user_profile)
                print(f"🔍 调试：引导流程返回回复: {response[:100]}...") # 新增调试日志
                return response
            # d. 如果下一个状态是自由聊天，标记引导完成并显示欢迎消息
            elif next_state == ConversationState.FREE_CHAT:
                # 🚨 关键优化：标记用户已完成引导流程
                db_manager.update_user_profile(current_user_id, is_onboarding_complete=True)
                print(f"🎉 用户{current_user_id}完成引导流程，解锁自由对话模式")
                
                # 显示进入自由聊天的欢迎消息
                welcome_message = state_manager.get_state_prompt_template(next_state, user_profile)
                print(f"🔍 调试：自由聊天模式返回欢迎回复: {welcome_message[:100]}...") # 新增调试日志
                return welcome_message
    
    # 🚨 新增：处理主动引导情绪分析状态
    elif current_conversation_state == ConversationState.GUIDED_EMOTION_WHAT:
        # 用户输入"好的"或确认，开始引导
        if "好的" in message.lower() or "开始" in message.lower():
            current_conversation_state = state_manager.get_next_state(current_conversation_state, message, user_profile)
            db_manager.update_user_state(current_user_id, current_conversation_state.value)
            response = state_manager.get_state_prompt_template(current_conversation_state, user_profile)
            print(f"🔍 调试：进入GUIDED_EMOTION_WHY状态。回复: {response[:100]}...")
            return response
        else: # 用户选择"暂时不用"或进行自由聊天
            current_conversation_state = ConversationState.FREE_CHAT
            db_manager.update_user_state(current_user_id, current_conversation_state.value)
            response = state_manager.get_state_prompt_template(current_conversation_state, user_profile)
            print(f"🔍 调试：用户选择跳过引导，返回自由聊天。回复: {response[:100]}...")
            return response
            
    elif current_conversation_state == ConversationState.GUIDED_EMOTION_WHY:
        # 用户输入原因后，更新状态并获取下一步引导
        db_manager.update_user_profile(current_user_id, current_emotion_reason=message.strip()) # 临时保存原因
        current_conversation_state = state_manager.get_next_state(current_conversation_state, message, user_profile)
        db_manager.update_user_state(current_user_id, current_conversation_state.value)
        response = state_manager.get_state_prompt_template(current_conversation_state, user_profile)
        print(f"🔍 调试：进入GUIDED_EMOTION_HOW状态。回复: {response[:100]}...")
        return response
        
    elif current_conversation_state == ConversationState.GUIDED_EMOTION_HOW:
        # 用户输入行动计划后，更新状态并回到自由聊天
        db_manager.update_user_profile(current_user_id, current_emotion_action=message.strip()) # 临时保存行动计划
        current_conversation_state = state_manager.get_next_state(current_conversation_state, message, user_profile)
        db_manager.update_user_state(current_user_id, current_conversation_state.value)
        response = state_manager.get_state_prompt_template(current_conversation_state, user_profile)
        print(f"🔍 调试：完成引导，返回自由聊天。回复: {response[:100]}...")
        return response

    # 4. 如果是自由对话状态 (FREE_CHAT)，则调用Camel-AI Agent
    # (无论是从引导流程自然过渡而来，还是用户已经是老用户)
    print(f"🔄 进入自由对话处理，状态: {current_conversation_state}")
    
    try:
        # 🚨 修复：增强的自由对话处理，确保智能回复
        main_agent = agent_manager.get_main_agent(current_user_id)
        if main_agent:
            print("✅ Agent获取成功，开始处理自由对话...")
            response = main_agent.step(message, ConversationState.FREE_CHAT)
            print(f"✅ 自由对话回复生成: {response[:50]}...")
        else:
            print("⚠️ Agent获取失败，使用紧急智能回复...")
            response = generate_emergency_intelligent_response(message, user_profile)
            
        # 🚨 修复：确保对话记录被正确保存
        try:
            db_manager.save_conversation(current_user_id, "user", message)
            db_manager.save_conversation(current_user_id, "assistant", response)
            print("✅ 对话记录保存成功")
        except Exception as e:
            print(f"⚠️ 对话保存失败: {e}")
        
        return response
        
    except Exception as e:
        print(f"❌ 自由对话处理失败: {e}")
        import traceback
        traceback.print_exc()
        # 确保总是有智能回复
        return generate_emergency_intelligent_response(message, user_profile)

def generate_emergency_intelligent_response(message: str, user_profile: dict) -> str:
    """
    🚨 紧急智能回复生成函数
    当Agent失败时提供高质量的备用回复，避免模板化回复
    
    Args:
        message: 用户输入
        user_profile: 用户档案信息
        
    Returns:
        str: 智能生成的温暖回复
    """
    try:
        from api_service_unified import get_modelscope_response
        
        # 构建用户信息上下文
        user_name = user_profile.get('current_nickname', '同学')
        current_emotion = user_profile.get('current_emotion', '')
        current_desire = user_profile.get('current_desire', '')
        
        context_info = []
        if user_name != '同学':
            context_info.append(f"用户昵称：{user_name}")
        if current_emotion:
            context_info.append(f"当前心情：{current_emotion}")
        if current_desire:
            context_info.append(f"核心需求：{current_desire}")
        
        # 🚨 关键改进：获取用户档案摘要和近期对话历史
        user_profile_summary = db_manager.get_summarized_user_profile(user_profile['user_id'])
        recent_history = db_manager.get_conversation_history(user_profile['user_id'], limit=RECENT_CONVERSATION_LIMIT)

        # 整合用户档案摘要
        if user_profile_summary:
            context_info.insert(0, user_profile_summary)

        # 整合近期对话历史
        formatted_history = []
        for entry in recent_history:
            formatted_history.append(f"**{entry['role']}**: {entry['content']}")
        if formatted_history:
            context_info.append("【近期对话回顾】")
            context_info.extend(formatted_history)

        context_str = "\n".join(context_info) if context_info else "初次交流"
        
        # 构建紧急智能回复的系统提示词
        system_prompt = f"""你是喵呜，温暖专业的AI心理陪伴者。

【用户信息及对话上下文】
{context_str}

【紧急回复指导】
1. 称呼用户为"{user_name}"，体现亲近感
2. 针对用户的具体问题给出真诚、个性化的回复
3. 体现专业心理支持水平，但保持温暖亲切的语调
4. 绝对避免使用"我明白你说的XXX"等模板化回复
5. 适当使用emoji增加温暖感，回复50-120字

请真诚地回应用户，给出具体有帮助的建议和支持。"""

        messages = [
            {
                "role": "system",
                "content": system_prompt
            },
            {
                "role": "user",
                "content": message
            }
        ]
        
        print(f"🆘 启动紧急智能回复模式，为{user_name}生成回复...")
        response = get_modelscope_response(messages, temperature=0.8, use_camel=False)
        
        print(f"✅ 紧急智能回复生成成功: {response[:50]}...")
        return response
        
    except Exception as e:
        print(f"❌ 紧急智能回复也失败了: {e}")
        # 最后的人工设计回复，也要个性化
        user_name = user_profile.get('current_nickname', '同学')
        
        # 根据用户输入类型提供不同的回复
        if any(word in message.lower() for word in ['信心', '自信', '勇气']):
            return f"{user_name}，我能理解你现在可能感到有些不够自信，这是很正常的。每个人都有这样的时候，但你能主动寻求帮助，这本身就是一种勇气呢！我们可以一起聊聊具体是什么让你感到缺乏信心，然后找找重新建立信心的方法 🌸💪"
        elif any(word in message.lower() for word in ['难过', '伤心', '痛苦']):
            return f"{user_name}，我能感受到你现在的难过，这样的情绪需要被理解和接纳。有时候允许自己感受这些情绪是很重要的。你愿意和我聊聊是什么让你感到难过吗？我会陪伴着你 🤗💝"
        elif any(word in message.lower() for word in ['焦虑', '紧张', '担心']):
            return f"{user_name}，焦虑的感觉确实不好受，但你能意识到这一点已经很了不起了。我们可以试着一起找出焦虑的源头，然后探讨一些缓解的方法。记住，你不是一个人在面对这些 🌸✨"
        else:
            return f"{user_name}，我听到了你的心声，谢谢你愿意和我分享。每个人的感受都值得被重视，你的话对我来说很重要。我们可以慢慢聊，我会认真倾听并尽我所能帮助你 💝🌟"

def update_user_profile_from_state(user_input, state, user_profile):
    """
    根据当前状态更新用户档案信息
    """
    global current_user_id
    
    try:
        if state == ConversationState.COLLECTING_NICKNAME:
            db_manager.update_user_profile(current_user_id, current_nickname=user_input.strip())
        
        elif state == ConversationState.COLLECTING_MOOD:
            db_manager.update_user_profile(current_user_id, current_emotion=user_input.strip())
        
        elif state == ConversationState.COLLECTING_PRESSURE:
            db_manager.update_user_profile(current_user_id, current_pressure_sources=user_input.strip())
        
        elif state == ConversationState.COLLECTING_DESIRE:
            db_manager.update_user_profile(current_user_id, current_desire=user_input.strip())
        
        elif state == ConversationState.COLLECTING_ENERGY_RITUAL:
            db_manager.update_user_profile(current_user_id, current_energy_ritual=user_input.strip())
        
        elif state == ConversationState.COLLECTING_SUPPORT_NETWORK:
            db_manager.update_user_profile(current_user_id, current_support_network=user_input.strip())
        
        elif state == ConversationState.IN_JUNGIAN_TEST:
            print(f"🔍 APP日志：进入IN_JUNGIAN_TEST状态处理，用户ID: {current_user_id}, 用户输入: {user_input}") # 🚨 新增日志
            # 🚨 关键修复：处理荣格测试答案，包括C选项
            if user_input.upper() in ['A', 'B', 'C']:
                # 获取当前答案列表
                user_profile = db_manager.get_user_profile(current_user_id)
                current_answers = user_profile.get('jungian_test_answers', [])
                print(f"🔍 APP日志：用户 {current_user_id} 当前已收集答案: {current_answers}")
                current_answers.append(user_input.upper())
                print(f"🔍 APP日志：用户 {current_user_id} 新答案加入后: {current_answers}")
                
                # 保存更新的答案列表
                db_manager.update_user_profile(current_user_id, jungian_test_answers=current_answers)
                print(f"✅ APP日志：主对话流程：荣格测试答案已保存到用户档案 - {current_answers}")
                print(f"🔍 APP日志：用户 {current_user_id} 当前答案列表长度: {len(current_answers)}")
                
                # 如果完成8题，计算并保存结果
                print(f"🔍 APP日志：用户 {current_user_id} 已完成的荣格测试答案数量: {len(current_answers)}")
                if len(current_answers) >= 8:
                    print(f"🔍 APP日志：用户 {current_user_id} 满足8题条件，准备计算荣格结果。")
                    try: # 🚨 新增：捕获计算和保存荣格结果的异常
                        from state_manager import calculate_jungian_result
                        result = calculate_jungian_result(current_answers)
                        print(f"🔍 APP日志：计算出的荣格结果: {result}")
                        print(f"🔍 APP日志：即将保存到用户档案的荣格得分: {result['scores']}")
                        
                        # 🚨 关键修复1：先保存荣格得分到用户档案
                        db_manager.update_user_profile(current_user_id, jungian_scores=result['scores'])
                        print(f"✅ APP日志：主对话流程：荣格得分已保存到用户档案 - {result['scores']}")
                        print(f"🔍 APP日志：准备保存到用户档案的荣格得分: {result['scores']}")
                        
                        # 保存到数据库
                        db_manager.save_jungian_test_result(
                            user_id=current_user_id,
                            question_answers=[{"question_index": i, "answer": ans} for i, ans in enumerate(current_answers)],
                            dimension_scores=result['scores'],
                            result_summary=f"MBTI类型: {result['mbti_type']}"
                        )
                        print(f"✅ APP日志：主对话流程：荣格测试结果已保存到历史数据库")
                        
                        # ✅ MBTI结果已准备好供主对话使用
                        print(f"✅ APP日志：主对话流程：MBTI结果已准备 - {result['mbti_type']}")
                    except Exception as e:
                        print(f"❌ APP日志：计算或保存荣格测试结果失败: {e}") # 🚨 新增异常日志
                else:
                    print(f"🔍 APP日志：用户 {current_user_id} 荣格测试答案不足8题，继续收集答案。")
                        
    except Exception as e:
        print(f"❌ APP日志：更新用户档案失败: {e}")

# ========================================================================
# 🎨 前端界面设计 - ChatGPT风格 + 明日香主题（样式分离版）
# ========================================================================

# 前端样式已经直接插入到主文件中，不再需要外部导入
# 所有UI样式都在下面的gr.HTML中定义

# ========================================================================
# 🚀 Gradio界面定义 - 完整应用
# ========================================================================

# 使用 ChatInterface 替代 Interface，提供更好的对话体验
with gr.Blocks(
    # theme=gr.themes.Soft(
    #     primary_hue="orange",
    #     secondary_hue="amber",
    #     neutral_hue="orange"
    # ),
    title=APP_TITLE
) as demo:
    # 加载样式 - 优化版本
    gr.HTML(styles.get_modelscope_iframe_css())

    # 状态变量
    year_state = gr.State(datetime.now().year)
    month_state = gr.State(datetime.now().month)
    selected_date_state = gr.State("未选择日期")

    # MBTI测试状态管理
    test_session = gr.State({
        "current_question": 0,
        "scores": {"E": 0, "I": 0, "S": 0, "N": 0, "T": 0, "F": 0, "J": 0, "P": 0},
        "test_active": False
    })

    # 幸福日志状态管理
    happiness_logs = gr.State([])  # 存储幸福日志记录
    
    # 🚨 移除之前在顶层定义并被标记为"重新定义弹窗的Gr.Row"的代码块
    # new_user_name_input = gr.Textbox(label="新用户昵称", placeholder="请输入昵称，例如：小明", visible=False)
    # confirm_new_user_btn = gr.Button("创建并切换", variant="primary", visible=False)
    # cancel_new_user_btn = gr.Button("取消", variant="secondary", visible=False)
    # user_list_radio = gr.Radio(label="选择用户", choices=[], interactive=True, visible=False)
    # confirm_switch_user_btn = gr.Button("确认切换", variant="primary", size="sm", visible=False)
    # delete_user_btn = gr.Button("🗑️ 删除", variant="stop", size="sm", visible=False)
    # cancel_switch_btn = gr.Button("取消", variant="secondary", size="sm", elem_id="cancel_switch_btn", visible=False)
    # delete_user_info = gr.Markdown("", label="用户数据摘要", visible=False)
    # confirm_delete_btn = gr.Button("🗑️ 确认删除", variant="stop", size="sm", visible=False)
    # cancel_delete_btn = gr.Button("❌ 取消", variant="secondary", size="sm", visible=False)
    
    # 主标题 - 明日香风格重新设计，居中放大，固定淡桃红色
    gr.HTML("""
    <div style="text-align: center; margin: 20px 0;">
        <h1 style="
            font-size: 2.5em; 
            color: #F7C9C1; 
            font-weight: 700; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            margin: 10px 0;
        ">
            🐱 大学生心理助手喵呜 🐱
        </h1>
        <p style="
            font-size: 1.2em; 
            color: #F7C9C1; 
            margin: 5px 0 20px 0;
            font-weight: 400;
        ">
            你的温暖校园小树洞 · 专业心理陪伴
        </p>
    </div>
    """)
    
        # 四个主功能选项卡 - 水平排列布局
    with gr.Tabs() as main_tabs:
        # 选项卡1：主对话
        with gr.Tab("💬 主对话") as main_chat_tab:
            gr.HTML('<h3 style="text-align: center; color: #D9A3C5;">💬 与喵呜聊天</h3>')
            
            # 🚨 重要提示：社区功能解锁条件
            gr.HTML("""
            <div class="community-warning-card">
                <h4 style="color: var(--title-color); margin: 5px 0;">🐱 特别提醒：解锁喵呜社区</h4>
                <p style="color: var(--text-color); margin: 5px 0; font-size: 14px;">
                    <strong>想要使用喵呜社区功能？</strong><br/>
                    请先完成完整对话体验：喵呜了解用户 → 荣格八维测试 → 系统思维训练<br/>
                    <em>这样做是为了确保社区互动更有针对性和效果哦！</em> ✨
                </p>
            </div>
            """)
            
            # ChatGPT风格的左右分栏布局
            with gr.Row():
                # 左侧：历史管理区域
                with gr.Column(scale=1, min_width=250):
                    gr.HTML("<h4 style='margin: 10px 0; color: var(--title-color); text-align: center;'>📝 对话历史</h4>")
                    
                    # 用户管理
                    with gr.Group():
                        gr.HTML("<h5 style='margin: 5px 0; color: var(--title-color);'>👤 用户管理</h5>")
                        current_user_display = gr.Textbox(
                            value="默认用户",
                            label="当前用户",
                            interactive=False,
                            elem_classes="current-user-display"
                        )
                        
                        with gr.Row():
                            new_user_btn = gr.Button("➕ 新用户", size="sm", variant="secondary", elem_id="new-user-btn")
                            switch_user_btn = gr.Button("🔄 切换", size="sm", variant="secondary")
                    
                    # 对话状态显示
                    with gr.Group():
                        gr.HTML("<h5 style='margin: 5px 0; color: var(--title-color);'>🎯 当前状态</h5>")
                        conversation_status = gr.Textbox(
                            value="准备开始对话",
                            label="对话阶段",
                            interactive=False,
                            elem_classes="status-display"
                        )
                        progress_info = gr.HTML("""
                        <div style="font-size: 12px; color: var(--text-color); padding: 5px;">
                            <p>📋 完整流程：</p>
                            <p>1️⃣ 喵呜了解用户</p>
                            <p>2️⃣ 荣格测试</p>
                            <p>3️⃣ 情绪疏导</p>
                            <p>4️⃣ 幸福日志</p>
                            <p>5️⃣ 自由聊天</p>
                        </div>
                        """)
                    
                    # 快捷操作
                    with gr.Group():
                        gr.HTML("<h5 style='margin: 5px 0; color: var(--title-color);'>⚡ 快捷操作</h5>")
                        reset_conversation_btn = gr.Button("🔄 重置对话", variant="secondary", size="sm")
                        clear_btn = gr.Button("🗑️ 清空记录", variant="secondary", size="sm")
                
                # 右侧：主要聊天区域
                with gr.Column(scale=3):
                    # 聊天界面内容
                    chatbot = gr.Chatbot(
                        height=500,
                        label="💌 与喵呜的对话",
                        show_label=False,
                        container=True,
                        type="messages",
                        elem_classes="chatbot",
                        avatar_images=(None, "🐱"),

                    )
                    
                    # 输入区域
                    with gr.Row():
                        msg = gr.Textbox(
                            lines=2,
                            placeholder="在这里输入你的问题...",
                            label="💖 你的心声",
                            scale=4,
                            elem_classes="input-container"
                        )
                        with gr.Column(scale=1):
                            submit_btn = gr.Button("发送💖", variant="primary", elem_classes="btn-primary")
            
            # 用户管理变量和弹窗
            current_user = gr.State("默认用户")
            
            # 🚨 弹窗定义：在gr.Blocks()内部定义模态框
            new_user_modal = gr.Row(visible=False)
            switch_user_modal = gr.Row(visible=False)
            delete_user_modal = gr.Row(visible=False)
            
            # 🚨 弹窗内容定义：直接在Modal内部定义组件
            with new_user_modal:
                with gr.Column(elem_id="new_user_modal_content"):
                    gr.Markdown("### ✨ 创建新用户")
                    new_user_name_input_in_modal = gr.Textbox(label="新用户昵称", placeholder="请输入昵称，例如：小明") # 直接定义
                    with gr.Row():
                        confirm_new_user_btn_in_modal = gr.Button("创建并切换", variant="primary") # 直接定义
                        cancel_new_user_btn_in_modal = gr.Button("取消", variant="secondary") # 直接定义

            with switch_user_modal:
                with gr.Column(elem_id="switch_user_modal_content"):
                    gr.Markdown("### 🔄 切换用户")
                    user_list_radio_in_modal = gr.Radio(label="选择用户", choices=[], interactive=True)
                    with gr.Row():
                        confirm_switch_user_btn_in_modal = gr.Button("确认切换", variant="primary", size="sm")
                        delete_user_btn_in_modal = gr.Button("🗑️ 删除", variant="stop", size="sm")
                        cancel_switch_btn_in_modal = gr.Button("取消", variant="secondary", size="sm", elem_id="cancel_switch_btn")

            with delete_user_modal:
                with gr.Column():
                    gr.HTML("<h4 style='text-align: center; color: var(--title-color);'>⚠️ 删除用户确认</h4>")
                    delete_user_info_in_modal = gr.Markdown("", label="用户数据摘要")
                    gr.HTML("<p style='color: red; text-align: center;'><strong>⚠️ 此操作不可逆！删除后所有数据将永久丢失！</strong></p>")
                    with gr.Row():
                        confirm_delete_btn_in_modal = gr.Button("🗑️ 确认删除", variant="stop", size="sm")
                        cancel_delete_btn_in_modal = gr.Button("❌ 取消", variant="secondary", size="sm")
            
            # 保留原有隐藏组件（兼容性）
            user_selector = gr.Radio(choices=["默认用户"], value="默认用户", visible=False)
            new_user_name = gr.Textbox(visible=False)
            add_user_btn = gr.Button("添加用户", visible=False)
            user_info = gr.Markdown("", visible=False)
        
        # 选项卡2：荣格八维测试（专业人格洞察）
        with gr.Tab("🧠 荣格八维测试（专业人格洞察）") as jungian_tab:
            with gr.Tabs():
                # 子选项卡：MBTI专业测试
                with gr.Tab("📊 MBTI专业测试"):
                    gr.HTML('<h3 style="text-align: center; color: #D9A3C5;">🧠 荣格八维测试</h3>')
                    
                    # 测试进度显示
                    test_progress = gr.Markdown(
                        "**测试准备中** 📝\n\n点击开始按钮启动专业MBTI测试",
                        elem_classes="test-progress-card"
                    )
                    
                    # 测试控制按钮
                    with gr.Group():
                        start_test_btn = gr.Button("🚀 开始MBTI测试", variant="primary", size="lg")
                        reset_test_btn = gr.Button("🔄 重新测试", variant="secondary", size="sm", visible=False)
                    
                    # 测试题目显示区域
                    test_question = gr.Markdown(
                        """### 🎯 荣格八维性格测试
                        
                        8题精准测试，了解你的认知功能和性格特点
                        
                        准备好了就点击"开始测试"按钮吧！""",
                        elem_classes="test-question-card"
                    )
                    
                    # 测试选项区域
                    with gr.Row(visible=False) as test_options_row:
                        option_a_btn = gr.Button("A", variant="primary", scale=1, elem_classes="test-option-btn")
                        option_b_btn = gr.Button("B", variant="primary", scale=1, elem_classes="test-option-btn")
                        option_c_btn = gr.Button("C", variant="secondary", scale=1, elem_classes="test-option-btn") # 🚨 新增C选项按钮
                    
                    # 测试结果显示区域
                    test_result = gr.Markdown("", visible=False, elem_classes="test-result-card")
                
                # 子选项卡：八维知识趣味性讲解
                with gr.Tab("📚 八维知识趣味性讲解"):
                    gr.HTML('<h3 style="text-align: center; color: #D9A3C5;">📚 八维功能宝典</h3>')
                    gr.Markdown("""
                    ### 🧠 荣格八维认知功能介绍
                    
                    荣格八维理论是深度理解个性的权威框架，包含8种基础认知功能：
                    
                    **🔍 感知功能（收集信息）**
                    - **Si (内向实感)**：注重过往经验和细节记忆
                    - **Se (外向实感)**：专注当下体验和具体行动  
                    - **Ni (内向直觉)**：洞察未来趋势和深层含义
                    - **Ne (外向直觉)**：探索可能性和创新连接
                    
                    **⚖️ 判断功能（做决策）**  
                    - **Ti (内向思考)**：追求逻辑一致性和理论框架
                    - **Te (外向思考)**：注重效率和客观标准
                    - **Fi (内向情感)**：坚持个人价值和内在和谐  
                    - **Fe (外向情感)**：关注他人感受和群体和谐
                    
                    完成MBTI测试后，你将了解自己的主导功能栈，发现独特的思维优势！✨
                    """)
        
        # 选项卡3：系统思维（全新增强版 - 专业思维工作区）
        with gr.Tab("🧭 系统思维") as thinking_tab:
            # 顶部标题区域 - 现代化设计
            with gr.Group(elem_classes="thinking-header-section"):
                gr.HTML('''
                <div style="text-align: center; padding: 20px 0; background: linear-gradient(135deg, rgba(182, 211, 226, 0.1), rgba(225, 187, 201, 0.1)); border-radius: 15px; margin-bottom: 20px;">
                    <h2 style="color: var(--title-color); margin: 0; font-size: 28px; font-weight: 700;">🧭 系统思维工作区</h2>
                    <p style="color: var(--text-color); margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">深度思考 · 结构化分析 · 智能引导</p>
                </div>
                ''')

            # 功能导航区域 - 卡片式布局
            with gr.Group(elem_classes="thinking-nav-section"):
                with gr.Row():
                    # 快速开始卡片
                    with gr.Column(scale=1):
                        gr.HTML('''
                        <div style="background: linear-gradient(135deg, rgba(182, 211, 226, 0.15), rgba(168, 200, 218, 0.1));
                                    border: 2px solid rgba(182, 211, 226, 0.3); border-radius: 15px; padding: 20px; text-align: center; height: 120px; display: flex; flex-direction: column; justify-content: center;">
                            <h4 style="color: var(--title-color); margin: 0 0 8px 0; font-size: 18px;">🚀 快速开始</h4>
                            <p style="color: var(--text-color); margin: 0; font-size: 14px; opacity: 0.8;">立即开始系统思维分析</p>
                        </div>
                        ''')

                    # 历史回顾卡片
                    with gr.Column(scale=1):
                        gr.HTML('''
                        <div style="background: linear-gradient(135deg, rgba(241, 209, 219, 0.15), rgba(222, 163, 193, 0.1));
                                    border: 2px solid rgba(241, 209, 219, 0.3); border-radius: 15px; padding: 20px; text-align: center; height: 120px; display: flex; flex-direction: column; justify-content: center;">
                            <h4 style="color: var(--title-color); margin: 0 0 8px 0; font-size: 18px;">📚 历史回顾</h4>
                            <p style="color: var(--text-color); margin: 0; font-size: 14px; opacity: 0.8;">查看过往思维记录</p>
                        </div>
                        ''')

                    # 智能推荐卡片
                    with gr.Column(scale=1):
                        gr.HTML('''
                        <div style="background: linear-gradient(135deg, rgba(240, 255, 244, 0.15), rgba(230, 247, 255, 0.1));
                                    border: 2px solid rgba(240, 255, 244, 0.3); border-radius: 15px; padding: 20px; text-align: center; height: 120px; display: flex; flex-direction: column; justify-content: center;">
                            <h4 style="color: var(--title-color); margin: 0 0 8px 0; font-size: 18px;">🎯 智能推荐</h4>
                            <p style="color: var(--text-color); margin: 0; font-size: 14px; opacity: 0.8;">基于你的特点推荐路径</p>
                        </div>
                        ''')

            # 主要功能选择区域 - 重新设计
            with gr.Group(elem_classes="thinking-main-selection"):
                gr.Markdown("""
                ### 🌟 选择你的思维探索方式

                **每种方式都经过精心设计，结合你的个人档案提供最适合的引导：**
                """)

                with gr.Row():
                    # 完整分析模式
                    with gr.Column(scale=2):
                        choice_a_btn = gr.Button(
                            "🧠 完整系统思维分析\n深度探索 · 个性化引导 · 专业分析",
                            variant="primary",
                            elem_classes="thinking-mode-btn thinking-mode-primary",
                            size="lg"
                        )

                    # 轻松模式
                    with gr.Column(scale=1):
                        choice_b_btn = gr.Button(
                            "💬 轻松交流模式\n自然对话 · 渐进引导",
                            variant="secondary",
                            elem_classes="thinking-mode-btn thinking-mode-secondary",
                            size="lg"
                        )

                    # 自由表达模式
                    with gr.Column(scale=1):
                        choice_c_btn = gr.Button(
                            "🎈 自由表达空间\n随心所欲 · 温暖陪伴",
                            variant="secondary",
                            elem_classes="thinking-mode-btn thinking-mode-secondary",
                            size="lg"
                        )

                # 模式说明区域
                mode_description = gr.Markdown("""
                **💡 选择建议：**
                - 如果你想深入分析问题，建议选择「完整系统思维分析」
                - 如果你想轻松聊聊，建议选择「轻松交流模式」
                - 如果你只想倾诉，建议选择「自由表达空间」
                """, elem_classes="thinking-mode-description")

                # 用户档案显示区域（增强版）
                user_profile_display = gr.Markdown("", visible=False, elem_classes="thinking-profile-enhanced")
            
            # 智能思维导航系统 - 可视化进度跟踪
            with gr.Group(elem_classes="thinking-progress-system", visible=False) as progress_system:
                gr.HTML('''
                <div style="text-align: center; margin: 20px 0;">
                    <h4 style="color: var(--title-color); margin-bottom: 15px;">🗺️ 思维探索地图</h4>
                </div>
                ''')

                # 进度可视化 - 现代化设计
                progress_visualization = gr.HTML('''
                <div style="display: flex; justify-content: center; align-items: center; margin: 20px 0; padding: 20px;
                            background: linear-gradient(135deg, rgba(255, 248, 240, 0.8), rgba(255, 248, 240, 0.6));
                            border-radius: 15px; border: 2px solid rgba(182, 211, 226, 0.3);">
                    <div class="thinking-progress-track">
                        <div class="progress-step active" data-step="start">
                            <div class="step-circle">🎯</div>
                            <div class="step-label">开始</div>
                        </div>
                        <div class="progress-connector"></div>
                        <div class="progress-step" data-step="what">
                            <div class="step-circle">🔍</div>
                            <div class="step-label">是什么</div>
                        </div>
                        <div class="progress-connector"></div>
                        <div class="progress-step" data-step="why">
                            <div class="step-circle">🤔</div>
                            <div class="step-label">为什么</div>
                        </div>
                        <div class="progress-connector"></div>
                        <div class="progress-step" data-step="how">
                            <div class="step-circle">💡</div>
                            <div class="step-label">怎么办</div>
                        </div>
                        <div class="progress-connector"></div>
                        <div class="progress-step" data-step="complete">
                            <div class="step-circle">🎉</div>
                            <div class="step-label">完成</div>
                        </div>
                    </div>
                </div>
                ''', elem_classes="thinking-progress-visual")

                # 当前步骤说明
                current_step_info = gr.Markdown("", elem_classes="thinking-current-step")

            # 第一步：是什么（全新增强版 - 多维度分析）
            with gr.Group(elem_classes="thinking-step-section-enhanced", visible=False) as what_section:
                # 步骤标题 - 现代化设计
                gr.HTML('''
                <div style="background: linear-gradient(135deg, rgba(182, 211, 226, 0.2), rgba(168, 200, 218, 0.15));
                            border-radius: 12px; padding: 20px; margin-bottom: 20px; text-align: center;">
                    <h3 style="color: var(--title-color); margin: 0; font-size: 24px; font-weight: 700;">🔍 第一步：是什么</h3>
                    <p style="color: var(--text-color); margin: 8px 0 0 0; font-size: 16px; opacity: 0.9;">澄清问题本质 · 多维度分析</p>
                </div>
                ''')

                # 智能引导系统 - 基于用户档案的个性化提示
                with gr.Group(elem_classes="thinking-smart-guidance"):
                    personalized_guidance = gr.Markdown("""
                    ### 🌟 让我们一起探索你的困扰

                    **请详细描述让你感到困扰的情况：**

                    💡 **智能提示：**
                    - 📅 **时间**：什么时候开始的？持续多久了？
                    - 🎯 **具体情况**：发生了什么具体的事情？
                    - 💭 **内心感受**：你当时的感受是什么？
                    - 🌍 **影响范围**：对你的生活产生了什么影响？
                    """, elem_classes="thinking-guidance-enhanced")

                # 多维度输入系统
                with gr.Tabs(elem_classes="thinking-input-tabs"):
                    # 基础描述
                    with gr.Tab("📝 基础描述"):
                        problem_input = gr.Textbox(
                            lines=5,
                            placeholder="详细描述你遇到的困扰...\n\n例如：最近总是拖延作业，明明知道deadline要到了，但还是忍不住刷手机。每次想开始学习，心里就很焦虑，但又控制不住自己去做其他事情...",
                            label="🎯 问题描述",
                            elem_classes="thinking-input-enhanced thinking-step-what",
                            max_lines=8
                        )

                    # 情绪识别
                    with gr.Tab("💭 情绪识别"):
                        gr.Markdown("**选择最能描述你当前感受的情绪（可多选）：**")
                        emotion_checkboxes = gr.CheckboxGroup(
                            choices=[
                                "😌 平静安详", "😊 开心愉悦", "😔 低落沮丧", "😰 焦虑不安",
                                "😠 愤怒生气", "😢 难过伤心", "😴 疲惫困倦", "🤔 困惑迷茫",
                                "😤 烦躁不耐", "😨 恐惧害怕", "😞 失望沮丧", "🥺 委屈无助"
                            ],
                            label="情绪状态",
                            elem_classes="thinking-emotion-enhanced"
                        )

                        emotion_intensity = gr.Slider(
                            minimum=1, maximum=10, value=5, step=1,
                            label="情绪强度 (1=轻微, 10=非常强烈)",
                            elem_classes="thinking-emotion-slider"
                        )

                    # 背景信息
                    with gr.Tab("🌍 背景信息"):
                        context_input = gr.Textbox(
                            lines=3,
                            placeholder="相关的背景信息，比如最近发生的事情、环境变化等...",
                            label="📋 背景信息",
                            elem_classes="thinking-input-enhanced"
                        )

                        urgency_level = gr.Radio(
                            choices=["🟢 不急，慢慢来", "🟡 有点急，希望尽快解决", "🔴 很急，急需解决"],
                            label="紧急程度",
                            elem_classes="thinking-urgency-selector"
                        )

                # 智能分析按钮组
                with gr.Row():
                    analyze_what_btn = gr.Button(
                        "🚀 开始智能分析",
                        variant="primary",
                        elem_classes="thinking-btn-enhanced thinking-btn-primary",
                        scale=2,
                        size="lg"
                    )
                    quick_analysis_btn = gr.Button(
                        "⚡ 快速分析",
                        variant="secondary",
                        elem_classes="thinking-btn-enhanced",
                        scale=1
                    )
                    reset_what_btn = gr.Button(
                        "🔄 重新开始",
                        variant="secondary",
                        elem_classes="thinking-btn-enhanced",
                        scale=1
                    )
            
            # 第二步：为什么（革命性增强版 - 多层级深度分析）
            with gr.Group(elem_classes="thinking-step-section-enhanced", visible=False) as why_section:
                # 步骤标题 - 现代化设计
                gr.HTML('''
                <div style="background: linear-gradient(135deg, rgba(241, 209, 219, 0.2), rgba(222, 163, 193, 0.15));
                            border-radius: 12px; padding: 20px; margin-bottom: 20px; text-align: center;">
                    <h3 style="color: var(--title-color); margin: 0; font-size: 24px; font-weight: 700;">🤔 第二步：为什么</h3>
                    <p style="color: var(--text-color); margin: 8px 0 0 0; font-size: 16px; opacity: 0.9;">深挖根本原因 · 多层级分析</p>
                </div>
                ''')

                # 智能过渡引导 - 基于第一步的分析结果
                with gr.Group(elem_classes="thinking-transition-guidance"):
                    why_guidance = gr.Markdown("""
                    ### 🌟 很好！我们已经识别了问题的核心

                    现在让我们深入探索背后的原因。你可以选择最适合当前状态的分析方式：
                    """, visible=False, elem_classes="thinking-guidance-enhanced")

                # 分析方式选择 - 重新设计为卡片式
                with gr.Group(visible=False, elem_classes="thinking-analysis-modes") as why_choice_group:
                    gr.Markdown("### 🎯 选择你的分析方式")

                    with gr.Row():
                        # 深度分析模式
                        with gr.Column(scale=2):
                            why_choice_c = gr.Button(
                                "🔍 深度分析模式\n系统性探索 · 多维度剖析 · 找到根本原因",
                                variant="primary",
                                elem_classes="thinking-analysis-mode-btn thinking-mode-primary",
                                size="lg"
                            )

                        # 倾听陪伴模式
                        with gr.Column(scale=1):
                            why_choice_a = gr.Button(
                                "👂 倾听陪伴模式\n温暖倾听 · 情感支持",
                                variant="secondary",
                                elem_classes="thinking-analysis-mode-btn thinking-mode-secondary"
                            )

                        # 静心休息模式
                        with gr.Column(scale=1):
                            why_choice_b = gr.Button(
                                "☕ 静心休息模式\n暂停思考 · 内心平静",
                                variant="secondary",
                                elem_classes="thinking-analysis-mode-btn thinking-mode-secondary"
                            )
                
                # 深度分析系统 - 多层级递进式分析
                reason_analysis_area = gr.Group(visible=False, elem_classes="thinking-deep-analysis-system")
                with reason_analysis_area:
                    # 分析准备阶段
                    with gr.Group(elem_classes="thinking-analysis-prep"):
                        gr.HTML('''
                        <div style="background: linear-gradient(135deg, rgba(230, 247, 255, 0.3), rgba(240, 255, 244, 0.2));
                                    border-radius: 12px; padding: 20px; margin-bottom: 20px; text-align: center;">
                            <h4 style="color: var(--title-color); margin: 0 0 10px 0;">🌬️ 深度分析准备</h4>
                            <p style="color: var(--text-color); margin: 0; opacity: 0.9;">让我们先调整状态，为深入探索做准备</p>
                        </div>
                        ''')

                        # 呼吸引导 - 互动式设计
                        breathing_guide = gr.HTML('''
                        <div style="background: rgba(255, 255, 255, 0.1); border-radius: 10px; padding: 15px; margin: 15px 0;">
                            <div style="text-align: center;">
                                <div style="font-size: 18px; margin-bottom: 10px;">🫁 深呼吸引导</div>
                                <div style="font-size: 14px; line-height: 1.6; color: var(--text-color); opacity: 0.8;">
                                    <p>1. 慢慢用鼻子吸气，数到4... 1、2、3、4</p>
                                    <p>2. 轻轻用嘴巴呼气，数到6... 1、2、3、4、5、6</p>
                                    <p>3. 感受身体的放松，感受思维的清晰</p>
                                    <p>4. 重复3次，让内心归于平静</p>
                                </div>
                            </div>
                        </div>
                        ''')

                        with gr.Row():
                            breathing_done_btn = gr.Button(
                                "🌸 完成深呼吸，开始探索",
                                variant="primary",
                                elem_classes="thinking-btn-enhanced",
                                scale=2
                            )
                            skip_breathing_btn = gr.Button(
                                "⏭️ 跳过准备",
                                variant="secondary",
                                elem_classes="thinking-btn-enhanced",
                                scale=1
                            )

                    # 多层级分析系统 - 革命性设计
                    with gr.Group(elem_classes="thinking-multilevel-analysis"):
                        gr.Markdown("""
                        ### 🔬 多层级原因分析系统

                        **我们将从5个维度深入探索问题的根本原因：**
                        """)

                        # 使用Tabs实现多层级分析
                        with gr.Tabs(elem_classes="thinking-analysis-tabs"):
                            # 第一层：表面事件
                            with gr.Tab("📅 表面事件层"):
                                gr.Markdown("**🎯 触发事件分析**")
                                event_input = gr.Textbox(
                                    lines=4,
                                    placeholder="详细描述触发这个问题的具体事件...\n\n例如：上周小组讨论时，我提出的想法被同学当众否定，老师也没有支持我的观点...",
                                    label="具体触发事件",
                                    elem_classes="thinking-input-enhanced thinking-step-why"
                                )

                                event_timeline = gr.Textbox(
                                    lines=2,
                                    placeholder="时间线：什么时候开始的？持续了多久？",
                                    label="⏰ 时间线分析",
                                    elem_classes="thinking-input-enhanced"
                                )

                            # 第二层：情感痛点
                            with gr.Tab("💔 情感痛点层"):
                                gr.Markdown("**💭 核心痛点识别**")
                                pain_input = gr.Textbox(
                                    lines=4,
                                    placeholder="描述最让你难受的瞬间或感受...\n\n例如：当众被说'这想法太幼稚了'的那一刻，感觉所有人都在看我，脸瞬间红了，心跳加速...",
                                    label="核心痛点",
                                    elem_classes="thinking-input-enhanced thinking-step-why"
                                )

                                pain_intensity = gr.Slider(
                                    minimum=1, maximum=10, value=5, step=1,
                                    label="痛苦程度 (1=轻微不适, 10=极度痛苦)",
                                    elem_classes="thinking-pain-slider"
                                )

                            # 第三层：影响范围
                            with gr.Tab("🌊 影响范围层"):
                                gr.Markdown("**📊 全面影响评估**")
                                impact_input = gr.Textbox(
                                    lines=4,
                                    placeholder="这件事对你产生了哪些具体影响？\n\n例如：三天都不敢在课上发言，晚上失眠，总是想起那一幕，开始怀疑自己的能力...",
                                    label="具体影响",
                                    elem_classes="thinking-input-enhanced thinking-step-why"
                                )

                                # 影响范围选择
                                impact_areas = gr.CheckboxGroup(
                                    choices=[
                                        "🎓 学习表现", "👥 人际关系", "😴 睡眠质量", "🍽️ 饮食习惯",
                                        "💪 身体健康", "🧠 心理状态", "⚡ 工作效率", "🎯 目标动力",
                                        "👨‍👩‍👧‍👦 家庭关系", "💰 经济状况", "🎨 兴趣爱好", "🔮 未来规划"
                                    ],
                                    label="影响领域",
                                    elem_classes="thinking-impact-areas"
                                )

                            # 第四层：深层信念
                            with gr.Tab("🧠 深层信念层"):
                                gr.Markdown("**🔍 潜在信念探索**")
                                belief_input = gr.Textbox(
                                    lines=4,
                                    placeholder="这件事触发了你内心什么样的想法或信念？\n\n例如：我总是不够好、我的想法没有价值、别人会觉得我很愚蠢...",
                                    label="内在信念",
                                    elem_classes="thinking-input-enhanced thinking-step-why"
                                )

                                # 信念类型识别
                                belief_types = gr.CheckboxGroup(
                                    choices=[
                                        "🏆 完美主义", "😰 害怕失败", "👥 过度在意他人看法", "🎭 冒充者综合征",
                                        "🔒 控制欲", "😔 自我价值感低", "⚡ 急于求成", "🌊 情绪敏感",
                                        "🎯 目标导向过强", "🤝 讨好型人格", "🛡️ 防御心理", "💔 被抛弃恐惧"
                                    ],
                                    label="可能的信念模式",
                                    elem_classes="thinking-belief-types"
                                )

                            # 第五层：系统性因素
                            with gr.Tab("🌍 系统性因素层"):
                                gr.Markdown("**🔗 环境与系统分析**")
                                system_input = gr.Textbox(
                                    lines=4,
                                    placeholder="分析环境、文化、系统性因素的影响...\n\n例如：学校的竞争氛围、家庭的期望压力、社会的成功标准...",
                                    label="系统性因素",
                                    elem_classes="thinking-input-enhanced thinking-step-why"
                                )

                                # 系统因素选择
                                system_factors = gr.CheckboxGroup(
                                    choices=[
                                        "🏫 教育环境", "👨‍👩‍👧‍👦 家庭背景", "🌐 社会文化", "💼 工作环境",
                                        "👥 同伴压力", "📱 媒体影响", "💰 经济条件", "🏥 健康状况",
                                        "⏰ 时间压力", "🎯 期望压力", "🔄 生活变化", "🌟 价值观冲突"
                                    ],
                                    label="系统性影响因素",
                                    elem_classes="thinking-system-factors"
                                )

                        # 智能分析按钮组
                        with gr.Row():
                            analyze_why_btn = gr.Button(
                                "🚀 启动深度分析",
                                variant="primary",
                                elem_classes="thinking-btn-enhanced thinking-btn-primary",
                                scale=2,
                                size="lg"
                            )
                            layer_analysis_btn = gr.Button(
                                "📊 分层分析",
                                variant="secondary",
                                elem_classes="thinking-btn-enhanced",
                                scale=1
                            )
                            reset_why_btn = gr.Button(
                                "🔄 重新分析",
                                variant="secondary",
                                elem_classes="thinking-btn-enhanced",
                                scale=1
                            )
                
                # 选择A的倾听模式
                listening_area = gr.Group(visible=False, elem_classes="thinking-step-area")
                with listening_area:
                    with gr.Group(elem_classes="thinking-guidance"):
                        gr.Markdown("**我在这里，安心地说出你的感受吧～** 💙")
                        gr.Markdown("""
                        💫 **没有对错，没有评判，只有倾听和理解**
                        
                        想到什么就说什么，我会认真听的...
                        """)
                    
                    listening_input = gr.Textbox(
                        lines=5,
                        placeholder="说说看，你现在想到什么就说什么，我会认真听的...",
                        label="🤗 倾诉空间",
                        elem_classes="thinking-input",
                        max_lines=8
                    )
                    
                    with gr.Row():
                        listening_btn = gr.Button("💝 分享给喵呜", variant="primary", elem_classes="thinking-btn", scale=2)
                        back_to_why_btn = gr.Button("🔙 回到原因分析", variant="secondary", elem_classes="thinking-btn", scale=1)
                
                # 选择B的安静模式
                quiet_area = gr.Group(visible=False, elem_classes="thinking-step-area")
                with quiet_area:
                    with gr.Group(elem_classes="thinking-guidance"):
                        gr.Markdown("""
                        **让我们一起安静一会儿吧...** ☕️
                        
                        有时候什么都不想，就这样静静地待着，也是很好的。
                        深呼吸几次，感受一下此刻的宁静。
                        
                        💫 **静心小贴士：**
                        - 慢慢吸气，数到4
                        - 轻轻呼气，数到6
                        - 感受身体的放松
                        - 让思绪慢慢沉淀
                        
                        当你准备好继续的时候，就点击下面的按钮～
                        """)
                    
                    with gr.Row():
                        ready_btn = gr.Button("🌸 我准备好继续了", variant="primary", elem_classes="thinking-btn", scale=2)
                        back_to_why_btn2 = gr.Button("🔙 回到原因分析", variant="secondary", elem_classes="thinking-btn", scale=1)
            
            # 第三步：怎么办（革命性增强版 - 智能行动规划系统）
            with gr.Group(elem_classes="thinking-step-section-enhanced", visible=False) as how_section:
                # 步骤标题 - 现代化设计
                gr.HTML('''
                <div style="background: linear-gradient(135deg, rgba(240, 255, 244, 0.2), rgba(230, 247, 255, 0.15));
                            border-radius: 12px; padding: 20px; margin-bottom: 20px; text-align: center;">
                    <h3 style="color: var(--title-color); margin: 0; font-size: 24px; font-weight: 700;">💡 第三步：怎么办</h3>
                    <p style="color: var(--text-color); margin: 8px 0 0 0; font-size: 16px; opacity: 0.9;">智能行动规划 · 个性化方案 · 执行跟踪</p>
                </div>
                ''')

                # 智能行动规划准备系统
                action_prep = gr.Group(visible=False, elem_classes="thinking-action-prep-system")
                with action_prep:
                    # 成就感激发区域
                    with gr.Group(elem_classes="thinking-achievement-boost"):
                        gr.HTML('''
                        <div style="background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.05));
                                    border-radius: 12px; padding: 20px; margin-bottom: 20px; text-align: center; border: 2px solid rgba(255, 215, 0, 0.2);">
                            <h4 style="color: var(--title-color); margin: 0 0 10px 0;">🎉 太棒了！你已经完成了最难的部分</h4>
                            <p style="color: var(--text-color); margin: 0; opacity: 0.9;">
                                通过深入分析，你已经获得了宝贵的自我洞察。现在让我们将这些洞察转化为具体的行动方案！
                            </p>
                        </div>
                        ''')

                    # 状态调整系统 - 互动式设计
                    with gr.Group(elem_classes="thinking-state-adjustment"):
                        gr.Markdown("### 🧘‍♀️ 行动前状态调整")

                        # 当前状态评估
                        current_energy = gr.Slider(
                            minimum=1, maximum=10, value=5, step=1,
                            label="当前能量水平 (1=很疲惫, 10=精力充沛)",
                            elem_classes="thinking-energy-slider"
                        )

                        current_motivation = gr.Slider(
                            minimum=1, maximum=10, value=5, step=1,
                            label="当前动机水平 (1=完全没动力, 10=非常有动力)",
                            elem_classes="thinking-motivation-slider"
                        )

                        # 调整方式选择
                        adjustment_method = gr.Radio(
                            choices=[
                                "🌬️ 深呼吸放松 (3分钟)",
                                "💪 能量激发 (积极暗示)",
                                "🎯 目标聚焦 (明确意图)",
                                "⏭️ 直接开始 (跳过调整)"
                            ],
                            label="选择状态调整方式",
                            elem_classes="thinking-adjustment-method"
                        )

                        with gr.Row():
                            start_adjustment_btn = gr.Button(
                                "🚀 开始状态调整",
                                variant="primary",
                                elem_classes="thinking-btn-enhanced",
                                scale=2
                            )
                            skip_prep_btn = gr.Button(
                                "⏭️ 跳过调整",
                                variant="secondary",
                                elem_classes="thinking-btn-enhanced",
                                scale=1
                            )
                
                # 智能行动方案制定系统 - 革命性设计
                solution_area = gr.Group(visible=False, elem_classes="thinking-solution-system")
                with solution_area:
                    # 智能推荐引擎
                    with gr.Group(elem_classes="thinking-smart-recommendations"):
                        gr.Markdown("### 🤖 AI智能推荐系统")

                        # 基于分析结果的智能推荐
                        smart_recommendations = gr.HTML('''
                        <div style="background: linear-gradient(135deg, rgba(255, 248, 240, 0.8), rgba(230, 247, 255, 0.6));
                                    border-radius: 12px; padding: 20px; margin: 15px 0; border: 2px solid rgba(182, 211, 226, 0.3);">
                            <h4 style="color: var(--title-color); margin: 0 0 15px 0;">🎯 基于你的分析，我推荐以下行动方案：</h4>
                            <div id="recommendations-content" style="color: var(--text-color); line-height: 1.6;">
                                <p>📊 正在分析你的情况，生成个性化推荐...</p>
                            </div>
                        </div>
                        ''')

                        # 推荐接受度
                        recommendation_feedback = gr.Radio(
                            choices=["✅ 很有帮助，我想基于这些推荐制定计划", "🤔 有一些帮助，我想做些调整", "❌ 不太适合，我想自己制定"],
                            label="对推荐的反馈",
                            elem_classes="thinking-recommendation-feedback"
                        )

                    # 多维度行动规划系统
                    with gr.Tabs(elem_classes="thinking-action-tabs"):
                        # 即时行动
                        with gr.Tab("⚡ 即时行动"):
                            gr.Markdown("**🚀 今天就能开始的小行动**")
                            immediate_action = gr.Textbox(
                                lines=3,
                                placeholder="今天就能做的一个小行动...\n\n例如：今晚花10分钟整理明天要说的话，准备在小组讨论时主动发言一次",
                                label="立即行动",
                                elem_classes="thinking-input-enhanced thinking-step-how"
                            )

                            action_difficulty = gr.Slider(
                                minimum=1, maximum=10, value=3, step=1,
                                label="行动难度 (1=非常容易, 10=非常困难)",
                                elem_classes="thinking-difficulty-slider"
                            )

                        # 短期计划
                        with gr.Tab("📅 短期计划 (1周内)"):
                            gr.Markdown("**🎯 一周内的具体目标**")
                            short_term_plan = gr.Textbox(
                                lines=4,
                                placeholder="一周内要完成的具体目标和行动步骤...",
                                label="短期行动计划",
                                elem_classes="thinking-input-enhanced thinking-step-how"
                            )

                            # 每日任务分解
                            daily_tasks = gr.Textbox(
                                lines=3,
                                placeholder="将目标分解为每日小任务...",
                                label="📋 每日任务分解",
                                elem_classes="thinking-input-enhanced"
                            )

                        # 中期规划
                        with gr.Tab("🎯 中期规划 (1个月内)"):
                            gr.Markdown("**🌟 一个月内的成长目标**")
                            medium_term_plan = gr.Textbox(
                                lines=4,
                                placeholder="一个月内想要达到的状态和目标...",
                                label="中期发展规划",
                                elem_classes="thinking-input-enhanced thinking-step-how"
                            )

                            # 里程碑设置
                            milestones = gr.Textbox(
                                lines=3,
                                placeholder="设置几个重要的里程碑节点...",
                                label="🏆 里程碑设置",
                                elem_classes="thinking-input-enhanced"
                            )

                        # 支持系统
                        with gr.Tab("🤝 支持系统"):
                            gr.Markdown("**💪 建立你的支持网络**")

                            # 人际支持
                            people_support = gr.CheckboxGroup(
                                choices=[
                                    "👨‍👩‍👧‍👦 家人支持", "👥 朋友陪伴", "👨‍🏫 老师指导", "👥 同学互助",
                                    "💼 导师建议", "🏥 专业帮助", "👥 社群支持", "🤝 伙伴监督"
                                ],
                                label="人际支持网络",
                                elem_classes="thinking-people-support"
                            )

                            # 工具资源
                            tool_support = gr.CheckboxGroup(
                                choices=[
                                    "📱 手机提醒", "📅 日程管理", "📚 学习资料", "🎵 音乐放松",
                                    "📝 记录工具", "⏰ 时间管理", "🏃‍♂️ 运动健身", "🧘‍♀️ 冥想练习"
                                ],
                                label="工具资源支持",
                                elem_classes="thinking-tool-support"
                            )

                            # 环境优化
                            environment_input = gr.Textbox(
                                lines=3,
                                placeholder="如何优化你的环境来支持行动...",
                                label="🌍 环境优化",
                                elem_classes="thinking-input-enhanced"
                            )

                        # 风险预案
                        with gr.Tab("⚠️ 风险预案"):
                            gr.Markdown("**🛡️ 预见困难，制定应对策略**")

                            potential_obstacles = gr.Textbox(
                                lines=3,
                                placeholder="可能遇到的困难和阻碍...",
                                label="潜在障碍",
                                elem_classes="thinking-input-enhanced"
                            )

                            coping_strategies = gr.Textbox(
                                lines=4,
                                placeholder="应对策略和备选方案...",
                                label="应对策略",
                                elem_classes="thinking-input-enhanced"
                            )

                            # 紧急联系
                            emergency_contact = gr.Textbox(
                                lines=2,
                                placeholder="遇到困难时可以联系的人...",
                                label="🆘 紧急联系人",
                                elem_classes="thinking-input-enhanced"
                            )

                    # 智能行动计划生成按钮组
                    with gr.Row():
                        analyze_how_btn = gr.Button(
                            "🚀 生成智能行动计划",
                            variant="primary",
                            elem_classes="thinking-btn-enhanced thinking-btn-primary",
                            scale=2,
                            size="lg"
                        )
                        preview_plan_btn = gr.Button(
                            "👀 预览计划",
                            variant="secondary",
                            elem_classes="thinking-btn-enhanced",
                            scale=1
                        )
                        reset_how_btn = gr.Button(
                            "🔄 重新规划",
                            variant="secondary",
                            elem_classes="thinking-btn-enhanced",
                            scale=1
                        )
                    
                    # 行动鼓励（个性化）
                    with gr.Group(elem_classes="thinking-guidance"):
                        gr.Markdown("""
                        💝 **记住：当你开始行动就已经走在胜利的道路上！**
                        
                        - 行动比完美更重要
                        - 小步前进胜过原地思考
                        - 每一个微小的改变都值得庆祝
                        
                        **你已经迈出了最难的一步** —— 意识到问题并愿意改变！ ✨
                        """)
                    
                    # 思考能力范围
                    with gr.Group(elem_classes="thinking-guidance"):
                        gr.HTML("<h5 style='color: var(--title-color); margin: 10px 0 5px 0;'>🎯 第二步：思考能力范围</h5>")
                        gr.Markdown("**针对前面分析的原因，你现在能做什么来改善情况？**\n\n从小处着手，降低行动门槛，想想最容易开始的小行动。")
                        capability_input = gr.Textbox(
                            lines=3,
                            placeholder="比如：今晚整理一下明天要说的话，准备在小组讨论时主动发言一次...",
                            label="💡 我能做的事",
                            elem_classes="thinking-input thinking-step-how",
                            max_lines=4
                        )
                    
                    # Mini任务设计
                    with gr.Group(elem_classes="thinking-guidance"):
                        gr.HTML("<h5 style='color: var(--title-color); margin: 15px 0 5px 0;'>📌 第三步：Mini任务设计</h5>")
                        gr.Markdown("**给明早的自己贴个mini任务贴纸，写啥？（≤15字越好）**\n\n将行动与原因挂钩，设计一个具体可执行的小任务。")
                        mini_task_input = gr.Textbox(
                            lines=2,
                            placeholder="比如：明天课堂发言一次 / 给室友道歉5分钟",
                            label="📝 明天的Mini任务",
                            elem_classes="thinking-input thinking-step-how",
                            max_lines=3
                        )
                    
                    # 分析按钮
                    with gr.Row():
                        analyze_how_btn = gr.Button("🎯 生成完整行动计划", variant="primary", elem_classes="thinking-btn", scale=2)
                        reset_how_btn = gr.Button("🔄 重新思考", variant="secondary", elem_classes="thinking-btn", scale=1)
                    
                    # 行动鼓励
                    with gr.Group(elem_classes="thinking-guidance"):
                        gr.Markdown("""
                        💝 **记住：当你开始行动就已经走在胜利的道路上！**
                        
                        - 行动比完美更重要
                        - 小步前进胜过原地思考
                        - 每一个微小的改变都值得庆祝
                        
                        **你已经迈出了最难的一步** —— 意识到问题并愿意改变！ ✨
                        """)
            
            # AI智能分析中心 - 全新设计
            with gr.Group(elem_classes="thinking-ai-center"):
                # AI分析结果显示区域 - 现代化设计
                thinking_analysis = gr.HTML('''
                <div style="background: linear-gradient(135deg, rgba(255, 248, 240, 0.9), rgba(230, 247, 255, 0.8));
                            border-radius: 15px; padding: 25px; margin: 20px 0; border: 2px solid rgba(182, 211, 226, 0.3);
                            box-shadow: 0 8px 25px rgba(182, 211, 226, 0.2);">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h3 style="color: var(--title-color); margin: 0; font-size: 22px; font-weight: 700;">🤖 AI智能分析助手</h3>
                        <p style="color: var(--text-color); margin: 8px 0 0 0; opacity: 0.9;">专业分析 · 个性化洞察 · 温暖陪伴</p>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;">
                        <div style="background: rgba(255, 255, 255, 0.3); border-radius: 10px; padding: 15px; text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 8px;">🔍</div>
                            <div style="font-weight: 600; color: var(--title-color); margin-bottom: 5px;">专业分析</div>
                            <div style="font-size: 13px; color: var(--text-color); opacity: 0.8;">基于心理学和系统思维</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.3); border-radius: 10px; padding: 15px; text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 8px;">💡</div>
                            <div style="font-weight: 600; color: var(--title-color); margin-bottom: 5px;">启发思考</div>
                            <div style="font-size: 13px; color: var(--text-color); opacity: 0.8;">多角度看问题</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.3); border-radius: 10px; padding: 15px; text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 8px;">🎯</div>
                            <div style="font-weight: 600; color: var(--title-color); margin-bottom: 5px;">具体建议</div>
                            <div style="font-size: 13px; color: var(--text-color); opacity: 0.8;">可操作的行动指导</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.3); border-radius: 10px; padding: 15px; text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 8px;">💪</div>
                            <div style="font-weight: 600; color: var(--title-color); margin-bottom: 5px;">温暖鼓励</div>
                            <div style="font-size: 13px; color: var(--text-color); opacity: 0.8;">陪伴你一起成长</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.3); border-radius: 10px; padding: 15px; text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 8px;">🧠</div>
                            <div style="font-weight: 600; color: var(--title-color); margin-bottom: 5px;">个性化洞察</div>
                            <div style="font-size: 13px; color: var(--text-color); opacity: 0.8;">基于你的特征和历史</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.3); border-radius: 10px; padding: 15px; text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 8px;">💝</div>
                            <div style="font-weight: 600; color: var(--title-color); margin-bottom: 5px;">连续性支持</div>
                            <div style="font-size: 13px; color: var(--text-color); opacity: 0.8;">记住每次交流</div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(255, 255, 255, 0.2); border-radius: 10px;">
                        <p style="color: var(--text-color); margin: 0; font-size: 16px; font-weight: 500;">
                            ✨ 准备好了吗？选择一个开始方式，让我们开始这段探索之旅！
                        </p>
                    </div>
                </div>
                ''', elem_classes="thinking-analysis-enhanced")

                # 动态分析结果显示区域
                dynamic_analysis_result = gr.Markdown("", visible=False, elem_classes="thinking-dynamic-result")

            # 历史回顾与数据可视化系统
            with gr.Group(elem_classes="thinking-history-system", visible=False) as history_system:
                gr.HTML('''
                <div style="text-align: center; margin: 20px 0;">
                    <h4 style="color: var(--title-color); margin-bottom: 15px;">📚 思维历史回顾</h4>
                </div>
                ''')

                with gr.Tabs(elem_classes="thinking-history-tabs"):
                    # 历史记录
                    with gr.Tab("📖 历史记录"):
                        conversation_history_display = gr.Markdown("", elem_classes="thinking-history-display")

                        # 历史筛选
                        history_filter = gr.Radio(
                            choices=["📅 最近一周", "📅 最近一月", "📅 全部记录", "🎯 特定主题"],
                            label="筛选范围",
                            elem_classes="thinking-history-filter"
                        )

                    # 成长轨迹
                    with gr.Tab("📈 成长轨迹"):
                        growth_visualization = gr.HTML('''
                        <div style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; margin: 15px 0;">
                            <h5 style="color: var(--title-color); margin: 0 0 15px 0;">🌱 你的成长轨迹</h5>
                            <div id="growth-chart" style="height: 200px; display: flex; align-items: center; justify-content: center; color: var(--text-color);">
                                📊 成长数据可视化图表将在这里显示
                            </div>
                        </div>
                        ''')

                        # 成长指标
                        growth_metrics = gr.HTML('''
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                            <div style="background: rgba(255, 255, 255, 0.1); border-radius: 10px; padding: 15px; text-align: center;">
                                <div style="font-size: 20px; color: var(--title-color); font-weight: 700;">12</div>
                                <div style="font-size: 14px; color: var(--text-color);">完成的思维分析</div>
                            </div>
                            <div style="background: rgba(255, 255, 255, 0.1); border-radius: 10px; padding: 15px; text-align: center;">
                                <div style="font-size: 20px; color: var(--title-color); font-weight: 700;">8</div>
                                <div style="font-size: 14px; color: var(--text-color);">制定的行动计划</div>
                            </div>
                            <div style="background: rgba(255, 255, 255, 0.1); border-radius: 10px; padding: 15px; text-align: center;">
                                <div style="font-size: 20px; color: var(--title-color); font-weight: 700;">75%</div>
                                <div style="font-size: 14px; color: var(--text-color);">行动执行率</div>
                            </div>
                            <div style="background: rgba(255, 255, 255, 0.1); border-radius: 10px; padding: 15px; text-align: center;">
                                <div style="font-size: 20px; color: var(--title-color); font-weight: 700;">4.8</div>
                                <div style="font-size: 14px; color: var(--text-color);">平均满意度</div>
                            </div>
                        </div>
                        ''')

                    # 主题分析
                    with gr.Tab("🏷️ 主题分析"):
                        theme_analysis = gr.HTML('''
                        <div style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; margin: 15px 0;">
                            <h5 style="color: var(--title-color); margin: 0 0 15px 0;">🎯 你最关注的主题</h5>
                            <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                                <span style="background: rgba(182, 211, 226, 0.3); color: var(--text-color); padding: 8px 15px; border-radius: 20px; font-size: 14px;">学习压力</span>
                                <span style="background: rgba(241, 209, 219, 0.3); color: var(--text-color); padding: 8px 15px; border-radius: 20px; font-size: 14px;">人际关系</span>
                                <span style="background: rgba(240, 255, 244, 0.3); color: var(--text-color); padding: 8px 15px; border-radius: 20px; font-size: 14px;">情绪管理</span>
                                <span style="background: rgba(230, 247, 255, 0.3); color: var(--text-color); padding: 8px 15px; border-radius: 20px; font-size: 14px;">未来规划</span>
                            </div>
                        </div>
                        ''')

                        # 主题详细分析
                        theme_details = gr.Markdown("", elem_classes="thinking-theme-details")

            # 个性化洞察区域
            personalized_analysis = gr.Markdown("", visible=False, elem_classes="thinking-personalized-analysis-enhanced")
        
        # 选项卡4：幸福日志（积极心理习惯）
        with gr.Tab("🐾 幸福日志（积极心理习惯）") as happiness_tab:
            with gr.Tabs():
                # 子选项卡：记录幸福
                with gr.Tab("📝 记录幸福"):
                    gr.HTML('<h3 style="text-align: center; color: var(--title-color);">📝 记录今天的幸福</h3>')
                    gr.HTML('<p style="text-align: center; color: var(--text-color); margin: 10px 0;">喵~ 每天记录一点点幸福，就像收集小鱼干一样让人开心~</p>')
                    
                                        # 记录幸福区域
                    with gr.Group(elem_classes="happiness-record-section"):
                        prompt = gr.Textbox(
                            value=random.choice(CAT_PROMPTS), 
                            label="喵说", 
                            interactive=False
                        )
                        entry_date = gr.Textbox(
                            label="选择日期", 
                            value=date.today().strftime("%Y-%m-%d"),
                            info="格式：YYYY-MM-DD"
                        )
                        happiness_input = gr.Textbox(
                            label="今天的幸福事", 
                            placeholder="喵~ 在这里写下今天让你开心的事情吧~",
                            lines=3
                        )
                        mood_rating = gr.Slider(
                            minimum=1, maximum=5, value=3, step=1, 
                            label="开心程度", info="1=有点低落，5=超开心"
                        )
                        with gr.Row():
                            add_btn = gr.Button("🐾 保存幸福", variant="primary", scale=2)
                            clear_happiness_btn = gr.Button("🗑️ 清空", variant="secondary", scale=1)
                        response = gr.Textbox(label="喵的回应", interactive=False)
                
                # 子选项卡：日历回顾
                with gr.Tab("📅 日历回顾"):
                    gr.HTML('<h3 style="text-align: center; color: var(--title-color);">📅 幸福时光回顾</h3>')
                    gr.HTML('<p style="text-align: center; color: var(--text-color); font-size: 0.9em;">喵~ 点击有🐾标记的日期查看那天的幸福记录哦~</p>')
                    
                    # 当前选中日期显示
                    selected_date_display = gr.Textbox(
                        value="未选择日期", 
                        label="当前选中",
                        interactive=False
                    )
                    
                    # 日历标题和导航
                    calendar_title_display = gr.Markdown(f"### {datetime.now().year}年 {calendar.month_name[datetime.now().month]}")
                    
                    with gr.Row():
                        prev_btn = gr.Button("◀️ 上个月", size="sm")
                        next_btn = gr.Button("下个月 ▶️", size="sm")
                        refresh_btn = gr.Button("🔄 刷新", variant="secondary", size="sm")
                    
                    # 日历显示
                    calendar_df = gr.DataFrame(
                        value=generate_calendar_data(),
                        headers=None,
                        interactive=False,
                        elem_id="happiness_calendar"
                    )
                    
                    # 🚨 修复：添加日期选择器，因为DataFrame不支持点击选择
                    date_selector = gr.Textbox(
                        label="📅 选择日期（格式：YYYY年MM月DD日）",
                        placeholder="例如：2025年08月26日",
                        value="",
                        interactive=True
                    )
                    
                    view_date_btn = gr.Button("🔍 查看该日期的记录", variant="primary", size="sm")
                    
                    # 选中日期的记录显示
                    date_entries = gr.Textbox(
                        label="选中日期的记录", 
                        interactive=False,
                        lines=4
                    )

        # 选项卡5：喵呜社区（温暖的互助空间）
        with gr.Tab("🐱 喵呜社区") as community_tab:  # 性能优化：延迟加载
            # 社区权限检查状态
            community_access_status = gr.State({"has_access": False, "message": ""})
            
            # 权限检查和引导界面 - 动态内容
            community_access_check = gr.Markdown("", visible=True)
            
            # 社区主界面（需要解锁后才可见）
            with gr.Group(visible=False) as community_main_interface:
                gr.HTML('<h3 style="text-align: center; color: var(--title-color);">🐱 欢迎来到喵呜社区</h3>')
                gr.HTML('<p style="text-align: center; color: var(--text-color); margin: 10px 0;">这里是温暖的互助空间，大家可以匿名分享经验、寻求帮助～</p>')
                
                with gr.Tabs():
                    # 子选项卡1：社区广场
                    with gr.Tab("🏪 社区广场"):
                        gr.HTML('<h4 style="color: var(--title-color);">📋 求助帖子</h4>')
                        
                        # 帖子刷新按钮
                        refresh_posts_btn = gr.Button("🔄 刷新帖子", variant="secondary", size="sm")
                        
                        # 帖子列表显示 - 静态样本展示
                        posts_display = gr.Markdown(
                            "正在加载社区帖子...",
                            label="社区帖子",
                            elem_classes="happiness-records"
                        )

                    # 子选项卡：广场匹配推荐
                    with gr.Tab("🤝 广场匹配推荐"):
                        gr.HTML('<h4 style="color: var(--title-color);">🤝 匹配推荐</h4>')
                        gr.HTML('<p style="text-align: center; color: var(--text-color); margin: 10px 0;">喵~ 根据你的MBTI类型，为你推荐可能志同道合的小伙伴和求助帖哦！</p>')
                        
                        refresh_matching_btn = gr.Button("🔄 刷新匹配推荐", variant="primary", size="sm")
                        matching_recommendations_display = gr.Markdown(
                            "正在加载匹配推荐...",
                            label="匹配推荐",
                            elem_classes="happiness-records"
                        )

                    # 子选项卡：发布求助
                    with gr.Tab("💝 发布求助"):
                        gr.HTML('<h4 style="color: var(--title-color);">📝 发布你的求助</h4>')
                        gr.Markdown("""
                        **💡 发帖小贴士：**
                        - 🎭 **匿名保护**：你的身份会以"温暖的XXXX小伙伴"形式显示
                        - 🎯 **具体描述**：详细说明你的困扰，这样大家能给出更有针对性的建议
                        - 💝 **互助精神**：记得也去帮助其他需要帮助的同学哦！
                        """)
                        
                        # 发帖表单
                        with gr.Group(elem_classes="happiness-record-section"):
                            post_title = gr.Textbox(
                                label="📝 帖子标题",
                                placeholder="简短描述你的困扰，比如：人际交往中的困惑",
                                lines=1
                            )
                            post_content = gr.Textbox(
                                label="📄 详细内容", 
                                placeholder="详细描述你的情况，遇到的困扰，以及希望得到什么样的帮助...",
                                lines=5
                            )
                            post_tags = gr.Textbox(
                                label="🏷️ 标签 (可选)",
                                placeholder="用逗号分隔，比如：人际关系,学习压力,情感困扰",
                                lines=1
                            )
                            
                            with gr.Row():
                                publish_post_btn = gr.Button("🚀 发布求助", variant="primary", scale=2)
                                clear_post_btn = gr.Button("🗑️ 清空", variant="secondary", scale=1)
                            
                            post_result = gr.Textbox(label="发布结果", interactive=False)

                    # 子选项卡：我的求助
                    with gr.Tab("👥 我的求助"):
                        gr.HTML('<h4 style="color: var(--title-color);">📚 我的求助记录</h4>')
                        
                        # 刷新我的求助按钮
                        refresh_my_help_requests_btn = gr.Button("🔄 刷新我的求助", variant="secondary", size="sm")
                        
                        # 我的求助显示
                        my_help_requests_display = gr.Markdown(
                            "正在加载你的求助记录...",
                            label="我的求助",
                            elem_classes="happiness-records"
                        )

                    # 子选项卡：喵呜精选
                    with gr.Tab("💡 喵呜精选"):
                        gr.HTML('<h4 style="color: var(--title-color);">💡 喵呜精选：积极心理学内容</h4>')
                        gr.HTML('<p style="text-align: center; color: var(--text-color); margin: 10px 0;">喵~ 为你精选高质量的积极心理学文章和互动视频哦！</p>')
                        
                        refresh_psychology_btn = gr.Button("🔄 刷新推荐", variant="primary", size="sm")
                        psychology_recommendations_display = gr.Markdown(
                            "正在加载积极心理学推荐...",
                            label="积极心理学推荐",
                            elem_classes="happiness-records"
                        )

                    # 子选项卡：喵呜乐园
                    with gr.Tab("🎮 喵呜乐园"):
                        gr.HTML('<h4 style="color: var(--title-color);">🎮 喵呜乐园：心理健康小游戏</h4>')
                        gr.HTML('<p style="text-align: center; color: var(--text-color); margin: 10px 0;">喵~ 一起玩些小游戏，放松心情，提升幸福感吧！</p>')
                        
                        refresh_games_btn = gr.Button("🔄 刷新游戏", variant="primary", size="sm")
                        game_recommendations_display = gr.Markdown(
                            "正在加载心理健康小游戏推荐...",
                            label="心理健康小游戏推荐",
                            elem_classes="happiness-records"
                        )

    # ===== 功能函数定义 =====

    def user_message_handler(message, history, status_display):
        """
        处理用户消息并更新聊天历史 - 完整的引导式对话流程
        实现：喵呜了解用户 → 荣格测试(可选) → 系统思维(可选) → 自由聊天的完整闭环
        """
        if not message.strip():
            return "", history, status_display
        
        # Gradio 的 history 在 type="messages" 时已经是字典列表
        # 添加用户消息到历史
        history.append({"role": "user", "content": message})
        
        # 🎯 核心改进：使用完整的引导式对话流程
        bot_reply = chatbot_response_with_camel_ai(message, history)
        
        # 添加机器人回复到历史
        history.append({"role": "assistant", "content": bot_reply})
        
        # 更新状态显示
        global current_conversation_state
        status_text = get_status_description(current_conversation_state)
        
        return "", history, status_text
    
    def get_status_description(state):
        """获取状态的友好描述"""
        status_map = {
            ConversationState.INITIAL_GREETING: "🌟 初始问候",
            ConversationState.COLLECTING_NICKNAME: "📝 收集昵称中",
            ConversationState.COLLECTING_MOOD: "💭 了解心情中",
            ConversationState.COLLECTING_PRESSURE: "🎯 收集压力源",
            ConversationState.COLLECTING_DESIRE: "💝 了解核心需求",
            ConversationState.COLLECTING_ENERGY_RITUAL: "⚡ 了解回血方式",
            ConversationState.COLLECTING_SUPPORT_NETWORK: "🤝 了解支持网络",
            ConversationState.INTRO_JUNGIAN_TEST: "🧠 引入荣格测试",
            ConversationState.IN_JUNGIAN_TEST: "🔬 荣格测试进行中",
            ConversationState.INTRO_EMOTION_GUIDANCE: "💡 引入情绪疏导",
            ConversationState.EMOTION_WHAT: "🔍 情绪识别阶段",
            ConversationState.EMOTION_COMPANION_CHOICE: "🤗 陪伴选择阶段",
            ConversationState.EMOTION_WHY: "🔎 根因分析阶段",
            ConversationState.EMOTION_HOW: "🛠️ 行动规划阶段",
            ConversationState.FREE_CHAT: "💬 自由聊天模式",
            ConversationState.EMERGENCY_MODE: "🚨 紧急干预模式"
        }
        return status_map.get(state, "🤔 未知状态")
    
    def clear_conversation():
        """清空对话并重置状态"""
        global conversation_manager
        conversation_manager.reset_session()
        return [] # 返回空列表清空聊天框
    
    def welcome_message():
        """初始欢迎消息"""
        return [{"role": "assistant", "content": "嗨～ 同学你好呀！我是你的校园小树洞喵呜🌳，很高兴在这里遇到你~ 随便说点什么，让我们开始愉快的聊天吧！✨"}]
    
    # 用户管理功能
    def create_new_user():
        """创建新用户的弹窗界面"""
        import random
        suggested_names = ["小可爱", "同学", "小伙伴", "萌新", "树洞朋友"]
        return gr.Textbox(
            placeholder=f"输入昵称，例如：{random.choice(suggested_names)}",
            label="✨ 新用户昵称",
            visible=True,
            interactive=True
        )
    
    def add_new_user_to_db(username):
        """将新用户添加到数据库并切换"""
        global current_user_id
        if username and username.strip():
            try:
                # 创建新用户到数据库
                new_user_id = db_manager.create_user(username.strip())
                current_user_id = new_user_id
                
                # 获取用户信息用于显示
                user_info = db_manager.get_user(new_user_id)
                
                # 重置聊天历史，显示欢迎消息
                welcome_msg = [{"role": "assistant", "content": f"嗨～ {username.strip()}，我是你的校园小树洞喵呜🌳！很高兴认识你呀~ 随便说点什么，让我们开始愉快的聊天吧！✨"}]
                
                return (
                    username.strip(),  # 更新当前用户显示
                    welcome_msg,  # 重置对话历史
                    f"✅ 已切换到新用户：{username.strip()}",  # 状态提示
                    ""  # 清空输入框
                )
            except Exception as e:
                print(f"❌ 创建用户失败: {e}")
                return (
                    current_user_display.value,  # 保持原用户显示 
                    chatbot.value,  # 保持原对话
                    f"❌ 创建用户失败，请重试",
                    username  # 保持输入
                )
        return current_user_display.value, chatbot.value, "❌ 请输入有效昵称", username
    
    def switch_to_user_list():
        """显示用户列表供切换"""
        try:
            users = db_manager.get_all_users()
            if users:
                user_choices = [(f"{user['nickname']} (ID:{user['id']})", user['id']) for user in users]
                return gr.Radio(
                    choices=user_choices,
                    label="👥 选择用户",
                    visible=True
                )
            else:
                return gr.Radio(choices=[], label="📝 暂无其他用户", visible=True)
        except Exception as e:
            print(f"❌ 获取用户列表失败: {e}")
            return gr.Radio(choices=[], label="❌ 获取用户列表失败", visible=True)
    
    def switch_user_by_id(selected_user_id):
        """根据用户ID切换用户 - 增强版，完整同步所有状态"""
        global current_user_id, current_conversation_state
        if selected_user_id:
            try:
                # 更新全局用户ID
                current_user_id = int(selected_user_id)
                
                # 获取用户信息
                user_info = db_manager.get_user(current_user_id)
                if user_info:
                    user_nickname = user_info.get('nickname', '同学')
                    
                    # 🚨 修复：同步用户的对话状态
                    from state_manager import get_conversation_state_from_string
                    current_conversation_state = get_conversation_state_from_string(
                        user_info.get('current_state', 'initial_greeting')
                    )
                    
                    # 加载用户的对话历史
                    conversation_history = db_manager.get_conversation_history(current_user_id, limit=20)
                    
                    # 🚨 修复：改进历史加载逻辑
                    if conversation_history and len(conversation_history) > 0:
                        # 转换为Gradio格式
                        chat_history = []
                        for msg in conversation_history:
                            chat_history.append({"role": msg['role'], "content": msg['content']})
                        
                        print(f"✅ 加载了 {len(chat_history)} 条历史对话记录")
                        
                        # 添加切换提示消息
                        chat_history.append({
                            "role": "assistant", 
                            "content": f"欢迎回来，{user_nickname}！我是你的校园小树洞喵呜🌳~ \n\n📚 已为你加载了最近的对话历史，我们可以继续之前的话题，或者聊点新的！✨"
                        })
                        
                    else:
                        # 新用户或无历史，显示欢迎消息
                        chat_history = [{"role": "assistant", "content": f"嗨～ {user_nickname}！我是你的校园小树洞喵呜🌳~ 很高兴认识你呀！来，和我说说心里话吧！✨"}]
                    
                    # 🚨 修复：更新状态显示
                    status_description = get_status_description(current_conversation_state)
                    
                    return (
                        user_nickname,  # 更新当前用户显示
                        chat_history,  # 加载对话历史
                        f"✅ 已切换到用户：{user_nickname} | {status_description}"  # 状态提示
                    )
                else:
                    return (None, None, "❌ 用户不存在")
            except Exception as e:
                print(f"❌ 切换用户失败: {e}")
                import traceback
                traceback.print_exc()
                return (None, None, f"❌ 切换失败：{str(e)}")
        return (None, None, "❌ 请选择有效用户")
    
    def switch_user(selected_user):
        return f"### 📋 当前用户信息\n**昵称**: {selected_user}\n**状态**: 活跃用户\n**性格类型**: 待测试\n**最后活动**: 刚刚"
    
    # MBTI测试相关函数
    def start_mbti_test():
        """开始MBTI测试"""
        from conversation_flow_modelscope import get_jungian_question, JUNG_ARCHETYPES_QUESTIONS
        
        question_text, question_data = get_jungian_question(0)
        if question_text:
            return (
                # 更新进度显示
                f"**测试进行中** 📊\n\n**第1题 / {len(JUNG_ARCHETYPES_QUESTIONS)}题**\n\n准备好了就选择A、B或C选项！", # 🚨 修改：更新题目总数和选项提示
                # 更新题目显示
                question_text,
                # 显示选项按钮
                gr.Row(visible=True),
                # 隐藏开始按钮，显示重置按钮
                gr.Button(visible=False),  # start_test_btn
                gr.Button(visible=True),   # reset_test_btn
                # 更新测试状态
                {"current_question": 0, "scores": {"E": 0, "I": 0, "S": 0, "N": 0, "T": 0, "F": 0, "J": 0, "P": 0}, "test_active": True},
                # 隐藏结果
                gr.Markdown(visible=False)
            )
        else:
            return "测试加载失败，请稍后重试。", "", gr.Row(visible=False), gr.Button(visible=True), gr.Button(visible=False), {}, gr.Markdown(visible=False)
    
    def process_test_answer(option, test_state):
        """处理测试答案 - Camel-AI优化版（集成数据库存储）"""
        global current_user_id # 🚨 关键修复：将global声明移至函数开头
        print(f"🔍 APP日志：进入process_test_answer函数，用户ID: {current_user_id}, 选项: {option}, 当前题目: {test_state.get('current_question')}") # 🚨 新增日志
        if not test_state.get("test_active", False):
            print("⚠️ APP日志：测试未激活，退出process_test_answer。") # 🚨 新增日志
            return test_state, gr.Markdown("", visible=False), gr.Row(visible=False), gr.Button(visible=True), gr.Button(visible=False), gr.Markdown(visible=False), gr.Markdown(visible=True)
        
        from conversation_flow_modelscope import JUNG_ARCHETYPES_QUESTIONS, process_jungian_answer, get_jungian_question, generate_detailed_jungian_analysis
        
        current_q = test_state["current_question"]
        
        # 处理当前问题的答案
        if current_q < len(JUNG_ARCHETYPES_QUESTIONS):
            question_data = JUNG_ARCHETYPES_QUESTIONS[current_q]
            scores_to_add = process_jungian_answer(option, question_data)
            
            # 更新分数
            for dimension, score in scores_to_add.items():
                test_state["scores"][dimension] += score
            print(f"🔍 APP日志：题目 {current_q + 1} 答案处理完成，更新后得分: {test_state['scores']}") # 🚨 新增日志
        
        # 移动到下一题
        next_q = current_q + 1
        test_state["current_question"] = next_q
        
        if next_q < len(JUNG_ARCHETYPES_QUESTIONS):
            # 继续下一题
            question_text, _ = get_jungian_question(next_q)
            print(f"🔍 APP日志：进入下一题 {next_q + 1} / {len(JUNG_ARCHETYPES_QUESTIONS)}") # 🚨 新增日志
            return (
                f"**测试进行中** 📊\n\n**第{next_q + 1}题 / {len(JUNG_ARCHETYPES_QUESTIONS)}题**\n\n继续选择A、B或C选项！", # 🚨 修改：更新题目总数和选项提示
                question_text,
                gr.Row(visible=True),
                gr.Button(visible=False),
                gr.Button(visible=True),
                test_state,
                gr.Markdown(visible=False)
            )
        else:
            # 🐪 测试完成，保存到数据库
            print(f"🔍 APP日志：荣格测试完成，准备保存结果。用户ID: {current_user_id}, 最终得分: {test_state['scores']}") # 🚨 新增日志
            test_state["test_active"] = False
            analysis = generate_detailed_jungian_analysis(test_state["scores"])
            
            # 保存测试结果到数据库
            try:
                question_answers = []
                for i in range(len(JUNG_ARCHETYPES_QUESTIONS)):
                    # 这里简化处理，实际可以记录每题的选择
                    question_answers.append({"question_index": i, "selected_option": "记录完整答案"})
                
                db_manager.save_jungian_test_result(
                    user_id=current_user_id,
                    question_answers=question_answers,
                    dimension_scores=test_state["scores"],
                    result_summary=analysis
                )
                
                # 🚨 关键修复：确保荣格得分同时保存到用户档案
                db_manager.update_user_profile(current_user_id, jungian_scores=test_state["scores"])
                
                # 更新用户对话状态为测试完成
                db_manager.update_user_state(current_user_id, ConversationState.JUNGIAN_TEST_COMPLETED.value)
                print(f"✅ APP日志：荣格八维测试结果已保存到数据库 (用户ID: {current_user_id})")
                print(f"✅ APP日志：用户档案中的荣格得分已更新: {test_state['scores']}")
            except Exception as e:
                print(f"❌ APP日志：荣格八维测试结果保存失败: {e}") # 🚨 新增异常日志
                import traceback
                traceback.print_exc() # 🚨 新增：打印堆栈跟踪以便调试
            
            return (
                f"**测试完成** ✅\n\n恭喜！你的MBTI类型分析已生成并保存",
                "### 🎉 测试完成！\n\n你的专业MBTI分析报告已经生成并永久保存，请查看右侧结果。\n\n想要重新测试吗？点击下方的重新测试按钮。",
                gr.Row(visible=False),
                gr.Button(visible=True),
                gr.Button(visible=True),
                test_state,
                gr.Markdown(value=analysis, visible=True)
            )
    
    def reset_mbti_test():
        """重置MBTI测试"""
        from conversation_flow_modelscope import JUNG_ARCHETYPES_QUESTIONS
        return (
            "**测试准备中** 📝\n\n点击开始按钮启动专业MBTI测试",
            f"""### 🎯 荣格八维性格测试\n\n{len(JUNG_ARCHETYPES_QUESTIONS)}题精准测试，了解你的认知功能和性格特点\n\n准备好了就点击"开始测试"按钮吧！""", # 🚨 修改：更新题目总数
            gr.Row(visible=False),
            gr.Button(visible=True),   # start_test_btn
            gr.Button(visible=False),  # reset_test_btn
            {"current_question": 0, "scores": {"E": 0, "I": 0, "S": 0, "N": 0, "T": 0, "F": 0, "J": 0, "P": 0}, "test_active": False},
            gr.Markdown(visible=False)
        )
    
    # 增强版系统思维工具函数 - 革命性升级
    def analyze_problem_what_enhanced(problem_text, emotions=None, context_info=None, urgency_level=None, user_id=1):
        """第一步：增强版问题分析 - 多维度智能分析"""
        if not problem_text.strip():
            return "🌟 请先描述一下你遇到的困扰，我会用专业的系统思维方法帮你分析～"

        # 获取用户档案信息
        user_profile = db_manager.get_user_profile(user_id) if 'db_manager' in globals() else None
        user_info = db_manager.get_user(user_id) if 'db_manager' in globals() else None

        # 构建增强版个性化上下文
        personalized_context = "=== 用户档案信息 ===\n"
        if user_info:
            nickname = user_info.get('nickname', '同学')
            personalized_context += f"昵称：{nickname}\n"

        if user_profile:
            if user_profile.get('current_emotion'):
                personalized_context += f"历史情绪状态：{user_profile['current_emotion']}\n"
            if user_profile.get('jungian_scores'):
                personalized_context += f"性格特征：{user_profile['jungian_scores']}\n"
            if user_profile.get('current_pressure_sources'):
                personalized_context += f"已知压力源：{user_profile['current_pressure_sources']}\n"

        # 添加当前分析的情绪信息
        if emotions:
            personalized_context += f"当前选择的情绪：{', '.join(emotions)}\n"

        # 添加背景信息
        if context_info:
            personalized_context += f"背景信息：{context_info}\n"

        # 添加紧急程度
        if urgency_level:
            personalized_context += f"紧急程度：{urgency_level}\n"
        
        analysis_messages = [
            {
                "role": "system", 
                "content": f"""你是温暖专业的心理陪伴者喵呜，同时也是系统思维专家。用户描述了困扰，请进行深度的"是什么"分析，帮助澄清问题本质。

{personalized_context}

请从以下角度分析：
1. 问题表面现象：用户描述的具体情况
2. 问题核心本质：隐藏在表面下的真正困扰
3. 情感层面：用户的感受和情绪
4. 影响范围：对生活、学习、人际关系的影响
5. 积极视角：问题中可能蕴含的成长机会
6. 个性化洞察：基于用户的性格特征提供针对性建议

语言风格要求温暖亲切，专业但有同理心，280-350字。"""
            },
            {
                "role": "user",
                "content": f"用户描述的困扰：{problem_text}"
            }
        ]
        
        try:
            analysis_result = get_modelscope_response(analysis_messages)

            # 生成智能推荐
            recommendations = generate_smart_recommendations(problem_text, emotions, user_profile)

            return f"""### 🔍 AI深度分析报告

{analysis_result}

---

### 🤖 智能推荐系统

{recommendations}

---

**🎉 太棒了！** 我们已经完成了问题的深度分析。现在你可以选择：
- 🔍 继续深入探索原因（推荐）
- 💭 先处理情绪状态
- 🎯 直接制定行动计划

**下一步建议：** 让我们深入探索"为什么"会出现这个问题，这将帮助你找到更有效的解决方案。"""

        except Exception as e:
            return generate_fallback_analysis(problem_text, emotions, urgency_level)
    
    def generate_smart_recommendations(problem_text, emotions, user_profile):
        """生成智能推荐"""
        recommendations = []

        # 基于情绪状态的推荐
        if emotions:
            if "😰 焦虑不安" in emotions or "😔 低落沮丧" in emotions:
                recommendations.append("🌬️ **情绪调节优先**：建议先进行深呼吸练习，稳定情绪状态")
            if "😠 愤怒生气" in emotions:
                recommendations.append("⚡ **能量转化**：将愤怒转化为改变的动力")
            if "🤔 困惑迷茫" in emotions:
                recommendations.append("🗺️ **结构化思考**：使用思维导图整理思路")

        # 基于问题类型的推荐
        if "学习" in problem_text or "作业" in problem_text:
            recommendations.append("📚 **学习策略**：考虑使用番茄工作法或时间块管理")
        if "人际" in problem_text or "关系" in problem_text:
            recommendations.append("🤝 **沟通技巧**：尝试非暴力沟通的方法")
        if "选择" in problem_text or "决定" in problem_text:
            recommendations.append("⚖️ **决策工具**：使用利弊分析或决策矩阵")

        # 基于用户档案的推荐
        if user_profile and user_profile.get('jungian_scores'):
            if 'I' in str(user_profile['jungian_scores']):
                recommendations.append("🧘 **内向优势**：利用独处时间进行深度思考")
            if 'E' in str(user_profile['jungian_scores']):
                recommendations.append("👥 **外向优势**：寻求他人的支持和讨论")

        if not recommendations:
            recommendations = [
                "🎯 **系统思维**：将问题分解为更小的可管理部分",
                "💪 **资源盘点**：识别你已有的优势和支持系统",
                "🌱 **成长视角**：将挑战视为学习和成长的机会"
            ]

        return "\n".join(f"- {rec}" for rec in recommendations[:3])

    def generate_fallback_analysis(problem_text, emotions, urgency_level):
        """生成备用分析结果"""
        emotion_text = f"，特别是{', '.join(emotions[:2])}的感受" if emotions else ""
        urgency_text = f"考虑到{urgency_level}，" if urgency_level else ""

        return f"""### 🔍 智能分析结果

我能深深感受到你现在的困扰{emotion_text}。{urgency_text}让我们一起来系统性地分析这个问题。

**🎯 问题核心识别：**
你描述的情况反映了当前状态与期望状态之间的差距，这种落差正在影响你的情绪和生活质量。

**💭 多维度分析：**
- **情绪层面**：{emotions[0] if emotions else '复杂的情绪状态'}正在影响你的判断和行动
- **认知层面**：可能存在一些思维模式需要调整
- **行为层面**：某些习惯或行为模式可能需要优化
- **环境层面**：外在因素也在发挥重要作用

**🌟 积极信号：**
你愿意主动寻求帮助和进行系统思考，这本身就是一个非常积极的信号！

**🚀 下一步建议：**
让我们继续深入探索"为什么"会出现这个问题，这将帮助我们找到更有针对性的解决方案。

---

**准备好继续探索了吗？** 🌟"""

    def analyze_problem_why_enhanced(event_text="", pain_text="", impact_text="",
                                   belief_text="", system_text="", user_id=1):
        """第二步：增强版原因分析 - 五层深度分析"""
        if not any([event_text.strip(), pain_text.strip(), impact_text.strip(),
                   belief_text.strip(), system_text.strip()]):
            return "🌟 请至少填写一个层面的内容，我们一起来深入探索～"

        # 获取用户档案信息
        user_profile = db_manager.get_user_profile(user_id) if 'db_manager' in globals() else None
        user_info = db_manager.get_user(user_id) if 'db_manager' in globals() else None

        # 构建增强版个性化上下文
        personalized_context = "=== 用户档案信息 ===\n"
        if user_info:
            nickname = user_info.get('nickname', '同学')
            personalized_context += f"昵称：{nickname}\n"

        if user_profile:
            if user_profile.get('current_emotion'):
                personalized_context += f"情绪状态：{user_profile['current_emotion']}\n"
            if user_profile.get('jungian_scores'):
                personalized_context += f"性格特征：{user_profile['jungian_scores']}\n"

        # 构建分析内容
        analysis_content = "=== 用户提供的分析内容 ===\n"
        if event_text.strip():
            analysis_content += f"表面事件：{event_text}\n"
        if pain_text.strip():
            analysis_content += f"情感痛点：{pain_text}\n"
        if impact_text.strip():
            analysis_content += f"影响范围：{impact_text}\n"
        if belief_text.strip():
            analysis_content += f"深层信念：{belief_text}\n"
        if system_text.strip():
            analysis_content += f"系统因素：{system_text}\n"

        # 构建增强版分析提示
        analysis_messages = [
            {
                "role": "system",
                "content": f"""你是世界顶级的心理学专家和系统思维大师。用户正在进行深度的"为什么"分析，已经提供了多层级的信息。

{personalized_context}

{analysis_content}

请进行革命性的深度分析，包括：

🔍 **根本原因分析**：
- 识别表面原因 vs 深层原因
- 分析因果关系链条
- 找出核心驱动因素

💭 **心理动力学探索**：
- 潜意识动机分析
- 防御机制识别
- 内在冲突解析

🧠 **认知模式深挖**：
- 限制性信念识别
- 思维偏差分析
- 自我叙事模式

🌍 **系统性因素整合**：
- 环境影响分析
- 社会文化因素
- 人际关系动力

🎯 **个性化洞察**：
- 基于用户特点的专门分析
- 模式识别和预测
- 转化机会识别

📋 **深度建议**：
- 3个核心洞察
- 2个突破方向
- 1个立即可行的认知调整

语言风格：深刻而温暖，专业而易懂，400-500字。"""
            },
            {
                "role": "user",
                "content": "请基于以上信息进行深度的原因分析。"
            }
        ]

        try:
            analysis_result = get_modelscope_response(analysis_messages)

            # 生成思维导图建议
            mindmap_suggestion = generate_mindmap_suggestion(event_text, pain_text, impact_text, belief_text, system_text)

            return f"""### 🤔 深度原因分析报告

{analysis_result}

---

### 🗺️ 思维导图建议

{mindmap_suggestion}

---

**🎉 太棒了！** 我们已经深入探索了问题的根本原因。现在你可以选择：
- 💡 制定针对性的行动方案（推荐）
- 🔄 进一步深化某个层面的分析
- 💭 先处理发现的核心情绪

**下一步建议：** 基于这些深刻洞察，让我们制定一个全面而可行的行动计划。"""

        except Exception as e:
            return generate_fallback_why_analysis(event_text, pain_text, impact_text, belief_text, system_text)

    def generate_mindmap_suggestion(event_text, pain_text, impact_text, belief_text, system_text):
        """生成思维导图建议"""
        mindmap_elements = []

        if event_text.strip():
            mindmap_elements.append("📅 **触发事件** → 具体情况和时间线")
        if pain_text.strip():
            mindmap_elements.append("💔 **核心痛点** → 最深层的情感反应")
        if impact_text.strip():
            mindmap_elements.append("🌊 **影响范围** → 对生活各方面的具体影响")
        if belief_text.strip():
            mindmap_elements.append("🧠 **深层信念** → 潜在的思维模式和假设")
        if system_text.strip():
            mindmap_elements.append("🌍 **系统因素** → 环境和外在条件")

        if not mindmap_elements:
            mindmap_elements = [
                "🎯 **问题核心** → 主要困扰",
                "🔍 **原因分析** → 可能的成因",
                "💡 **解决方向** → 潜在的改善路径"
            ]

        mindmap_text = "\n".join(f"- {element}" for element in mindmap_elements)

        return f"""**建议的思维导图结构：**

{mindmap_text}

💡 **使用建议：** 你可以将这些要素画成思维导图，用线条连接相关元素，这样能更清晰地看到问题的全貌和内在联系。"""

    def generate_fallback_why_analysis(event_text, pain_text, impact_text, belief_text, system_text):
        """生成备用的为什么分析"""
        filled_sections = []
        if event_text.strip():
            filled_sections.append("触发事件")
        if pain_text.strip():
            filled_sections.append("情感痛点")
        if impact_text.strip():
            filled_sections.append("影响范围")
        if belief_text.strip():
            filled_sections.append("深层信念")
        if system_text.strip():
            filled_sections.append("系统因素")

        return f"""### 🤔 深度原因分析

感谢你在{', '.join(filled_sections)}方面的深入思考！让我来帮你整合这些信息。

**🔍 综合分析：**
基于你提供的信息，我们可以看到这个问题具有多层次的复杂性：

- **表面层面**：具体的事件和情况触发了问题
- **情感层面**：深层的情感反应和痛点被激活
- **认知层面**：某些信念和思维模式在发挥作用
- **系统层面**：环境和外在因素也在产生影响

**💡 关键洞察：**
问题往往不是单一原因造成的，而是多个因素相互作用的结果。理解这种复杂性本身就是解决问题的第一步。

**🌟 积极发现：**
你能够从多个角度分析问题，这显示了很强的自我觉察能力和系统思维能力！

**🚀 下一步：**
现在让我们基于这些深刻的洞察，制定一个全面而可行的行动方案。

---

**准备好制定行动计划了吗？** ✨"""

    def analyze_problem_how_enhanced(immediate_action="", short_term_plan="", medium_term_plan="",
                                   support_people=None, support_tools=None, environment_opt="",
                                   obstacles="", coping_strategies="", emergency_contact="", user_id=1):
        """第三步：增强版行动方案制定 - 全方位智能规划"""
        if not any([immediate_action.strip(), short_term_plan.strip(), medium_term_plan.strip()]):
            return "🌟 请至少填写一个行动计划，我们一起来制定完整的方案～"

        # 获取用户档案信息
        user_profile = db_manager.get_user_profile(user_id) if 'db_manager' in globals() else None
        user_info = db_manager.get_user(user_id) if 'db_manager' in globals() else None

        # 构建个性化上下文
        personalized_context = "=== 用户档案信息 ===\n"
        if user_info:
            nickname = user_info.get('nickname', '同学')
            personalized_context += f"昵称：{nickname}\n"

        if user_profile:
            if user_profile.get('jungian_scores'):
                personalized_context += f"性格特征：{user_profile['jungian_scores']}\n"
            if user_profile.get('current_pressure_sources'):
                personalized_context += f"压力源：{user_profile['current_pressure_sources']}\n"

        # 构建行动计划内容
        action_content = "=== 用户制定的行动计划 ===\n"
        if immediate_action.strip():
            action_content += f"即时行动：{immediate_action}\n"
        if short_term_plan.strip():
            action_content += f"短期计划：{short_term_plan}\n"
        if medium_term_plan.strip():
            action_content += f"中期规划：{medium_term_plan}\n"
        if support_people:
            action_content += f"人际支持：{', '.join(support_people)}\n"
        if support_tools:
            action_content += f"工具支持：{', '.join(support_tools)}\n"
        if environment_opt.strip():
            action_content += f"环境优化：{environment_opt}\n"
        if obstacles.strip():
            action_content += f"预见障碍：{obstacles}\n"
        if coping_strategies.strip():
            action_content += f"应对策略：{coping_strategies}\n"
        if emergency_contact.strip():
            action_content += f"紧急联系：{emergency_contact}\n"

        # 构建增强版分析提示
        analysis_messages = [
            {
                "role": "system",
                "content": f"""你是世界顶级的行动规划专家和执行教练。用户已经完成了深度的问题分析，现在制定了行动方案，需要你的专业指导。

{personalized_context}

{action_content}

请提供革命性的行动方案优化，包括：

🎯 **方案评估与优化**：
- 评估计划的可行性和有效性
- 识别潜在的改进空间
- 提供具体的优化建议

⚡ **执行策略设计**：
- 制定详细的执行步骤
- 设计激励和奖励机制
- 建立进度跟踪系统

🛡️ **风险管理**：
- 识别执行过程中的风险点
- 制定应急预案
- 建立支持系统

📊 **成功指标**：
- 设定可衡量的成功标准
- 建立反馈机制
- 制定调整策略

🌟 **个性化建议**：
- 基于用户特点的专门建议
- 利用个人优势的策略
- 克服个人弱点的方法

🚀 **行动激励**：
- 3个核心执行要点
- 2个关键成功因素
- 1个立即开始的第一步

语言风格：实用而鼓舞人心，专业而温暖，450-550字。"""
            },
            {
                "role": "user",
                "content": "请基于我的行动计划提供专业的优化建议和执行指导。"
            }
        ]

        try:
            analysis_result = get_modelscope_response(analysis_messages)

            # 生成执行跟踪建议
            tracking_suggestion = generate_tracking_suggestion(immediate_action, short_term_plan, medium_term_plan)

            return f"""### 💡 智能行动方案优化报告

{analysis_result}

---

### 📊 执行跟踪建议

{tracking_suggestion}

---

### 🎉 恭喜完成系统思维全流程！

你已经完成了一次完整的系统思维之旅：
- ✅ **问题澄清** - 深入理解了困扰的本质
- ✅ **原因分析** - 多层级探索了根本原因
- ✅ **方案制定** - 制定了全面的行动计划

**🌟 现在你拥有的宝藏：**
- 🔍 清晰的问题认识
- 💡 深层的原因洞察
- 🎯 具体的行动方案
- 🛡️ 完善的支持系统

**记住：** 最小的行动胜过最完美的计划！从今天开始，选择一个最简单的小步骤，相信自己，你一定可以的！💪

**我会一直在这里支持你～** 如果在执行过程中遇到困难，随时回来找我聊聊哦！🌟"""

        except Exception as e:
            return generate_fallback_how_analysis(immediate_action, short_term_plan, medium_term_plan)

    def generate_tracking_suggestion(immediate_action, short_term_plan, medium_term_plan):
        """生成执行跟踪建议"""
        suggestions = []

        if immediate_action.strip():
            suggestions.append("📅 **今日行动跟踪**：设置手机提醒，记录完成情况")

        if short_term_plan.strip():
            suggestions.append("📊 **周度进展评估**：每周回顾进展，调整策略")

        if medium_term_plan.strip():
            suggestions.append("🎯 **月度目标检视**：每月评估目标达成情况")

        suggestions.extend([
            "💪 **成就庆祝**：完成小目标时给自己一个奖励",
            "🔄 **灵活调整**：根据实际情况适时调整计划",
            "🤝 **寻求支持**：遇到困难时主动寻求帮助"
        ])

        return "\n".join(f"- {suggestion}" for suggestion in suggestions[:4])

    def generate_fallback_how_analysis(immediate_action, short_term_plan, medium_term_plan):
        """生成备用的怎么办分析"""
        filled_plans = []
        if immediate_action.strip():
            filled_plans.append("即时行动")
        if short_term_plan.strip():
            filled_plans.append("短期计划")
        if medium_term_plan.strip():
            filled_plans.append("中期规划")

        return f"""### 💡 行动方案优化建议

太棒了！你在{', '.join(filled_plans)}方面都有了具体的想法。让我来帮你优化这个方案。

**🎯 方案亮点：**
你的计划显示了很好的时间规划意识和系统思维能力，这是成功执行的重要基础！

**💪 执行建议：**
- **从小处开始**：选择最容易的行动作为突破口
- **建立节奏**：设定固定的执行时间和地点
- **记录进展**：用简单的方式跟踪你的进步
- **庆祝成就**：每个小进步都值得庆祝

**🌟 成功要素：**
1. **坚持比完美更重要** - 每天一小步胜过偶尔一大步
2. **灵活调整策略** - 根据实际情况适时调整计划
3. **寻求支持帮助** - 不要独自承担所有压力

**🚀 立即开始：**
选择一个今天就能做的最小行动，现在就开始！

---

**🎉 恭喜你完成了完整的系统思维分析！**
记住，行动比完美更重要。相信自己，你一定可以的！✨"""

    def analyze_problem_why(event_text, pain_text, impact_text, user_id=1):
        """第二步：分析原因 - 三层深入分析（个性化版）"""
        if not any([event_text.strip(), pain_text.strip(), impact_text.strip()]):
            return "喵~ 请至少填写一个层面的内容，我们一起来探索～"
        
        # 获取用户档案信息
        user_profile = db_manager.get_user_profile(user_id) if 'db_manager' in globals() else None
        user_info = db_manager.get_user(user_id) if 'db_manager' in globals() else None
        
        # 构建个性化系统提示
        personalized_context = ""
        if user_info:
            nickname = user_info.get('nickname', '同学')
            personalized_context += f"用户昵称：{nickname}\n"
        
        if user_profile:
            if user_profile.get('current_emotion'):
                personalized_context += f"当前情绪：{user_profile['current_emotion']}\n"
            if user_profile.get('jungian_scores'):
                personalized_context += f"性格特征：{user_profile['jungian_scores']}\n"
        
        analysis_content = f"""
用户的三层分析：
【事件层】具体触发事件：{event_text.strip() if event_text.strip() else '（未填写）'}
【痛点层】核心痛点：{pain_text.strip() if pain_text.strip() else '（未填写）'}
【影响层】具体影响：{impact_text.strip() if impact_text.strip() else '（未填写）'}
"""
        
        analysis_messages = [
            {
                "role": "system", 
                "content": f"""你是温暖专业的心理陪伴者喵呜，擅长系统思维分析。用户从三个层面分享了对问题原因的思考，请进行深度的"为什么"分析。

{personalized_context}

请从以下角度深入分析：
1. 表层原因：直接触发因素和表面现象
2. 深层原因：心理、认知、行为模式的根源
3. 系统原因：环境、关系、文化背景的影响
4. 情感根源：内心深处的恐惧、需求、价值观冲突
5. 成长机会：从问题中发现的学习和成长空间
6. 个性化洞察：基于用户的性格特征分析行为模式

语言风格要求温暖亲切，专业但有同理心，300-400字。"""
            },
            {
                "role": "user",
                "content": analysis_content
            }
        ]
        
        try:
            analysis_result = get_modelscope_response(analysis_messages)
            return f"### 🤔 让我们深入探索原因\n\n{analysis_result}"
        except Exception as e:
            return f"### 🤔 让我们深入探索原因\n\n你在三个层面的思考真的很有洞察力！我能感受到你对这个问题的深入分析。🌟\n\n**让我们一起梳理一下：**\n\n你从事件、痛点、影响三个维度的分析触及了问题的核心。其实，很多时候困扰的产生都有多层原因：\n\n💭 **直观层面**：我们能直接感受到的触发因素\n🔄 **习惯层面**：日复一日的行为模式在发挥作用\n🧠 **认知层面**：我们对事情的理解和期待影响着感受\n💝 **深层需求**：内心真正渴望的可能没有得到满足\n\n**💪 最重要的是**：你愿意去思考和面对，这已经是解决问题的第一步了！\n\n---\n\n**🌟 太棒了！** 现在让我们制定解决方案吧！"
    
    def analyze_problem_how(capability_text, mini_task_text, support_text="", user_id=1):
        """第三步：制定解决方案（个性化版）"""
        if not any([capability_text.strip(), mini_task_text.strip()]):
            return "喵~ 说说你的想法吧，哪怕是很小的改变念头也很棒呢！"
        
        # 获取用户档案信息
        user_profile = db_manager.get_user_profile(user_id) if 'db_manager' in globals() else None
        user_info = db_manager.get_user(user_id) if 'db_manager' in globals() else None
        
        # 构建个性化系统提示
        personalized_context = ""
        if user_info:
            nickname = user_info.get('nickname', '同学')
            personalized_context += f"用户昵称：{nickname}\n"
        
        if user_profile:
            if user_profile.get('current_emotion'):
                personalized_context += f"当前情绪：{user_profile['current_emotion']}\n"
            if user_profile.get('jungian_scores'):
                personalized_context += f"性格特征：{user_profile['jungian_scores']}\n"
        
        action_content = f"""
用户的行动思考：
【能力范围】我能做的事：{capability_text.strip() if capability_text.strip() else '（未填写）'}
【Mini任务】明天的具体任务：{mini_task_text.strip() if mini_task_text.strip() else '（未填写）'}
【支持系统】需要的支持：{support_text.strip() if support_text.strip() else '（未填写）'}
"""
 
        analysis_messages = [
            {
                "role": "system",
                "content": f"""你是温暖专业的心理陪伴者喵呜，同时是优秀的行动教练。用户提出了解决方案的想法，请帮助制定温暖而实用的行动计划。

{personalized_context}

请从以下角度制定计划：
1. 立即行动：今天就能开始的小步骤
2. 短期目标：本周可以完成的具体任务
3. 中期规划：本月要建立的习惯和模式
4. 长期愿景：持续改进和成长的方向
5. 支持系统：需要的资源和帮助
6. 成功要素：关键的成功因素和注意事项
7. 个性化建议：基于用户的性格特征提供适合的行动方式

语言风格要求温暖有力量，实用可操作，350-450字。"""
            },
            {
                "role": "user", 
                "content": action_content
            }
        ]
        
        try:
            analysis_result = get_modelscope_response(analysis_messages)
            return f"### 💡 让我们一起制定行动计划\n\n{analysis_result}\n\n---\n\n🎉 **哇！我们完成了一次完整的系统思维之旅！**\n\n**看看你现在拥有的宝藏：**\n✨ **清晰的问题认识** - 不再迷茫困惑\n🔍 **深层的原因洞察** - 找到了问题的根源\n🚀 **具体的行动方案** - 有了明确的改变方向\n\n**记住：** 最小的行动胜过最完美的计划！从今天开始，选择一个最简单的小步骤，相信自己，你一定可以的！💪\n\n**我会一直在这里支持你～** 如果在行动过程中遇到困难，随时回来找我聊聊哦！🌟"
        except Exception as e:
            return f"### 💡 让我们一起制定行动计划\n\n你的想法真的很棒！我能感受到你想要改变的决心和勇气。🌟\n\n**让我们把想法变成行动：**\n\n🎯 **优化你的方案**：你的核心思路很对，我们可以让它更具体一些\n📅 **今天就能开始**：从最容易的小行动开始\n🛠️ **实用小工具**：记录每天的小进步\n💪 **记住**：你已经迈出了最难的一步——意识到问题并愿意改变。\n\n---\n\n🎉 **恭喜完成系统思维分析！** 现在就开始行动吧！我相信你一定可以的！✨"

    # 增强版系统思维流程控制函数
    def start_systematic_analysis_enhanced(user_id=1):
        """开始增强版系统化分析"""
        # 获取用户档案信息
        user_profile = db_manager.get_user_profile(user_id) if 'db_manager' in globals() else None
        user_info = db_manager.get_user(user_id) if 'db_manager' in globals() else None

        # 构建个性化引导信息
        nickname = user_info.get('nickname', '同学') if user_info else '同学'
        personalized_guidance = f"🌟 太好了{nickname}！欢迎来到增强版系统思维工作区。"

        if user_profile:
            if user_profile.get('current_emotion'):
                personalized_guidance += f"\n\n💭 我注意到你之前的情绪状态是{user_profile['current_emotion']}，这将帮助我们更好地理解你的困扰。"
            if user_profile.get('jungian_scores'):
                personalized_guidance += f"\n\n🧠 基于你的性格特征，我会为你提供个性化的思维引导。"

        personalized_guidance += f"""

### 🚀 准备开始深度探索

我们将使用革命性的多维度分析方法，包括：
- 🔍 **智能问题识别** - AI辅助的深度分析
- 🤔 **五层原因探索** - 从表面到深层的全面剖析
- 💡 **智能行动规划** - 个性化的解决方案制定
- 📊 **可视化跟踪** - 进度监控和效果评估

**你准备好开始这段探索之旅了吗？** ✨"""

        return (
            gr.update(visible=True),  # what_section
            gr.update(visible=False), # why_section
            gr.update(visible=False), # how_section
            gr.update(visible=True),  # progress_system
            personalized_guidance,    # thinking_analysis
            gr.update(visible=True)   # user_profile_display
        )

    def update_progress_visualization(current_step="start"):
        """更新进度可视化"""
        steps = ["start", "what", "why", "how", "complete"]
        current_index = steps.index(current_step) if current_step in steps else 0

        progress_html = '''
        <div style="display: flex; justify-content: center; align-items: center; margin: 20px 0; padding: 20px;
                    background: linear-gradient(135deg, rgba(255, 248, 240, 0.8), rgba(255, 248, 240, 0.6));
                    border-radius: 15px; border: 2px solid rgba(182, 211, 226, 0.3);">
            <div class="thinking-progress-track">
        '''

        step_info = {
            "start": ("🎯", "开始"),
            "what": ("🔍", "是什么"),
            "why": ("🤔", "为什么"),
            "how": ("💡", "怎么办"),
            "complete": ("🎉", "完成")
        }

        for i, step in enumerate(steps):
            icon, label = step_info[step]
            active_class = "active" if i <= current_index else ""

            progress_html += f'''
                <div class="progress-step {active_class}" data-step="{step}">
                    <div class="step-circle">{icon}</div>
                    <div class="step-label">{label}</div>
                </div>
            '''

            if i < len(steps) - 1:
                progress_html += '<div class="progress-connector"></div>'

        progress_html += '''
            </div>
        </div>
        '''

        return progress_html

    def proceed_to_why_analysis_enhanced(problem_text, emotions, context_info, urgency_level):
        """进入增强版为什么分析"""
        if not problem_text.strip():
            return (
                gr.update(visible=False),  # why_section
                "请先完成问题描述再进入原因分析阶段。",  # current_step_info
                update_progress_visualization("what")  # progress_visualization
            )

        guidance_text = f"""
### 🤔 很好！现在让我们深入探索原因

基于你描述的问题："{problem_text[:50]}..."

我们已经完成了问题的初步分析。现在可以选择不同的方式来探索背后的原因：

**🎯 选择最适合你当前状态的分析方式**
"""

        return (
            gr.update(visible=True),   # why_section
            guidance_text,             # current_step_info
            update_progress_visualization("why")  # progress_visualization
        )

    def proceed_to_how_analysis_enhanced(analysis_data):
        """进入增强版怎么办分析"""
        guidance_text = """
### 💡 太棒了！现在让我们制定行动方案

基于前面的深度分析，我们已经获得了宝贵的洞察。现在让我们将这些洞察转化为具体的行动计划。

**🚀 准备开始制定你的专属行动方案**
"""

        return (
            gr.update(visible=True),   # how_section
            guidance_text,             # current_step_info
            update_progress_visualization("how")  # progress_visualization
        )
        
        personalized_guidance += "\n\n请先描述一下你遇到的困扰～"
        
        return (
            gr.update(visible=True),  # what_section
            gr.update(visible=False),  # why_section
            gr.update(visible=False),  # how_section
            gr.update(visible=True),   # progress_indicator
            personalized_guidance
        )
    
    def get_user_profile_for_thinking(user_id=1):
        """获取用户档案信息用于系统思维"""
        if 'db_manager' not in globals():
            return ""
        
        user_profile = db_manager.get_user_profile(user_id)
        user_info = db_manager.get_user(user_id)
        
        if not user_info:
            return ""
        
        profile_parts = []
        nickname = user_info.get('nickname', '同学')
        profile_parts.append(f"**昵称：** {nickname}")
        
        if user_profile:
            if user_profile.get('current_emotion'):
                profile_parts.append(f"**当前情绪：** {user_profile['current_emotion']}")
            if user_profile.get('jungian_scores'):
                profile_parts.append(f"**性格特征：** {user_profile['jungian_scores']}")
            if user_profile.get('current_pressure_sources'):
                profile_parts.append(f"**压力源：** {user_profile['current_pressure_sources']}")
        
        if profile_parts:
            return f"### 🧠 你的个人档案\n\n" + "\n\n".join(profile_parts) + "\n\n---\n\n"
        return ""
    
    def get_conversation_history_for_thinking(user_id=1, limit=3):
        """获取相关对话历史用于系统思维"""
        if 'db_manager' not in globals():
            return ""
        
        try:
            history = db_manager.get_conversation_history(user_id, limit=limit)
            if not history:
                return ""
            
            history_parts = []
            for msg in history[-limit:]:
                role = "👤 你" if msg['role'] == 'user' else "🤖 喵呜"
                content = msg['content'][:100] + "..." if len(msg['content']) > 100 else msg['content']
                history_parts.append(f"**{role}：** {content}")
            
            if history_parts:
                return f"### 💭 相关对话回顾\n\n" + "\n\n".join(history_parts) + "\n\n---\n\n"
        except Exception as e:
            print(f"获取对话历史失败: {e}")
        
        return ""

    def start_casual_chat():
        """开始轻松交流"""
        return (
            gr.update(visible=True),  # what_section
            gr.update(visible=False),  # why_section
            gr.update(visible=False),  # how_section
            gr.update(visible=True),   # progress_indicator
            "💬 好的！让我们轻松地聊聊，想到什么就说什么，我会认真听的～"
        )

    def start_free_discussion():
        """开始自由交流"""
        return (
            gr.update(visible=True),  # what_section
            gr.update(visible=False),  # why_section
            gr.update(visible=False),  # how_section
            gr.update(visible=True),   # progress_indicator
            "🎈 太棒了！让我们随心所欲地交流，你想聊什么都可以～"
        )

    def proceed_to_why_analysis(problem_text):
        """进入为什么分析阶段"""
        if not problem_text.strip():
            return (
                gr.update(visible=True),   # why_guidance
                gr.update(visible=True),   # why_choice_group
                gr.update(visible=False),  # reason_analysis_area
                gr.update(visible=False),  # listening_area
                gr.update(visible=False),  # quiet_area
                "喵~ 请先描述一下你的困扰，我们再继续分析～"
            )
        
        return (
            gr.update(visible=True),   # why_guidance
            gr.update(visible=True),   # why_choice_group
            gr.update(visible=False),  # reason_analysis_area
            gr.update(visible=False),  # listening_area
            gr.update(visible=False),  # quiet_area
            f"很好！我们已经识别了问题的核心：**{problem_text[:50]}{'...' if len(problem_text) > 50 else ''}**\n\n现在让我们深入探索原因，你希望怎么继续？"
        )

    def choose_listening_mode():
        """选择倾听模式"""
        return (
            gr.update(visible=False),  # why_choice_group
            gr.update(visible=True),   # listening_area
            gr.update(visible=False),  # quiet_area
            "💙 我在这里，安心地说出你的感受吧～没有对错，没有评判，只有倾听和理解。"
        )

    def choose_quiet_mode():
        """选择安静模式"""
        return (
            gr.update(visible=False),  # why_choice_group
            gr.update(visible=False),  # listening_area
            gr.update(visible=True),   # quiet_area
            "☕️ 让我们一起安静一会儿吧...有时候什么都不想，就这样静静地待着，也是很好的。"
        )

    def choose_analysis_mode():
        """选择分析模式"""
        return (
            gr.update(visible=False),  # why_choice_group
            gr.update(visible=False),  # listening_area
            gr.update(visible=False),  # quiet_area
            gr.update(visible=True),   # reason_analysis_area
            "🔍 太棒了！让我们一起探索问题背后的原因～我们会从三个层面来深入分析。"
        )

    def proceed_to_how_analysis(event_text, pain_text, impact_text):
        """进入怎么办分析阶段"""
        if not any([event_text.strip(), pain_text.strip(), impact_text.strip()]):
            return (
                gr.update(visible=True),   # action_prep
                gr.update(visible=False),  # solution_area
                "喵~ 请至少填写一个层面的内容，我们一起来探索～"
            )
        
        return (
            gr.update(visible=True),   # action_prep
            gr.update(visible=False),  # solution_area
            "🌟 太棒了！我们已经深入了解了问题和原因。现在让我们为制定行动方案做个小小的准备。"
        )

    def skip_preparation():
        """跳过准备阶段"""
        return (
            gr.update(visible=False),  # action_prep
            gr.update(visible=True),   # solution_area
            "💪 好的！让我们直接开始制定行动计划。"
        )

    def start_solution_planning():
        """开始解决方案制定"""
        return (
            gr.update(visible=False),  # action_prep
            gr.update(visible=True),   # solution_area
            "🎯 现在让我们一步步来制定行动计划，记住，即使是很小的事情都可以！"
        )

    def reset_what_step():
        """重置是什么步骤"""
        return (
            gr.update(value=""),  # problem_input
            "🔄 好的，让我们重新开始。请描述一下你遇到的困扰～"
        )

    def reset_why_step():
        """重置为什么步骤"""
        return (
            gr.update(value=""),  # event_input
            gr.update(value=""),  # pain_input
            gr.update(value=""),  # impact_input
            "🔄 好的，让我们重新填写原因分析～"
        )

    def reset_how_step():
        """重置怎么办步骤"""
        return (
            gr.update(value=""),  # capability_input
            gr.update(value=""),  # mini_task_input
            "🔄 好的，让我们重新思考行动方案～"
        )

    def handle_listening_input(listening_text):
        """处理倾听输入"""
        if not listening_text.strip():
            return "💙 我在这里，想到什么就说什么，我会认真听的..."
        
        return f"""
        💝 **我听到了你的心声...**
        
        {listening_text}
        
        **我的感受：**
        谢谢你愿意和我分享这些。我能感受到你内心的复杂情感，这些感受都是真实而重要的。
        
        **我想对你说：**
        - 你的感受是值得被理解和尊重的
        - 愿意表达出来本身就是一种勇气
        - 我会一直在这里陪伴你
        
        **如果你愿意，我们可以：**
        - 继续深入探索这些感受
        - 换个话题轻松一下
        - 或者就这样安静地待一会儿
        
        你希望怎么继续呢？💙
        """

    def back_to_why_analysis():
        """回到原因分析"""
        return (
            gr.update(visible=True),   # why_choice_group
            gr.update(visible=False),  # listening_area
            gr.update(visible=False),  # quiet_area
            "🔙 好的，让我们回到原因分析。你希望怎么继续？"
        )
    
    # MCP社区功能函数
    def get_formatted_psychology_recommendations():
        """获取格式化的积极心理学推荐"""
        try:
            global community_manager
            if community_manager is None:
                community_manager = get_community_manager(db_manager)
            
            recommendations = community_manager.get_psychology_content_recommendations(
                user_id=current_user_id, limit=5
            )
            return recommendations
        except Exception as e:
            print(f"⚠️ 获取积极心理学推荐失败: {e}")
            return "🐱 喵~ 暂时无法获取推荐内容，请稍后再试哦~"
    
    def get_formatted_game_recommendations():
        """获取格式化的心理健康小游戏推荐"""
        try:
            global community_manager
            if community_manager is None:
                community_manager = get_community_manager(db_manager)
            
            recommendations = community_manager.get_mental_health_game_recommendations(
                user_id=current_user_id, limit=3
            )
            return recommendations
        except Exception as e:
            print(f"⚠️ 获取心理健康小游戏推荐失败: {e}")
            return "🐱 喵~ 暂时无法获取游戏推荐，请稍后再试哦~"

    def get_all_community_posts_formatted():
        """获取并格式化所有社区帖子"""
        try:
            global community_manager, current_user_id
            if community_manager is None:
                community_manager = get_community_manager(db_manager)

            posts = community_manager.get_community_posts(limit=10)  # 获取最新10条帖子
            if not posts:
                return "## 📋 社区广场\n\n喵~ 社区里还没有帖子呢，快去发布你的第一个求助吧！✨"

            formatted_posts = "## 📋 社区广场\n\n"
            for i, post in enumerate(posts, 1):
                formatted_posts += f"""### {i}. **{post['title']}**
**类型**: {post.get('post_type', '未知')} | **发帖人**: {post['anonymous_name']}
**内容**: {post['content'][:100]}...
**发布时间**: {post['time_ago']}
---

"""
            return formatted_posts
        except Exception as e:
            print(f"⚠️ 获取社区帖子失败: {e}")
            return f"## ⚠️ 社区帖子获取失败\n\n抱歉，获取社区帖子时发生错误：{e}\n\n请稍后重试。"

    def publish_new_post(title, content, tags_str):
        """发布新帖子到社区"""
        global community_manager, current_user_id
        if not title.strip() or not content.strip():
            return "❌ 标题和内容不能为空！", gr.Textbox(value=""), gr.Textbox(value=""), gr.Textbox(value="")

        if current_user_id is None:
            return "❌ 请先创建或切换用户才能发布帖子！", gr.Textbox(value=""), gr.Textbox(value=""), gr.Textbox(value="")

        try:
            if community_manager is None:
                community_manager = get_community_manager(db_manager)
            
            tags = [t.strip() for t in tags_str.split(',')] if tags_str.strip() else []
            result = community_manager.publish_post(
                user_id=current_user_id,
                title=title.strip(),
                content=content.strip(),
                post_type="help_request", # 默认为求助帖
                tags=tags
            )
            
            if result["success"]:
                return result["message"], gr.Textbox(value=""), gr.Textbox(value=""), gr.Textbox(value="")
            else:
                return f"❌ 发布失败: {result['message']}", title, content, tags_str
        except Exception as e:
            print(f"⚠️ 发布帖子失败: {e}")
            return f"❌ 发布失败：{e}", title, content, tags_str

    def get_my_help_requests_formatted_func():
        """获取并格式化用户自己的求助记录"""
        global community_manager, current_user_id
        if current_user_id is None:
            return "## 📝 我的求助\n\n喵~ 请先创建或切换用户才能查看你的求助记录哦！✨"
        
        try:
            if community_manager is None:
                community_manager = get_community_manager(db_manager)
            
            return community_manager.get_my_help_requests(current_user_id, limit=10)
        except Exception as e:
            print(f"⚠️ 获取我的求助记录失败: {e}")
            return f"## ⚠️ 我的求助获取失败\n\n抱歉，获取你的求助记录时发生错误：{e}\n\n请稍后重试。"

    # 🚨 移动：用户管理功能相关的函数定义，确保它们在事件绑定前被定义
    def handle_user_switch(selected):
        """处理用户切换逻辑，确保历史正确加载"""
        if selected:
            result = switch_user_by_id(selected)
            print(f"🔄 用户切换结果: {result[0] if result[0] else 'None'}, 历史条数: {len(result[1]) if result[1] else 0}")
            # 🚨 修复：返回4个值，包括关闭模态框
            return result[0], result[1], result[2], gr.Row(visible=False)
        return (None, [], "❌ 请选择用户", gr.Row(visible=False))
    
    def show_delete_confirmation(selected_user_id):
        """显示删除用户确认信息"""
        if not selected_user_id:
            return gr.Row(visible=False), ""
        
        try:
            # 获取用户数据摘要
            summary = db_manager.get_user_data_summary(int(selected_user_id))
            
            if "error" in summary:
                return gr.Row(visible=False), f"❌ 错误：{summary['error']}"
            
            # 格式化显示信息
            info_text = f"""### 📊 用户数据摘要\n\n**👤 用户信息**\n- **用户ID**: {summary['user_id']}\n- **昵称**: {summary['nickname']}\n- **创建时间**: {summary['created_at']}\n\n**📈 数据统计**\n- **对话记录**: {summary['data_stats']['conversations']} 条\n- **荣格测试**: {summary['data_stats']['jungian_tests']} 次\n- **幸福日志**: {summary['data_stats']['happiness_logs']} 条\n- **情绪日记**: {summary['data_stats']['diary_entries']} 条\n- **用户喜好**: {summary['data_stats']['preferences']} 项\n\n**📊 总数据量**: {summary['total_records']} 条记录\n\n⚠️ **删除后将永久清除以上所有数据，此操作不可撤销！**"""
            
            return gr.Row(visible=True), info_text
            
        except Exception as e:
            print(f"❌ 获取删除确认信息失败: {e}")
            return gr.Row(visible=False), f"❌ 获取用户信息失败：{str(e)}"
    
    def delete_user_confirmed(selected_user_id):
        """确认删除用户"""
        global current_user_id, current_conversation_state
        
        if not selected_user_id:
            return (None, [], "❌ 未选择用户")
        
        try:
            user_id_to_delete = int(selected_user_id)
            
            # 🚨 安全检查：不能删除当前正在使用的用户
            if user_id_to_delete == current_user_id:
                return (None, [], "❌ 不能删除当前使用的用户，请先切换到其他用户")
            
            # 执行删除
            success = db_manager.delete_user(user_id_to_delete)
            
            if success:
                # 删除成功，刷新用户列表
                users = db_manager.get_all_users()
                if users:
                    # 如果删除后还有其他用户，保持当前状态
                    return (
                        current_user_display.value,
                        chatbot.value, 
                        f"✅ 用户ID {user_id_to_delete} 已成功删除"
                    )
                else:
                    # 如果删除后没有用户了，创建一个默认用户
                    new_user_id = db_manager.create_user("默认用户")
                    current_user_id = new_user_id
                    current_conversation_state = ConversationState.INITIAL_GREETING
                    
                    return (
                        "默认用户",
                        [{"role": "assistant", "content": "嗨～ 我是你的校园小树洞喵呜🌳，很高兴在这里遇到你~ 随便说点什么，让我们开始愉快的聊天吧！✨"}],
                        f"✅ 用户已删除，已为您创建新的默认用户"
                    )
            else:
                return (None, [], "❌ 删除用户失败，请检查日志")
                
        except Exception as e:
            print(f"❌ 删除用户时出错: {e}")
            return (None, [], f"❌ 删除失败：{str(e)}")

    # ===== 事件绑定 =====
    
    # 主对话区事件绑定
    msg.submit(user_message_handler, [msg, chatbot, conversation_status], [msg, chatbot, conversation_status])
    submit_btn.click(user_message_handler, [msg, chatbot, conversation_status], [msg, chatbot, conversation_status])
    clear_btn.click(clear_conversation, None, chatbot)
    reset_conversation_btn.click(clear_conversation, None, chatbot)
    
    # MBTI测试事件绑定
    start_test_btn.click(
        start_mbti_test,
        outputs=[test_progress, test_question, test_options_row, start_test_btn, reset_test_btn, test_session, test_result]
    )
    option_a_btn.click(
        lambda session: process_test_answer("A", session),
        inputs=[test_session],
        outputs=[test_progress, test_question, test_options_row, start_test_btn, reset_test_btn, test_session, test_result]
    )
    option_b_btn.click(
        lambda session: process_test_answer("B", session),
        inputs=[test_session],
        outputs=[test_progress, test_question, test_options_row, start_test_btn, reset_test_btn, test_session, test_result]
    )
    option_c_btn.click( # 🚨 新增C选项事件绑定
        lambda session: process_test_answer("C", session),
        inputs=[test_session],
        outputs=[test_progress, test_question, test_options_row, start_test_btn, reset_test_btn, test_session, test_result]
    )
    reset_test_btn.click(
        reset_mbti_test,
        outputs=[test_progress, test_question, test_options_row, start_test_btn, reset_test_btn, test_session, test_result]
    )
    
    # 增强版系统思维事件绑定
    # 选择开始方式 - 完整系统思维分析
    choice_a_btn.click(
        start_systematic_analysis_enhanced,
        inputs=[gr.State(1)],  # user_id
        outputs=[what_section, why_section, how_section, progress_system, thinking_analysis, user_profile_display]
    )

    # 轻松交流模式
    choice_b_btn.click(
        lambda: (
            gr.update(visible=True),   # what_section
            gr.update(visible=False),  # why_section
            gr.update(visible=False),  # how_section
            gr.update(visible=False),  # progress_system
            "🌟 轻松交流模式已启动！你可以随意描述你的困扰，我会温暖地陪伴你～",  # thinking_analysis
            gr.update(visible=False)   # user_profile_display
        ),
        outputs=[what_section, why_section, how_section, progress_system, thinking_analysis, user_profile_display]
    )

    # 自由表达空间
    choice_c_btn.click(
        lambda: (
            gr.update(visible=True),   # what_section
            gr.update(visible=False),  # why_section
            gr.update(visible=False),  # how_section
            gr.update(visible=False),  # progress_system
            "🎈 自由表达空间已开启！想说什么就说什么，我会认真倾听～",  # thinking_analysis
            gr.update(visible=False)   # user_profile_display
        ),
        outputs=[what_section, why_section, how_section, progress_system, thinking_analysis, user_profile_display]
    )

    # 第一步：是什么（增强版分析）
    analyze_what_btn.click(
        analyze_problem_what_enhanced,
        inputs=[
            problem_input,
            emotion_checkboxes,
            context_input,
            urgency_level,
            gr.State(1)
        ],
        outputs=[dynamic_analysis_result]
    )

    # 快速分析按钮
    quick_analysis_btn.click(
        lambda problem_text: analyze_problem_what_enhanced(problem_text, None, None, None, 1),
        inputs=[problem_input],
        outputs=[dynamic_analysis_result]
    )

    # 进入为什么分析
    analyze_what_btn.click(
        proceed_to_why_analysis_enhanced,
        inputs=[problem_input, emotion_checkboxes, context_input, urgency_level],
        outputs=[why_section, current_step_info, progress_visualization]
    )

    # 重置第一步
    reset_what_btn.click(
        lambda: (
            "",  # problem_input
            [],  # emotion_checkboxes
            "",  # context_input
            None,  # urgency_level
            "🔄 已重置，请重新开始问题分析。",  # dynamic_analysis_result
            update_progress_visualization("start")  # progress_visualization
        ),
        outputs=[problem_input, emotion_checkboxes, context_input, urgency_level, dynamic_analysis_result, progress_visualization]
    )
    
    # 第二步：为什么 - 分析模式选择
    why_choice_c.click(
        lambda: (
            gr.update(visible=False),  # why_choice_group
            gr.update(visible=False),  # listening_area
            gr.update(visible=False),  # quiet_area
            gr.update(visible=True),   # reason_analysis_area
            "🔍 太好了！让我们开始深度分析模式。请填写下面的多层级分析内容。"  # dynamic_analysis_result
        ),
        outputs=[why_choice_group, listening_area, quiet_area, reason_analysis_area, dynamic_analysis_result]
    )

    # 倾听陪伴模式
    why_choice_a.click(
        lambda: (
            gr.update(visible=False),  # why_choice_group
            gr.update(visible=True),   # listening_area
            gr.update(visible=False),  # quiet_area
            gr.update(visible=False),  # reason_analysis_area
            "👂 我在这里，全然地陪伴你。你的感受很重要，我愿做你情绪的容器。"  # dynamic_analysis_result
        ),
        outputs=[why_choice_group, listening_area, quiet_area, reason_analysis_area, dynamic_analysis_result]
    )

    # 静心休息模式
    why_choice_b.click(
        lambda: (
            gr.update(visible=False),  # why_choice_group
            gr.update(visible=False),  # listening_area
            gr.update(visible=True),   # quiet_area
            gr.update(visible=False),  # reason_analysis_area
            "☕ 让我们一起安静一会儿吧...深呼吸几次，感受一下此刻的宁静。"  # dynamic_analysis_result
        ),
        outputs=[why_choice_group, listening_area, quiet_area, reason_analysis_area, dynamic_analysis_result]
    )

    # 深度分析按钮
    analyze_why_btn.click(
        analyze_problem_why_enhanced,
        inputs=[
            event_input,
            pain_input,
            impact_input,
            belief_input,
            system_input,
            gr.State(1)
        ],
        outputs=[dynamic_analysis_result]
    )

    # 分层分析按钮
    layer_analysis_btn.click(
        lambda event, pain, impact, belief, system: f"""
### 📊 分层分析结果

**已填写的分析层级：**
{f"- 📅 表面事件层：{event[:50]}..." if event.strip() else ""}
{f"- 💔 情感痛点层：{pain[:50]}..." if pain.strip() else ""}
{f"- 🌊 影响范围层：{impact[:50]}..." if impact.strip() else ""}
{f"- 🧠 深层信念层：{belief[:50]}..." if belief.strip() else ""}
{f"- 🌍 系统因素层：{system[:50]}..." if system.strip() else ""}

**建议：** 继续完善其他层级的分析，或点击"启动深度分析"获得AI专业分析。
        """,
        inputs=[event_input, pain_input, impact_input, belief_input, system_input],
        outputs=[dynamic_analysis_result]
    )

    # 进入行动方案制定
    analyze_why_btn.click(
        proceed_to_how_analysis_enhanced,
        inputs=[gr.State("analysis_complete")],
        outputs=[how_section, current_step_info, progress_visualization]
    )

    # 重置第二步
    reset_why_btn.click(
        lambda: (
            "",  # event_input
            "",  # pain_input
            "",  # impact_input
            "",  # belief_input
            "",  # system_input
            "🔄 已重置原因分析，请重新开始。",  # dynamic_analysis_result
            update_progress_visualization("why")  # progress_visualization
        ),
        outputs=[event_input, pain_input, impact_input, belief_input, system_input, dynamic_analysis_result, progress_visualization]
    )
    
    # 第二步：为什么 - 倾听模式
    listening_btn.click(
        handle_listening_input,
        inputs=[listening_input],
        outputs=[thinking_analysis]
    )
    back_to_why_btn.click(
        back_to_why_analysis,
        outputs=[why_choice_group, listening_area, quiet_area, thinking_analysis]
    )
    
    # 第二步：为什么 - 安静模式
    ready_btn.click(
        choose_analysis_mode,
        outputs=[why_choice_group, listening_area, quiet_area, reason_analysis_area, thinking_analysis]
    )
    back_to_why_btn2.click(
        back_to_why_analysis,
        outputs=[why_choice_group, listening_area, quiet_area, thinking_analysis]
    )
    
    # 第三步：怎么办 - 增强版行动方案制定
    analyze_how_btn.click(
        analyze_problem_how_enhanced,
        inputs=[
            immediate_action,
            short_term_plan,
            medium_term_plan,
            people_support,
            tool_support,
            environment_input,
            potential_obstacles,
            coping_strategies,
            emergency_contact,
            gr.State(1)
        ],
        outputs=[dynamic_analysis_result]
    )

    # 快速方案生成
    preview_plan_btn.click(
        lambda immediate, short, medium: f"""
### ⚡ 快速方案预览

**你的行动计划框架：**
{f"🎯 **即时行动**：{immediate}" if immediate.strip() else ""}
{f"📅 **短期计划**：{short}" if short.strip() else ""}
{f"🎯 **中期规划**：{medium}" if medium.strip() else ""}

**建议：** 继续完善其他支持要素，或点击"生成完整方案"获得AI优化建议。
        """,
        inputs=[immediate_action, short_term_plan, medium_term_plan],
        outputs=[dynamic_analysis_result]
    )

    # 完成整个系统思维流程
    analyze_how_btn.click(
        lambda: (
            "🎉 恭喜完成系统思维全流程！",  # current_step_info
            update_progress_visualization("complete")  # progress_visualization
        ),
        outputs=[current_step_info, progress_visualization]
    )

    # 重置第三步
    reset_how_btn.click(
        lambda: (
            "",  # immediate_action
            "",  # short_term_plan
            "",  # medium_term_plan
            [],  # people_support
            [],  # tool_support
            "",  # environment_input
            "",  # potential_obstacles
            "",  # coping_strategies
            "",  # emergency_contact
            "🔄 已重置行动方案，请重新制定。",  # dynamic_analysis_result
            update_progress_visualization("how")  # progress_visualization
        ),
        outputs=[
            immediate_action, short_term_plan, medium_term_plan,
            people_support, tool_support, environment_input,
            potential_obstacles, coping_strategies, emergency_contact,
            dynamic_analysis_result, progress_visualization
        ]
    )

    # 保存完整分析结果
    save_analysis_btn.click(
        lambda problem, immediate, short, medium, analysis: f"""
### 💾 分析结果已保存

**保存内容：**
- 问题描述：{problem[:50]}...
- 行动方案：{immediate[:30]}...
- 分析结果：已保存到个人档案

**提示：** 你可以随时回来查看和更新你的分析结果。
        """,
        inputs=[problem_input, immediate_action, short_term_plan, medium_term_plan, dynamic_analysis_result],
        outputs=[dynamic_analysis_result]
    )

    # 获取智能推荐
    get_recommendations_btn.click(
        lambda problem, immediate, short: f"""
### 🤖 AI智能推荐

基于你的问题和计划，我推荐：

**🎯 执行建议：**
- 从最小的行动开始
- 设定明确的时间节点
- 建立进度跟踪机制

**💪 成功要素：**
- 保持行动的一致性
- 及时调整策略
- 寻求必要的支持

**🌟 个性化提醒：**
记住，完成比完美更重要！
        """,
        inputs=[problem_input, immediate_action, short_term_plan],
        outputs=[dynamic_analysis_result]
    )

    # 幸福日志事件绑定
    add_btn.click(
        add_happiness_entry,
        inputs=[happiness_input, mood_rating, entry_date],
        outputs=[response, prompt, entry_date]
    )
    clear_happiness_btn.click(
        clear_happiness_entries,
        outputs=[response]
    )
    
    # 日历功能事件绑定  
    prev_btn.click(
        lambda year, month: update_calendar_display(*get_prev_month(year, month)),
        inputs=[year_state, month_state],
        outputs=[calendar_df, year_state, month_state, calendar_title_display]
    )
    next_btn.click(
        lambda year, month: update_calendar_display(*get_next_month(year, month)),
        inputs=[year_state, month_state],
        outputs=[calendar_df, year_state, month_state, calendar_title_display]
    )
    refresh_btn.click(
        lambda year, month: update_calendar_display(year, month),
        inputs=[year_state, month_state],
        outputs=[calendar_df, year_state, month_state, calendar_title_display]
    )
    
    # 🚨 新增：日期查看按钮事件绑定
    view_date_btn.click(
        show_date_entries,
        inputs=[date_selector],
        outputs=[date_entries]
    )
    
    # 社区权限检查函数
    def check_community_access():
        """检查用户是否可以访问社区功能"""
        global current_user_id, community_manager
        
        if current_user_id is None:
            return """## 🔒 社区功能未解锁

### 👋 你好！欢迎来到喵呜社区入口

要解锁社区的完整功能，需要先完成以下步骤：

🚀 **解锁条件：**
1. 💬 **完整对话体验** - 在"主对话"中与喵呜进行一次完整交流
2. 🧠 **荣格八维测试** - 了解你的性格类型（推荐但非必需）  
3. 🔍 **系统思维体验** - 体验"是什么→为什么→怎么办"分析方法

**为什么需要这些步骤？** 🤔
- 📊 让喵呜更好地了解你的个性和需求
- 🎯 确保社区互动更有针对性和效果
- 🛡️ 营造一个理解彼此、相互支持的温暖氛围

**赶快去"💬 主对话"开始你的心理支持之旅吧！** ✨
""", gr.Group(visible=False)
            
        try:
            # 检查用户是否完成引导流程
            user_profile = db_manager.get_user_profile(current_user_id)
            is_onboarding_complete = user_profile.get('is_onboarding_complete', False)
            
            if is_onboarding_complete:
                # 用户已完成引导，显示欢迎信息并解锁社区
                return """## 🎉 欢迎来到喵呜社区！

### 🌟 恭喜你解锁了社区的所有功能：
- 🏪 **社区广场** - 浏览求助帖子和经验分享  
- 💝 **发布求助** - 匿名发布你的困扰，获得同伴支持
- 👥 **我的记录** - 管理你的发帖历史  
- 💡 **喵呜精选** - AI推荐积极心理学内容 ✨**MCP功能**
- 🎮 **喵呜乐园** - 心理健康小游戏推荐 ✨**MCP功能**

---
**🚀 技术亮点：** 集成MCP web_search功能，实时搜索和推荐优质心理健康内容！

感谢你完成了完整的心理支持体验，现在可以尽情探索社区功能了！💝
""", gr.Group(visible=True)
            else:
                # 用户尚未完成引导，显示解锁提示
                user_info = db_manager.get_user(current_user_id)
                current_state = user_info.get('current_state', 'initial_greeting') if user_info else 'initial_greeting'
                
                return f"""## 🔒 社区功能即将解锁！

### 👋 你好！你已经开始了喵呜之旅

**当前进度：** `{current_state}` 🎯

要完全解锁社区功能，请继续完成：

🚀 **剩余步骤：**
1. 💬 **完成完整对话流程** - 在"主对话"中继续与喵呜交流
2. 🧠 **荣格八维测试** - 了解你的性格类型（可选）  
3. 🔍 **系统思维体验** - 体验情绪疏导方法（可选）

**为什么需要完成这些？** 🤔
- 📊 让社区推荐更符合你的需求
- 🎯 确保你能给出和接受更有针对性的帮助
- 🛡️ 建立一个相互理解的温暖社区

**继续去"💬 主对话"完成你的心理支持体验吧！** ✨
""", gr.Group(visible=False)
                
        except Exception as e:
            return f"""## ⚠️ 社区功能暂时不可用

抱歉，社区功能遇到了一些技术问题：{e}

请稍后重试，或先体验其他功能模块。""", gr.Group(visible=False)
    
    refresh_psychology_btn.click(
        get_formatted_psychology_recommendations,
        outputs=[psychology_recommendations_display]
    )
    refresh_games_btn.click(
        get_formatted_game_recommendations,
        outputs=[game_recommendations_display]
    )

    # 社区广场刷新按钮
    refresh_posts_btn.click(
        get_all_community_posts_formatted,
        outputs=[posts_display]
    )
    
    # 广场匹配推荐刷新按钮
    refresh_matching_btn.click(
        lambda: community_manager.get_matching_recommendations(current_user_id, limit=5),
        outputs=[matching_recommendations_display]
    )

    # 发布求助按钮
    publish_post_btn.click(
        publish_new_post,
        inputs=[post_title, post_content, post_tags],
        outputs=[post_result, post_title, post_content, post_tags]
    )

    # 清空发布求助表单按钮
    clear_post_btn.click(
        lambda: ["", "", ""],
        outputs=[post_title, post_content, post_tags]
    )

    # 我的求助刷新按钮
    refresh_my_help_requests_btn.click(
        get_my_help_requests_formatted_func,
        outputs=[my_help_requests_display]
    )
    
    # 🚨 新的用户管理事件绑定
    
    # 显示新建用户弹窗
    new_user_btn.click(
        lambda: new_user_modal.update(visible=True),
        outputs=[new_user_modal]
    )
    
    # 取消新建用户
    cancel_new_user_btn_in_modal.click(
        lambda: (new_user_modal.update(visible=False), ""),
        outputs=[new_user_modal, new_user_name_input_in_modal]
    )
    
    # 显示切换用户弹窗
    switch_user_btn.click(
        lambda: (switch_user_modal.update(visible=True), switch_to_user_list()),
        outputs=[switch_user_modal, user_list_radio_in_modal]
    )
    
    # 🚨 修复并移动：确认切换用户，确保历史对话正确加载
    confirm_switch_user_btn_in_modal.click(
        handle_user_switch,
        inputs=[user_list_radio_in_modal],
        outputs=[current_user_display, chatbot, conversation_status, switch_user_modal]
    )
    cancel_switch_btn_in_modal.click(
        lambda: switch_user_modal.update(visible=False),
        outputs=[switch_user_modal]
    )
    delete_user_btn_in_modal.click(
        show_delete_confirmation,
        inputs=[user_list_radio_in_modal],
        outputs=[delete_user_modal, delete_user_info_in_modal]
    ).then(
        lambda: switch_user_modal.update(visible=False),  # 关闭用户切换弹窗
        outputs=[switch_user_modal]
    )
    
    # 🚨 修复并移动：删除用户确认弹窗的事件绑定
    cancel_delete_btn_in_modal.click(
        lambda: delete_user_modal.update(visible=False),
        outputs=[delete_user_modal]
    )
    confirm_delete_btn_in_modal.click(
        delete_user_confirmed,
        inputs=[user_list_radio_in_modal],
        outputs=[current_user_display, chatbot, conversation_status]
    ).then(
        lambda: delete_user_modal.update(visible=False),  # 删除完成后关闭弹窗
        outputs=[delete_user_modal]
    )
    
    # 🚨 修复并移动：新建用户确认事件绑定
    confirm_new_user_btn_in_modal.click(
        add_new_user_to_db,
        inputs=[new_user_name_input_in_modal],
        outputs=[current_user_display, chatbot, conversation_status, new_user_name_input_in_modal]
    ).then(
        lambda: new_user_modal.update(visible=False),  # 创建成功后关闭弹窗
        outputs=[new_user_modal]
    )
    
    # 保留原有事件绑定（兼容性）
    add_user_btn.click(add_new_user_to_db, inputs=[new_user_name], outputs=[user_selector, new_user_name, user_info])
    user_selector.change(switch_user, inputs=[user_selector], outputs=[user_info])
    
    # 初始化界面
    demo.load(welcome_message, outputs=[chatbot])
    
    # 🚨 新增：社区权限动态检查
    # 当用户切换到社区Tab时检查权限
    community_tab.select(
        check_community_access,
        outputs=[community_access_check, community_main_interface]
    ).then(
        get_all_community_posts_formatted,
        outputs=[posts_display]
    ).then(
        lambda: community_manager.get_matching_recommendations(current_user_id, limit=5),
        outputs=[matching_recommendations_display]
    ).then(
        get_my_help_requests_formatted_func,
        outputs=[my_help_requests_display]
    )

    # 页面加载时初始化社区状态
    demo.load(
        check_community_access,
        outputs=[community_access_check, community_main_interface]
    )
    demo.load(
        get_all_community_posts_formatted,
        outputs=[posts_display]
    )
    demo.load(
        lambda: community_manager.get_matching_recommendations(current_user_id, limit=5),
        outputs=[matching_recommendations_display]
    )
    demo.load(
        get_my_help_requests_formatted_func,
        outputs=[my_help_requests_display]
    )

    print("🌸 大学生心理助手喵呜 - 清理版界面已准备就绪")

# ===== 应用启动 =====

def setup_community_manager():
    """设置喵呜社区管理器（延迟初始化）"""
    global community_manager, current_user_id
    
    print("🔍 [APP] 开始设置社区管理器...")
    try:
        # 🚨 修复：传入db_manager参数
        community_manager = get_community_manager(db_manager)
        
        # 初始化默认用户
        if current_user_id is None:
            print("🔍 [APP] 未检测到当前用户，尝试创建默认用户...")
            current_user_id = db_manager.create_user("默认用户")
            print(f"✅ [APP] 创建默认用户成功 (ID: {current_user_id})。")
        else:
            print(f"✅ [APP] 当前用户已存在 (ID: {current_user_id})。")
        
        print("🐱 [APP] 喵呜社区管理器已就绪。")
    except Exception as e:
        print(f"❌ [APP] 社区管理器初始化失败: {e}")
        import traceback
        traceback.print_exc()

def launch_app():
    """启动应用"""
    
    # 🚨 新增：MCP功能状态检查和启用提示
    import os
    mcp_enabled = os.getenv('MCP_ENABLED', 'false').lower() == 'true'
    
    # 🌟 自动启用MCP功能（用于演示）
    if not mcp_enabled:
        os.environ['MCP_ENABLED'] = 'true'
        mcp_enabled = True
        print("🌐 自动启用MCP功能（演示模式）")
    
    print(f"🚀 启动配置:")
    print(f"   - 主机: {SERVER_HOST}")
    print(f"   - 端口: {SERVER_PORT}")
    print(f"   - 公网分享: {SERVER_SHARE}")
    print(f"   - 调试模式: {SERVER_DEBUG}")
    print(f"   - 自动端口查找: {AUTO_FIND_PORT}")
    print(f"   - MCP功能状态: {'✅ 已启用' if mcp_enabled else '❌ 未启用'}")
    
    if not mcp_enabled:
        print("💡 [APP] MCP启用提示:")
        print("   - 设置环境变量 MCP_ENABLED=true 可启用真实网络搜索功能")
        print("   - 或在终端中运行: set MCP_ENABLED=true && python 魔搭/app.py")
        print("   - 当前使用高质量静态内容进行功能演示")
    else:
        print("🌐 [APP] MCP Web搜索功能已启用，社区推荐将使用真实网络内容。")
        # 测试MCP Server可用性
        print("🔍 [APP] 正在测试MCP Server可用性...")
        try:
            from mcp_web_search_server import web_search
            test_result = web_search(query="测试", sources=[1]) # 调用web_search，传入query和source参数
            if test_result.get("success"):
                print("✅ [APP] MCP Server测试成功，功能正常。")
            else:
                print(f"⚠️ [APP] MCP Server测试失败: {test_result.get('error')}。")
        except ImportError:
            print(f"❌ [APP] MCP Server模块 'mcp_web_search_server' 导入失败。请确保文件存在且无语法错误。")
            import traceback
            traceback.print_exc()
        except Exception as e:
            print(f"⚠️ [APP] MCP Server测试调用失败: {e}")
            import traceback
            traceback.print_exc()
    
        # 初始化社区管理器
        print("🔍 [APP] 在launch_app中调用setup_community_manager()...")
        setup_community_manager()
        print("✅ [APP] setup_community_manager() 调用完成。")
        
        # 启动应用
        print("🔍 [APP] 尝试启动 Gradio 应用...")
        try:
            if AUTO_FIND_PORT:
                # 自动寻找可用端口
                available_port = find_free_port()
                if available_port:
                    print(f"🔍 [APP] 找到可用端口: {available_port}。")
                    demo.launch(
                        server_name=SERVER_HOST,
                        server_port=available_port,
                        share=SERVER_SHARE,
                        debug=SERVER_DEBUG,
                        show_error=SERVER_SHOW_ERROR
                    )
                else:
                    print("❌ [APP] 未找到可用端口，使用默认端口。")
                    demo.launch(
                        server_name=SERVER_HOST,
                        server_port=SERVER_PORT,
                        share=SERVER_SHARE,
                        debug=SERVER_DEBUG,
                        show_error=SERVER_SHOW_ERROR
                    )
            else:
                # 使用指定端口
                print(f"🔍 [APP] 使用指定端口 {SERVER_PORT} 启动 Gradio。")
                demo.launch(
                    server_name=SERVER_HOST,
                    server_port=SERVER_PORT,
                    share=SERVER_SHARE,
                    debug=SERVER_DEBUG,
                    show_error=SERVER_SHOW_ERROR
                )
            print("✅ [APP] Gradio 应用成功启动。")
        except Exception as e:
            print(f"❌ [APP] Gradio 应用启动失败: {e}")
            print("💡 [APP] 请检查端口是否被占用，或尝试修改配置。")
            import traceback
            traceback.print_exc()

# 主入口点
if __name__ == "__main__":
    print("==============================================")
    print("🚀 [APP] 应用主入口 (__main__) 已启动。")
    print("==============================================")
    try:
        launch_app()
    except Exception as e:
        print(f"❌ [APP] 应用在顶层捕获到未处理异常: {e}")
        print("💡 [APP] 应用程序意外终止，请检查上述日志以获取更多详细信息。")
        import traceback
        traceback.print_exc()
