"""
魔搭版对话流程管理模块 - 保持原版优秀逻辑
==========================================

完全保持原版conversation_flow.py的优秀逻辑和细节
只在API调用方面适配魔搭平台

功能：
1. 完整的对话状态管理（与原版相同）
2. 完整心理支持流程（与原版相同）
3. 完整荣格八维测试（8题，与原版相同）
4. 完整危机检测集成（与原版相同）

设计原则：保持原版逻辑，只适配API调用
"""

from typing import Dict, List, Optional
from config_modelscope import DEFAULT_SESSION_DATA, EMOTION_TYPES

# ========================================================================
# 📊 会话状态管理 - 与原版相同
# ========================================================================

class ConversationManager:
    """对话管理器 - 与原版conversation_flow.py完全相同"""
    
    def __init__(self):
        """初始化对话管理器"""
        self.session_data = DEFAULT_SESSION_DATA.copy()
    
    def reset_session(self):
        """重置会话状态"""
        self.session_data = DEFAULT_SESSION_DATA.copy()
    
    def get_current_stage(self) -> str:
        """获取当前对话阶段"""
        return self.session_data.get("stage", "welcome")
    
    def set_stage(self, stage: str):
        """设置对话阶段"""
        self.session_data["stage"] = stage
    
    def update_user_info(self, key: str, value):
        """更新用户信息"""
        self.session_data[key] = value
    
    def get_user_info(self, key: str):
        """获取用户信息"""
        return self.session_data.get(key, "")
    
    def add_conversation_history(self, role: str, content: str):
        """添加对话历史"""
        self.session_data.setdefault("conversation_history", []).append({
            "role": role,
            "content": content
        })

# ========================================================================
# 🧠 荣格八维测试数据 - 完整8题版本（与原版相同）
# ========================================================================

# 从原版jungian_test.py复制完整的8题测试
JUNG_ARCHETYPES_QUESTIONS = [
    {
        "question": "一场信息量很大的讲座结束后，你会：",
        "options": {
            "A": "找几个朋友聊聊，通过讨论来消化内容",
            "B": "需要一些独处时间，在脑海里回顾和整理",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"E": 2}, "B": {"I": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "当面临一个全新的、复杂的任务时，你倾向于：",
        "options": {
            "A": "立刻开始行动，在实践中边做边学",
            "B": "先花时间理解全局，构思一个整体框架",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"E": 2}, "B": {"I": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "在描述一件事情时，你更习惯：",
        "options": {
            "A": "关注具体发生了什么，提供详尽的事实和细节",
            "B": "解释这件事背后的含义和未来的可能性",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"S": 2}, "B": {"N": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "你更欣赏哪种生活方式？",
        "options": {
            "A": "务实、脚踏实地，享受当下拥有的",
            "B": "充满想象，不断探索和创造新的可能",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"S": 2}, "B": {"N": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "当你需要做出一个艰难的决定时，你更看重：",
        "options": {
            "A": "逻辑上的合理性和客观的公平原则",
            "B": "这个决定对相关人员的情感影响和价值观的契合度",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"T": 2}, "B": {"F": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "团队合作中出现分歧，你会优先：",
        "options": {
            "A": "聚焦于问题本身，进行冷静的对错分析",
            "B": "维护团队和谐，努力让大家都感到被理解",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"T": 2}, "B": {"F": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "关于周末安排，你更喜欢：",
        "options": {
            "A": "有一个清晰的计划，知道每个时间段做什么",
            "B": "保持开放和灵活性，看心情随性而为",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"J": 2}, "B": {"P": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "你通常如何处理任务的截止日期？",
        "options": {
            "A": "提前规划并完成，享受没有压力的感觉",
            "B": "在截止日期前灵感迸发，集中精力冲刺完成",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"J": 2}, "B": {"P": 2}, "C": {}},
        "weight": 2
    },
    # 复制并修改以达到16题
    {
        "question": "参加社交活动后，你会：",
        "options": {
            "A": "感到精力充沛，期待下一次聚会",
            "B": "需要时间独处，恢复精力",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"E": 2}, "B": {"I": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "在学习新技能时，你更喜欢：",
        "options": {
            "A": "通过实际操作和练习来学习",
            "B": "通过阅读和思考，理解理论知识再实践",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"E": 2}, "B": {"I": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "当解决问题时，你更依赖：",
        "options": {
            "A": "过去的经验和实际可行的方案",
            "B": "创新的想法和潜在的未来影响",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"S": 2}, "B": {"N": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "你更倾向于：",
        "options": {
            "A": "关注事实和细节，重视实际应用",
            "B": "探索抽象概念和可能性，重视灵感",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"S": 2}, "B": {"N": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "做决策时，你更注重：",
        "options": {
            "A": "基于逻辑分析和客观证据",
            "B": "基于个人价值观和对他人感受的影响",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"T": 2}, "B": {"F": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "在冲突中，你更倾向于：",
        "options": {
            "A": "寻求公正的解决方案，即使会伤害感情",
            "B": "优先考虑维护和谐，避免直接冲突",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"T": 2}, "B": {"F": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "你的工作方式是：",
        "options": {
            "A": "有条不紊，按照计划一步步推进",
            "B": "灵活应变，喜欢同时处理多个任务",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"J": 2}, "B": {"P": 2}, "C": {}},
        "weight": 2
    },
    {
        "question": "对于突发事件，你的反应是：",
        "options": {
            "A": "迅速制定应对计划，掌控局面",
            "B": "保持开放，随遇而安，寻找新的可能性",
            "C": "不确定 / 中立"
        },
        "score_map": {"A": {"J": 2}, "B": {"P": 2}, "C": {}},
        "weight": 2
    }
]

# 完整的16型人格描述（与原版相同）
PERSONALITY_TYPES = {
    "INTJ": {"name": "建筑师", "description": "理性的思想家，具有强烈的独立性和创新能力"},
    "INTP": {"name": "思想家", "description": "创新的发明家，对知识有着不可遏制的渴望"},
    "ENTJ": {"name": "指挥官", "description": "大胆、富有想象力、意志强烈的领导者"},
    "ENTP": {"name": "辩论家", "description": "聪明好奇的思想家，不会拒绝智力上的挑战"},
    "INFJ": {"name": "提倡者", "description": "安静而神秘，鼓舞人心且不知疲倦的理想主义者"},
    "INFP": {"name": "调停者", "description": "诗意而善良，总是乐于助人的利他主义者"},
    "ENFJ": {"name": "主人公", "description": "富有魅力且鼓舞人心的领导者，能够使听众着迷"},
    "ENFP": {"name": "竞选者", "description": "热情、有创造性、善于社交的自由精神"},
    "ISTJ": {"name": "物流师", "description": "实用主义的实干家，可靠性无可挑剔"},
    "ISFJ": {"name": "守护者", "description": "非常专注、温暖的守护者，时刻准备保护爱人"},
    "ESTJ": {"name": "总经理", "description": "出色的管理者，在管理事物或人员方面无与伦比"},
    "ESFJ": {"name": "执政官", "description": "非常关心他人、善于社交、广受欢迎的人"},
    "ISTP": {"name": "鉴赏家", "description": "大胆而实际的实验家，擅长使用各种工具"},
    "ISFP": {"name": "探险家", "description": "灵活、有魅力的艺术家，时刻准备探索新的可能性"},
    "ESTP": {"name": "企业家", "description": "聪明、精力充沛、善于感知的人，真正享受生活在边缘"},
    "ESFP": {"name": "表演者", "description": "自发性、精力充沛、热情的人，生活永远不会无聊"}
}

def calculate_jungian_type(scores: Dict[str, int]) -> str:
    """计算荣格类型 - 与原版相同的逻辑"""
    result = ""
    result += "E" if scores["E"] >= scores["I"] else "I"
    result += "S" if scores["S"] >= scores["N"] else "N"
    result += "T" if scores["T"] >= scores["F"] else "F"
    result += "J" if scores["J"] >= scores["P"] else "P"
    return result

def process_jungian_answer(user_answer: str, current_question: Dict) -> Dict[str, int]:
    """处理荣格测试答案 - 与原版相同的逻辑"""
    scores_to_add = {}
    user_answer = user_answer.strip().upper()
    
    if user_answer in current_question["score_map"]:
        scores_to_add = current_question["score_map"][user_answer]
        question_weight = current_question.get("weight", 1)
        
        for dimension, score in scores_to_add.items():
            scores_to_add[dimension] = score * question_weight
    # 🚨 新增：处理C选项时，不添加分数
    elif user_answer == "C":
        return {}
    else:
        for key, value in current_question["options"].items():
            if user_answer in value.upper():
                scores_to_add = current_question["score_map"][key]
                question_weight = current_question.get("weight", 1)
                
                for dimension, score in scores_to_add.items():
                    scores_to_add[dimension] = score * question_weight
                break
    
    return scores_to_add

def get_jungian_question(question_index: int):
    """获取荣格测试问题 - 与原版相同的逻辑"""
    if question_index >= len(JUNG_ARCHETYPES_QUESTIONS):
        return None, None
    
    question = JUNG_ARCHETYPES_QUESTIONS[question_index]
    options_text = "\n".join([f"**{key}.** {value}" for key, value in question["options"].items()])
    # 🚨 修改：更新问题提示，适应A/B/C选项
    question_text = f"**第{question_index + 1}题：{question['question']}**\n\n{options_text}\n\n(请直接输入 A、B 或 C)"
    
    return question_text, question

def get_cognitive_functions_stack(mbti_type: str) -> List[str]:
    """获取MBTI类型对应的八维认知功能栈（主导→辅助→第三→劣势）"""
    # 16种MBTI类型的认知功能栈
    cognitive_stacks = {
        "INTJ": ["Ni", "Te", "Fi", "Se"],
        "INTP": ["Ti", "Ne", "Si", "Fe"],
        "ENTJ": ["Te", "Ni", "Se", "Fi"],
        "ENTP": ["Ne", "Ti", "Fe", "Si"],
        "INFJ": ["Ni", "Fe", "Ti", "Se"],
        "INFP": ["Fi", "Ne", "Si", "Te"],
        "ENFJ": ["Fe", "Ni", "Se", "Ti"],
        "ENFP": ["Ne", "Fi", "Te", "Si"],
        "ISTJ": ["Si", "Te", "Fi", "Ne"],
        "ISFJ": ["Si", "Fe", "Ti", "Ne"],
        "ESTJ": ["Te", "Si", "Ne", "Fi"],
        "ESFJ": ["Fe", "Si", "Ne", "Ti"],
        "ISTP": ["Ti", "Se", "Ni", "Fe"],
        "ISFP": ["Fi", "Se", "Ni", "Te"],
        "ESTP": ["Se", "Ti", "Fe", "Ni"],
        "ESFP": ["Se", "Fi", "Te", "Ni"]
    }
    return cognitive_stacks.get(mbti_type, ["?", "?", "?", "?"])

def get_detailed_cognitive_analysis(cognitive_stack: List[str]) -> str:
    """生成详细的认知功能分析"""
    try:
        from cognitive_functions_content import COGNITIVE_FUNCTIONS_CONTENT, format_cognitive_function_card
        
        analysis_parts = []
        
        function_roles = [
            ("主导功能", "你最信赖和常用的认知方式，是你个性的核心驱动力", "🎯"),
            ("辅助功能", "支持主导功能的重要助手，帮你平衡和完善思维", "⚖️"),
            ("第三功能", "成年后逐渐发展的功能，是个人成长的关键领域", "🌱"),
            ("劣势功能", "最不熟练但具有巨大潜力的功能，需要耐心培养", "💪")
        ]
        
        for i, (role_name, role_desc, emoji) in enumerate(function_roles):
            if i < len(cognitive_stack):
                func_code = cognitive_stack[i]
                func_info = COGNITIVE_FUNCTIONS_CONTENT.get(func_code, {})
                
                if func_info:
                    analysis_parts.append(f"""
#### {emoji} **{role_name}：{func_info.get('name', func_code)} ({func_code})**
{role_desc}

**定义**：{func_info.get('definition', '暂无定义')}

**生动比喻**：{func_info.get('analogy', '暂无比喻')}

**你的优势**：{func_info.get('strengths', '暂无优势描述')}

**发展建议**：{func_info.get('development_tips', '暂无建议')}

---""")
        
        return "\n".join(analysis_parts)
        
    except ImportError:
        # 如果cognitive_functions_content模块不可用，返回简化版本
        return f"""
#### 🎯 **主导功能：{cognitive_stack[0]}**
你最核心的认知方式，是你个性的主要驱动力。

#### ⚖️ **辅助功能：{cognitive_stack[1]}** 
支持主导功能的重要助手，帮助你平衡思维。

#### 🌱 **第三功能：{cognitive_stack[2]}**
个人成长的关键领域，适度发展能让你更全面。

#### 💪 **劣势功能：{cognitive_stack[3]}**
最具挑战性但也最有潜力的功能，需要耐心培养。
"""

def generate_detailed_jungian_analysis(scores: Dict[str, int]) -> str:
    """生成详细荣格分析 - 包含完整八维认知功能分析"""
    jungian_type = calculate_jungian_type(scores)
    personality_info = PERSONALITY_TYPES.get(jungian_type, {"name": "独特类型", "description": "你拥有独特的个性组合"})
    
    # 获取八维认知功能排序
    cognitive_stack = get_cognitive_functions_stack(jungian_type)
    
    # 引入荣格名言
    jung_quote = """
> **"潜意识正在主宰你的生活，而你称之为命运。"**  
> ——卡尔·荣格
    """
    
    return f"""## 🌟 你的荣格八维深度分析报告

{jung_quote}

### 📊 **你的性格类型：{jungian_type} - {personality_info['name']}**
{personality_info['description']}

**首先，我要表达对你个性的深深敬意！** 🙏 
每一种性格类型都是珍贵且独特的，没有好坏之分，只有不同的优势和成长空间。你的个性组合造就了独一无二的你！

### 🧠 **你的八维认知功能详细解析**

{get_detailed_cognitive_analysis(cognitive_stack)}

### 🌈 **个性化发展建议**
作为 **{jungian_type}** 类型的你，我建议：

1. **🎯 发挥主导功能优势**：你的{cognitive_stack[0]}是你的核心力量，多在需要这种能力的场景中发挥
2. **⚖️ 平衡辅助功能**：适度发展你的{cognitive_stack[1]}，它能很好地支持你的主导功能
3. **🌱 关注第三功能**：你的{cognitive_stack[2]}是成长的关键，适度练习能让你更全面
4. **💪 接纳劣势功能**：不要对你的{cognitive_stack[3]}太苛刻，它需要更多耐心和时间

### 🎯 **实用生活指南**
- **学习方式**：充分利用你{cognitive_stack[0]}的特点来设计学习方法
- **人际交往**：理解别人可能有不同的认知偏好，多包容和理解
- **压力管理**：当感到疲惫时，多使用你的主导和辅助功能来恢复能量
- **职业发展**：寻找能发挥你核心认知优势的领域和岗位

### 💝 **最后的话**
你的个性组合是独一无二的礼物！每个认知功能都有其价值，关键是找到适合自己的平衡点。喵呜会一直陪伴你探索和发现更好的自己！ ✨

记住：**了解自己不是为了限制自己，而是为了更好地发挥自己的潜能！** 🌟"""

# ========================================================================
# 🚨 危机检测（与原版相同的逻辑）
# ========================================================================

CRISIS_KEYWORDS = [
    "想死了", "我想死", "想自杀", "不想活", "想结束生命", 
    "想死掉", "去死吧", "想消失", "不想存在", "想离开这个世界",
    "了结自己", "结束一切", "想走了", "不想继续", "活着没意思",
    "生无可恋", "想解脱", "一了百了", "想跳楼", "想上吊",
    "结束这一切", "没有意义", "是个负担", "不如消失"
]

EXCLUDE_PATTERNS = ["想死你", "笑死", "累死", "热死", "冷死", "忙死", "急死"]

def local_crisis_check(message: str) -> bool:
    """本地危机检测 - 与原版相同的逻辑"""
    if not message or not isinstance(message, str):
        return False
    
    message_lower = message.lower().strip()
    
    for excluded in EXCLUDE_PATTERNS:
        if excluded in message_lower:
            return False
    
    for keyword in CRISIS_KEYWORDS:
        if keyword in message_lower:
            print(f"🚨 本地危机检测：发现关键词 '{keyword}'")
            return True
    
    return False

def enhanced_emergency_detection(message: str, api_check_func=None) -> bool:
    """增强危机检测 - 与原版相同的逻辑"""
    if local_crisis_check(message):
        print("🚨 危机检测：本地关键词检测触发")
        return True
    
    if api_check_func:
        try:
            api_result = api_check_func(message)
            if isinstance(api_result, str) and "是" in api_result:
                print("🚨 危机检测：API智能分析触发")
                return True
            elif isinstance(api_result, bool) and api_result:
                print("🚨 危机检测：API智能分析触发")
                return True
        except Exception as e:
            print(f"⚠️ API危机检测失败: {e}")
    
    return False

# ========================================================================
# 💝 安抚语生成（与原版相同的逻辑）
# ========================================================================

from api_service_unified import get_local_comfort_message as get_local_comfort

def generate_comfort_message(pressure_sources: str, api_comfort_func=None) -> str:
    """生成安抚语 - 与原版相同的逻辑"""
    if api_comfort_func:
        try:
            api_comfort = api_comfort_func(pressure_sources)
            if api_comfort and len(api_comfort.strip()) > 5:
                print(f"✅ 使用API生成的安抚语: {api_comfort}")
                return api_comfort
        except Exception as e:
            print(f"⚠️ API安抚语生成失败: {e}")
    
    local_comfort = get_local_comfort()
    print(f"🔄 使用本地安抚语: {local_comfort}")
    return local_comfort

# ========================================================================
# 🎯 对话流程处理函数 - 与原版完全相同
# ========================================================================

def handle_welcome_stage(message: str, manager: ConversationManager) -> str:
    """处理欢迎阶段 - 融合温暖专业表达"""
    manager.set_stage("ask_nickname")
    return """🌟 遇见你真是一件美好的事～我是你的心灵伙伴喵呜🌱！你的到来让我心里暖暖的！✨

为了更好地陪伴你，我想先了解你一点点～这样我们的交流就能更加贴心有温度！

**为什么要这样做呢？** 💫
就像朋友初次见面，彼此了解能让对话更有深度，我也能给你更走心的回应～

放心，这里很安全，不想回答的随时可以说"跳过"哦！我们慢慢开始吧～ 

首先，怎么称呼会让你感觉更自在呢？(比如：小琳、阿哲、XX同学～)"""

def handle_nickname_stage(message: str, manager: ConversationManager) -> str:
    """处理昵称收集阶段 - 融合温暖专业表达"""
    nickname = message.strip()
    manager.update_user_info("user_nickname", nickname)
    manager.set_stage("user_info_mood")
    
    return f"""{nickname}，好喜欢这个名字！🌷现在想轻轻问问你此刻的心情～

**为什么要了解心情呢？** 🌈
情绪是我们内心的天气预报，知道你的"天气"，我就能调整陪伴的方式，让我们的交流更舒适～

用1-2个词或小表情形容此刻的心情吧～ 
比如：😌平静 / 🌧️有些低落 / 🥱疲惫 / 🌪️思绪纷乱 / ✨小期待 / 🌈还不错～

(用自己的话描述也很欢迎哦！)"""

def handle_mood_stage(message: str, manager: ConversationManager) -> str:
    """处理心情收集阶段 - 融合温暖专业表达"""
    manager.update_user_info("current_emotion", message.strip())
    manager.set_stage("pressure_radar")
    
    return f"""嗯，我感受到你的心情了～ {manager.get_user_info('user_nickname')} 💖

接下来想更深入地认识你，这样我就能给你更有针对性的支持！✨

**为什么需要了解这些呢？** 💭
就像绘制心灵地图，了解你的处境能让我更精准地陪伴你，而不是泛泛而谈哦！

最近有什么让你感到"心头沉甸甸"或"需要喘口气"的事情吗？
比如：📚学业压力 / 👥人际关系 / 🏠想家情绪 / 💼未来迷茫 / 💤身心疲惫...

选1-2件最想说的分享给我吧～ (如果暂时没有特别压力，也可以说"最近还好")"""

def handle_pressure_stage(message: str, manager: ConversationManager, api_comfort_func=None) -> str:
    """处理压力源收集阶段 - 与原版相同"""
    manager.update_user_info("pressure_sources", message.strip())
    manager.set_stage("pressure_comfort_only")
    
    try:
        comfort_message = generate_comfort_message(
            manager.get_user_info("pressure_sources"), 
            api_comfort_func
        )
    except:
        comfort_message = get_local_comfort()
    
    return f"""{comfort_message}

想继续聊聊的话，随时告诉我哦～我会一直在这里。"""

def handle_comfort_stage(message: str, manager: ConversationManager) -> str:
    """处理纯安抚阶段 - 融合温暖专业表达"""
    manager.set_stage("core_need")
    
    return f"""现在有个很重要的问题想问你～ 

**悄悄告诉我，这次来找喵呜，最希望得到什么样的支持呢？** 💝

这个问题很关键，因为不同的需求需要不同的陪伴方式：

比如：
👂 **有人安静倾听** - 我会做专注的听众，不打断不建议
🌿 **缓解压力方法** - 分享实用的小技巧和放松方式  
🤔 **探讨人生选择** - 我们一起梳理思路，看清方向
⏳ **时间管理建议** - 提供高效学习与生活的方法
💫 **心灵能量补给** - 给你温暖的鼓励和正能量
🍵 **轻松闲聊时刻** - 像朋友一样自在交流

你此刻最需要的是哪种呢？可以直接告诉我或选择上面的描述～"""

def handle_core_need_stage(message: str, manager: ConversationManager) -> str:
    """处理核心需求收集阶段 - 融合温暖专业表达"""
    manager.update_user_info("core_need", message.strip())
    manager.set_stage("energy_recovery")
    
    return f"""明白啦！我会记住你的需要～ {manager.get_user_info('user_nickname')} ✨

还有两个小问题，这样我就能更懂你啦！

**当你感到"能量耗尽"时，有什么专属的充电方式吗？** 🔋

比如：
🎧 听治愈系音乐 / 🐱 陪伴宠物时光 / 🌳 自然中散步 / 📖 沉浸阅读 / 🧘 正念冥想 / 🍰 享受美食...

还是正在寻找适合自己的恢复方式？说说你的能量恢复秘诀吧～"""

def handle_energy_stage(message: str, manager: ConversationManager) -> str:
    """处理能量恢复方式收集阶段 - 融合温暖专业表达"""
    manager.update_user_info("energy_recovery", message.strip())
    manager.set_stage("support_network")
    
    return f"""这种充电方式听起来好治愈！🌼 

最后一个温暖的问题：

**当你需要支持时，身边有可以依靠的人吗？** 🫂

比如：
👫 **亲密好友** - 有几个知心朋友
👨‍👩‍👧 **温暖家人** - 家人给予力量  
🧑‍🏫 **良师益友** - 师长提供指导
🫂 **有人但不愿打扰** - 不好意思麻烦别人
🌱 **正在建立支持圈** - 慢慢拓展人际网络

你的情况更接近哪一种呢？"""

def handle_support_stage(message: str, manager: ConversationManager) -> str:
    """处理支持网络收集阶段 - 融合温暖专业表达"""
    manager.update_user_info("support_network", message.strip())
    manager.set_stage("jungian_intro")
    
    return f"""谢谢你愿意分享这些～ {manager.get_user_info('user_nickname')} 💖

现在想邀请你玩个有趣的性格探索游戏！🎮

**为什么做这个测试呢？** 
这是专业的荣格八维性格评估，能帮助我们更深入地了解你的思维和行为模式～

**这样做有什么价值呢？** 💫
- 让我能提供更符合你性格的陪伴
- 帮助你发现自己的独特优势  
- 让我们的交流更有深度和效果

**会很复杂吗？** 🌈
完全不会！只需要8个精心设计的选择题，每题都聚焦核心特质～

**测试准确吗？** 🎯
题目基于心理学理论设计，虽然精简但准确性很高！

**不想做可以吗？** 🤗
当然可以跳过！但如果你愿意花几分钟，我就能更懂你，给你更贴心的支持～

准备好开始探索了吗？还是想直接聊聊心情？
(输入'开始测试'或'跳过'都可以～)"""

def handle_jungian_intro_stage(message: str, manager: ConversationManager) -> str:
    """处理荣格八维测试介绍阶段 - 融合温暖专业表达"""
    if "跳过" in message:
        manager.set_stage("emotion_what")
        return f"没问题！那我们直接进入情绪探索环节吧～\n\n**为什么要聊情绪呢？** 💡\n给情绪命名就像为心灵开灯，当我们能说'这是焦虑'而不是'我不舒服'，情绪就变得可以对话了！\n\n{manager.get_user_info('user_nickname')}，此刻你的心情更像哪几个？{' / '.join(EMOTION_TYPES)}？"
    else:
        manager.set_stage("jungian_test")
        manager.update_user_info("jungian_index", 0)
        
        question_text, _ = get_jungian_question(0)
        return f"太棒了！让我们一起开启自我发现之旅～\n\n{question_text}"

def handle_jungian_test_stage(message: str, manager: ConversationManager) -> str:
    """处理荣格八维测试进行阶段 - 与原版相同"""
    print(f"🔍 调试：进入 handle_jungian_test_stage - manager.session_data['jungian_index']: {manager.session_data.get('jungian_index', 0)}") # 新增调试日志
    current_index = manager.session_data.get("jungian_index", 0)
    
    # 🚨 关键修复：先处理当前问题答案，再更新索引获取下一个问题
    if current_index > 0: # 如果不是第一个问题，处理上一题的答案
        prev_question = JUNG_ARCHETYPES_QUESTIONS[current_index - 1]
        scores_to_add = process_jungian_answer(message, prev_question)
        
        for dimension, score in scores_to_add.items():
            manager.session_data["jungian_scores"][dimension] += score
    
    # 递增索引，为获取下一个问题做准备
    manager.update_user_info("jungian_index", current_index + 1)
    next_question_index = current_index + 1 # 这里使用递增后的索引来判断和获取下一题
    
    print(f"🔍 调试：handle_jungian_test_stage - current_index: {current_index}, JUNG_ARCHETYPES_QUESTIONS长度: {len(JUNG_ARCHETYPES_QUESTIONS)}") # 新增调试日志

    if next_question_index <= len(JUNG_ARCHETYPES_QUESTIONS): # 使用递增后的索引进行判断
        question_text, _ = get_jungian_question(next_question_index - 1) # 获取当前需要展示的问题 (即递增前的current_index)
        return question_text
    else: # 所有问题已答完
        manager.set_stage("cognitive_functions_invitation")
        
        detailed_analysis = generate_detailed_jungian_analysis(manager.session_data["jungian_scores"])
        
        return f"""🎉 **测试完成啦！** 

{detailed_analysis}

---

太棒了！我们已经找到了你的专属"性格代码"。想不想开启一个隐藏关卡，去认识一下组成你个性的 8 位"心智小精灵"？了解它们，就像是拿到了一份专属于你的、挖掘天赋潜能的藏宝图哦！✨

**为什么要了解认知功能呢？** 🤔
- 帮你理解**为什么**你会有这样的思维和行为模式
- 发现你独特的**天赋优势**，学会如何更好地发挥
- 找到你的**成长方向**，有针对性地提升自己
- 更深刻地**认识自己**，从而更好地与他人相处

你想要：
🗺️ **好啊，开始探险！** - 我想了解我的认知功能
😅 **暂时不了，谢谢** - 我们先聊聊情绪吧

请直接输入你的选择～"""

def handle_cognitive_functions_invitation_stage(message: str, manager: ConversationManager) -> str:
    """处理认知功能邀请阶段 - 新增V2.0功能"""
    if "好啊" in message or "开始探险" in message or "开始" in message or "好的" in message:
        manager.set_stage("cognitive_functions_exploration")
        manager.update_user_info("cognitive_function_index", 0)
        
        # 获取用户的MBTI类型和对应的认知功能栈
        jungian_type = calculate_jungian_type(manager.session_data["jungian_scores"])
        manager.update_user_info("user_mbti_type", jungian_type)
        
        # 开始展示第一个核心功能
        from cognitive_functions_content import get_user_cognitive_stack, format_cognitive_function_card
        
        cognitive_stack = get_user_cognitive_stack(jungian_type)
        if cognitive_stack:
            first_function = cognitive_stack[0]  # 主导功能
            function_card = format_cognitive_function_card(first_function)
            
            return f"""🎉 **太棒啦！让我们开始认知功能的探险之旅！** 

作为 **{jungian_type}** 类型，你有4个核心认知功能。我们先从你的**主导功能**开始认识～

{function_card}

这是你的"超级英雄技能"！想继续了解你的第二个核心功能吗？

输入 **'继续'** 了解下一个，或输入 **'够了'** 进入情绪聊天环节～"""
        else:
            # 如果无法获取认知功能栈，直接跳转到情绪聊天
            manager.set_stage("emotion_what")
            return f"抱歉，暂时无法获取你的认知功能信息。让我们直接进入情绪聊天吧！\n\n{manager.get_user_info('user_nickname')}，现在心里更像哪几个？{' / '.join(EMOTION_TYPES)}？"
    else:
        # 用户选择跳过
        manager.set_stage("emotion_what")
        return f"没问题呢！那我们直接进入情绪小聊天吧～\n\n**为什么要聊情绪呢？** 💡\n就像给感受贴个标签一样，当我们能准确地说出'我现在是焦虑'而不是'我就是不舒服'，情绪就不再那么可怕了！\n\n{manager.get_user_info('user_nickname')}，现在心里更像哪几个？{' / '.join(EMOTION_TYPES)}？"

def handle_cognitive_functions_exploration_stage(message: str, manager: ConversationManager) -> str:
    """处理认知功能探索阶段 - 新增V2.0功能"""
    if "够了" in message or "不了" in message or "跳过" in message:
        manager.set_stage("emotion_what")
        return f"好的！我们已经探索了你的一些核心功能，希望对你有帮助～\n\n现在让我们进入情绪聊天环节：\n\n{manager.get_user_info('user_nickname')}，现在心里更像哪几个？{' / '.join(EMOTION_TYPES)}？"
    
    # 继续展示下一个功能
    current_index = manager.session_data.get("cognitive_function_index", 0)
    next_index = current_index + 1
    
    from cognitive_functions_content import get_user_cognitive_stack, format_cognitive_function_card
    
    jungian_type = manager.get_user_info("user_mbti_type")
    cognitive_stack = get_user_cognitive_stack(jungian_type)
    
    if next_index < len(cognitive_stack) and next_index < 4:  # 只展示前4个核心功能
        function_code = cognitive_stack[next_index]
        function_card = format_cognitive_function_card(function_code)
        
        function_names = ["主导功能", "辅助功能", "第三功能", "劣势功能"]
        function_name = function_names[next_index] if next_index < len(function_names) else f"第{next_index+1}功能"
        
        manager.update_user_info("cognitive_function_index", next_index)
        
        return f"""✨ **接下来是你的{function_name}：**

{function_card}

你还想继续了解吗？

输入 **'继续'** 了解下一个，或输入 **'够了'** 进入情绪聊天环节～"""
    else:
        # 已经展示完所有核心功能
        manager.set_stage("emotion_what")
        return f"""🎊 **恭喜你！我们已经探索完你的所有核心认知功能啦！**

现在你更深入地了解了自己的"心智工具箱"。记住，每一种功能都是你独特的天赋，善用它们，你就能发挥出最好的自己！

想了解其他4个"隐藏功能"的话，可以随时在聊天中说"我想了解更多认知功能"哦～

现在让我们进入情绪聊天环节：

{manager.get_user_info('user_nickname')}，现在心里更像哪几个？{' / '.join(EMOTION_TYPES)}？"""

def handle_emotion_what_stage(message: str, manager: ConversationManager) -> str:
    """处理情绪澄清第一阶段 - 融合温暖专业表达"""
    manager.update_user_info("current_emotion", message.strip())
    manager.set_stage("emotion_choice")
    
    return f"嗯，感受到你的心情了… 🌸 如果现在不想深究原因，完全没关系！我们可以：\n\nA. 你尽情倾诉，我静静陪伴👂\nB. 一起安静呼吸，享受当下☁️\nC. 准备好探索情绪背后的故事\n\n选择最适合你此刻状态的选项吧～"

def handle_emotion_choice_stage(message: str, manager: ConversationManager, api_response_func=None) -> str:
    """处理情绪陪伴选择阶段 - 融合温暖专业表达"""
    choice = message.strip().upper()
    
    if choice in ["A", "B"]:
        manager.set_stage("pure_listen")
        
        listen_messages = [
            {
                "role": "system",
                "content": "你是温暖专业的心理陪伴者喵呜。现在处于倾听模式，专注共情理解，不提供解决方案。用温暖接纳的语言陪伴用户。"
            },
            {
                "role": "user",
                "content": f"用户选择了倾听模式，当前情绪：{manager.get_user_info('current_emotion')}。请给予温暖的陪伴回应。"
            }
        ]
        
        if api_response_func:
            listen_response = api_response_func(listen_messages)
        else:
            listen_response = "我在这里，全然地陪伴你。你的感受很重要，我愿做你情绪的容器。"
            
        return f"{listen_response}\n\n当你需要倾诉时，我都在这里。如果想探讨原因，随时告诉我'我想聊聊为什么'～"
        
    elif choice == "C":
        manager.set_stage("emotion_why")
        return f"很欣赏你探索自我的勇气！{manager.get_user_info('user_nickname')}，发生了什么让这种情绪出现呢？(比如：我感到焦虑，是因为...)"
    else:
        return "请选择A、B或C哦～我想用最适合的方式陪伴你！"

def handle_pure_listen_stage(message: str, manager: ConversationManager, api_response_func=None) -> str:
    """处理纯粹倾听模式 - 与原版相同"""
    if "为什么" in message or "建议" in message or "怎么办" in message:
        manager.set_stage("emotion_why")
        return f"好的，我们来聊聊'为什么'会这样呢？{manager.get_user_info('user_nickname')}，发生了什么，让它登场？试试回想原因？"
    else:
        listen_messages = [
            {
                "role": "system",
                "content": "你是一个温暖可爱的大学生心理小助手，名叫'喵呜'。继续倾听用户，给出温暖的共情回复。"
            },
            {
                "role": "user",
                "content": message
            }
        ]
        
        if api_response_func:
            return api_response_func(listen_messages)
        else:
            return "我理解你的感受，继续说吧，我在这里陪着你。"

def handle_emotion_why_stage(message: str, manager: ConversationManager) -> str:
    """处理情绪澄清第二阶段 - 融合温暖专业表达"""
    manager.update_user_info("last_user_reason", message.strip())
    manager.set_stage("emotion_how")
    
    return f"""嗯，我理解了。现在，让我们一起做三次深呼吸：
1. 深深吸气4秒...感受空气充满胸腔
2. 缓缓呼气6秒...释放所有紧张
3. 重复两次...

然后思考：针对这个情况，明天可以做的一个小行动是什么？(15字以内，越简单越好)
比如：给朋友发条问候 / 散步10分钟 / 写三件感恩的事"""

def handle_emotion_how_stage(message: str, manager: ConversationManager, api_response_func=None) -> str:
    """处理情绪澄清第三阶段 - 与原版相同"""
    manager.update_user_info("last_user_action", message.strip())
    manager.set_stage("happiness_log")
    
    action_messages = [
        {
            "role": "system",
            "content": "你是一个温暖可爱的大学生心理小助手，名叫'喵呜'。用户刚刚制定了行动计划，请给出鼓励和支持的回复。"
        },
        {
            "role": "user",
            "content": f"用户的行动计划是：{message}。请给出温暖的鼓励回复。"
        }
    ]
    
    if api_response_func:
        encouragement = api_response_func(action_messages)
    else:
        encouragement = "太棒了！你已经迈出了重要的一步，我相信你能做到的！"
        
    return f"{encouragement}\n\n当你开始行动就已经走在胜利的道路上啦！✨\n\n**最后一个小惊喜～** 🎁\n既然我们聊了那么多，不如记录一件今天的小确幸吧！可以是很小很小的事情哦～\n\n比如：喝到了好喝的奶茶 / 看到了漂亮的云朵 / 和喵呜聊天很开心\n\n愿意分享吗？（输入'跳过'也完全没问题～）"

def handle_happiness_log_stage(message: str, manager: ConversationManager, api_response_func=None) -> str:
    """处理幸福日志记录阶段 - 融合温暖专业表达"""
    if "跳过" in message:
        manager.set_stage("general_chat")
        return f"完全尊重你的选择～ {manager.get_user_info('user_nickname')}！记住，生活中的小美好永远值得期待～\n\n我随时在这里等你来聊聊～"
    else:
        manager.set_stage("general_chat")
        happiness_content = message.strip()
        
        happiness_messages = [
            {
                "role": "system", 
                "content": "你是温暖专业的心理陪伴者喵呜。用户分享了小确幸，请给予真诚温暖的回应，突出其中的积极意义。语气亲切自然，80字以内。"
            },
            {
                "role": "user",
                "content": f"用户分享：{happiness_content}。请给予温暖回应。"
            }
        ]
        
        if api_response_func:
            happiness_feedback = api_response_func(happiness_messages)
        else:
            happiness_feedback = "真美好！这种小确幸是生活的珍贵礼物～"
            
        return f"""🌼 {happiness_feedback} 

你知道吗？记录美好时刻就像在心灵花园种花，每次回忆都会绽放新的芬芳～

{manager.get_user_info('user_nickname')}，感谢你分享这份温暖！我随时在这里等你～"""

def handle_general_chat_stage(message: str, manager: ConversationManager, api_response_func=None) -> str:
    """处理通用聊天模式 - 与原版相同，增加认知功能查询支持"""
    
    # 检查是否想了解认知功能
    if ("认知功能" in message or "八维" in message or "心智功能" in message) and ("了解" in message or "学习" in message or "知道" in message):
        from cognitive_functions_content import get_all_functions_overview
        return f"""🧠 **太棒了！你想了解认知功能！** 

{get_all_functions_overview()}

你想了解哪个具体的功能呢？可以直接说"我想了解Ti"或"我想了解外向直觉"等。

或者你也可以说"我想了解我的认知功能"，我会根据你的MBTI类型({manager.get_user_info('user_mbti_type', '未知')})给你专门的解析！✨"""
    
    # 检查是否询问特定的认知功能
    function_queries = {
        "Si": ["Si", "内向实感", "实感内向"],
        "Se": ["Se", "外向实感", "实感外向"], 
        "Ni": ["Ni", "内向直觉", "直觉内向"],
        "Ne": ["Ne", "外向直觉", "直觉外向"],
        "Ti": ["Ti", "内向思考", "思考内向"],
        "Te": ["Te", "外向思考", "思考外向"],
        "Fi": ["Fi", "内向情感", "情感内向"],
        "Fe": ["Fe", "外向情感", "情感外向"]
    }
    
    for func_code, keywords in function_queries.items():
        if any(keyword in message for keyword in keywords):
            from cognitive_functions_content import format_cognitive_function_card
            function_card = format_cognitive_function_card(func_code)
            return f"✨ **你想了解 {func_code} 功能！**\n\n{function_card}\n\n还想了解其他功能吗？随时告诉我哦～"
    
    # 检查是否想了解自己的认知功能
    if "我的认知功能" in message or "我的八维" in message or "我的心智功能" in message:
        jungian_type = manager.get_user_info('user_mbti_type')
        if jungian_type:
            from cognitive_functions_content import format_user_cognitive_analysis
            user_analysis = format_user_cognitive_analysis(jungian_type)
            return f"{user_analysis}\n\n想了解任何特定功能的详细信息，随时告诉我哦！"
        else:
            return "看起来你还没有完成荣格八维测试呢！要不要现在试试？这样我就能给你专属的认知功能分析啦～"
    
    user_profile = f"用户昵称：{manager.get_user_info('user_nickname')}\n"
    
    if manager.get_user_info("current_emotion"):
        user_profile += f"当前心情：{manager.get_user_info('current_emotion')}\n"
    
    if manager.get_user_info("pressure_sources"):
        user_profile += f"主要压力源：{manager.get_user_info('pressure_sources')}\n"
    
    if manager.get_user_info("core_need"):
        user_profile += f"核心需求：{manager.get_user_info('core_need')}\n"
    
    if manager.get_user_info("energy_recovery"):
        user_profile += f"能量恢复方式：{manager.get_user_info('energy_recovery')}\n"
    
    if manager.get_user_info("support_network"):
        user_profile += f"支持网络：{manager.get_user_info('support_network')}\n"
    
    jungian_type = calculate_jungian_type(manager.session_data["jungian_scores"]) if any(manager.session_data["jungian_scores"].values()) else "未知"
    if jungian_type != "未知":
        user_profile += f"荣格八维类型：{jungian_type}\n"
    
    response_style = ""
    core_need = manager.get_user_info("core_need")
    if "听你说说" in core_need:
        response_style = "主要采用倾听和共情的方式回应，少给建议，多给情感支持。"
    elif "解压小妙招" in core_need:
        response_style = "重点分享实用的解压技巧和方法。"
    elif "纠结的选择" in core_need:
        response_style = "帮助分析利弊，提供决策思路。"
    elif "学习" in core_need or "时间管理" in core_need:
        response_style = "分享高效的学习方法和时间管理技巧。"
    elif "灵感鼓励" in core_need:
        response_style = "多给正能量和鼓励，激发动力。"
    elif "放松聊聊天" in core_need:
        response_style = "保持轻松愉快的聊天氛围，像朋友一样交流。"
    else:
        response_style = "根据具体情况灵活调整回应方式。"
    
    chat_messages = [
        {
            "role": "system",
            "content": f"你是温暖专业的心理陪伴者喵呜。基于用户信息深度陪伴：\n\n{user_profile}\n回应原则：{response_style}\n\n用温暖专业的语气，结合心理学知识给予支持。记住用户的具体情况，提供有温度的回应。"
        },
        {
            "role": "user", 
            "content": message
        }
    ]
    
    if api_response_func:
        return api_response_func(chat_messages)
    else:
        return "我理解你的感受，我会一直在这里陪伴你。"

# ========================================================================
# 🎯 主对话处理函数 - 与原版相同
# ========================================================================

def process_conversation(message: str, manager: ConversationManager, api_response_func=None, api_comfort_func=None, api_emergency_func=None) -> str:
    """主对话处理函数 - 与原版相同的逻辑"""
    current_stage = manager.get_current_stage()
    print(f"🔍 调试：进入 process_conversation，当前阶段: {current_stage}") # 新增调试日志
    
    # 紧急情况检测（最高优先级）
    if enhanced_emergency_detection(message, api_emergency_func):
        manager.set_stage("emergency_intervention")
        return """🚨 我感受到你可能正经历艰难时刻，这让我很关心你的安全。

**请立即联系专业支持：**
• 全国心理援助热线：400-161-9995（24小时）
• 北京心理危机中心：010-82951332
• 上海心理援助热线：021-64389888
• 紧急情况请拨打：110 或 120

你的存在本身就是价值，此刻的痛苦会过去。专业咨询师能提供最有效的支持。

我会继续陪伴你，但请先联系专业帮助好吗？"""
    
    # 如果已经进入紧急干预状态
    if current_stage == "emergency_intervention":
        if enhanced_emergency_detection(message, api_emergency_func):
            return """我一直在这里关心着你。你的感受很重要，你的生命更重要。

如果还没有联系专业帮助，请现在就行动：
• **全国心理危机干预热线**: 400-161-9995
• **当地医院急诊科**也可以提供心理危机干预

你愿意告诉我你现在在哪里吗？我想确认你是安全的。"""
        else:
            manager.set_stage("general_chat")
            return "谢谢你愿意继续和我聊天。我会一直陪伴你，如果需要专业帮助，随时可以寻求支持。现在想聊点什么呢？✨"
    
    # 根据当前阶段处理对话
    stage_handlers = {
        "welcome": handle_welcome_stage,
        "ask_nickname": handle_nickname_stage,
        "user_info_mood": handle_mood_stage,
        "pressure_radar": lambda msg, mgr: handle_pressure_stage(msg, mgr, api_comfort_func),
        "pressure_comfort_only": handle_comfort_stage,
        "core_need": handle_core_need_stage,
        "energy_recovery": handle_energy_stage,
        "support_network": handle_support_stage,
        "jungian_intro": handle_jungian_intro_stage,
        "jungian_test": handle_jungian_test_stage,
        "cognitive_functions_invitation": handle_cognitive_functions_invitation_stage,
        "cognitive_functions_exploration": handle_cognitive_functions_exploration_stage,
        "emotion_what": handle_emotion_what_stage,
        "emotion_choice": lambda msg, mgr: handle_emotion_choice_stage(msg, mgr, api_response_func),
        "pure_listen": lambda msg, mgr: handle_pure_listen_stage(msg, mgr, api_response_func),
        "emotion_why": handle_emotion_why_stage,
        "emotion_how": lambda msg, mgr: handle_emotion_how_stage(msg, mgr, api_response_func),
        "happiness_log": lambda msg, mgr: handle_happiness_log_stage(msg, mgr, api_response_func),
        "general_chat": lambda msg, mgr: handle_general_chat_stage(msg, mgr, api_response_func)
    }
    
    handler = stage_handlers.get(current_stage)
    if handler:
        return handler(message, manager)
    else:
        return "喵呜有点迷糊了，让我们重新开始吧！有什么想聊的吗？"