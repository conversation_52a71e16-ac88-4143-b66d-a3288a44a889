"""
SQLite 数据库管理模块 - Camel-AI 优化版
========================================

功能：
1. 用户数据持久化存储
2. 对话历史记录管理  
3. 荣格八维测试结果存储
4. 幸福日志数据管理
5. 用户档案和状态追踪

设计原则：
- 完全替代原有内存存储
- 保持所有原有功能不变
- 支持多用户和历史记录
- 为Camel-AI Agent提供记忆能力
"""

import sqlite3
import json
import datetime
from typing import Dict, List, Optional, Any
from contextlib import contextmanager
import threading # 🚨 关键修复：导入threading模块

# 数据库文件路径
DB_FILE = 'mental_health_agent.db'

class DatabaseManager:
    """数据库管理器 - 提供所有数据持久化功能"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.db_file = DB_FILE
        self._lock = threading.Lock() # 🚨 关键修复：初始化线程锁
        self.setup_database()
    
    @contextmanager
    def get_db_connection(self):
        """
        获取数据库连接的上下文管理器
        自动处理连接的打开和关闭、事务提交和回滚，以及异常时的连接清理。
        🚨 关键修复：重构上下文管理器，遵循标准模式，解决锁定和生成器错误。
        """
        conn = None
        print(f"🔍 DB日志：尝试获取数据库连接... (DB文件: {self.db_file})") # 🚨 新增日志
        try:
            conn = sqlite3.connect(self.db_file, timeout=60.0)  # 增加到60秒超时
            conn.row_factory = sqlite3.Row  # 允许按列名访问数据
            
            # 设置WAL模式以支持并发读写
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=memory")
            conn.execute("PRAGMA busy_timeout=60000")  # 60秒忙碌超时
            
            yield conn # 提供连接给with块
            conn.commit() # with块无异常，提交事务
            print("✅ DB日志：事务提交成功，连接即将关闭。") # 🚨 新增日志
        except sqlite3.OperationalError as e:
            if conn:
                conn.rollback() # 操作错误时回滚
            print(f"❌ 数据库操作失败: {e}")
            raise # 重新抛出异常
        except Exception as e:
            if conn:
                conn.rollback() # 其他错误时回滚
            print(f"⚠️ 数据库连接错误: {e}")
            raise # 重新抛出异常
        finally:
            if conn:
                try:
                    conn.close() # 确保连接最终关闭
                    print("✅ DB日志：数据库连接已成功关闭。") # 🚨 新增日志
                except sqlite3.Error as close_error:
                    print(f"⚠️ DB日志：数据库连接关闭时出错: {close_error}") # 🚨 新增日志
    
    def setup_database(self):
        """
        创建所有必要的数据库表
        包含用户、对话、档案、测试结果等所有表结构
        """
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 🚨 关键修复：先执行数据库迁移
            self._migrate_database(cursor)
            
            # 用户基本信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nickname TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    last_active_at TEXT,
                    current_state TEXT DEFAULT 'INITIAL_GREETING'
                )
            ''')
            
            # 对话历史记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    role TEXT NOT NULL,  -- 'user' or 'assistant'
                    content TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ''')
            
            # 用户详细档案表（JSON格式存储复杂数据）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id INTEGER PRIMARY KEY,
                    current_nickname TEXT,  -- 🚨 修复：添加缺失的昵称字段
                    jungian_scores TEXT,  -- JSON格式存储荣格八维得分
                    jungian_test_answers TEXT,  -- JSON格式存储荣格测试答案
                    preferences TEXT,     -- JSON格式存储偏好列表
                    emotion_trend TEXT,   -- JSON格式存储情绪趋势
                    action_history TEXT,  -- JSON格式存储行动历史
                    current_emotion TEXT,
                    current_pressure_sources TEXT,
                    current_desire TEXT,
                    current_energy_ritual TEXT,
                    current_support_network TEXT,
                    is_onboarding_complete INTEGER DEFAULT 0,  -- 🚨 修复：添加引导完成标志
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ''')
            
            # 幸福日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS happiness_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    date TEXT NOT NULL,
                    content TEXT NOT NULL,
                    level INTEGER NOT NULL,  -- 1-10的开心程度
                    timestamp TEXT NOT NULL,
                    source TEXT,  -- 幸福来源分类
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ''')
            
            # 荣格八维测试结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS jungian_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    test_date TEXT NOT NULL,
                    question_answers TEXT NOT NULL,  -- JSON格式存储所有答案
                    dimension_scores TEXT NOT NULL,  -- JSON格式存储维度得分
                    result_summary TEXT,  -- 测试结果总结
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ''')
            
            # 情绪日记表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS diary_entries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    date TEXT NOT NULL,
                    content TEXT NOT NULL,
                    emotions TEXT,  -- JSON格式存储情绪列表
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ''')
            
            # 用户喜好表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    date TEXT NOT NULL,
                    item TEXT NOT NULL,
                    description TEXT,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ''')
            
            conn.commit()
            print("✅ 数据库表初始化完成")
    
    def _migrate_database(self, cursor):
        """
        数据库结构迁移函数
        添加缺失的字段到现有表中
        """
        try:
            # 检查user_profiles表是否存在current_nickname字段
            cursor.execute("PRAGMA table_info(user_profiles)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'current_nickname' not in columns:
                cursor.execute("ALTER TABLE user_profiles ADD COLUMN current_nickname TEXT")
                print("✅ 添加current_nickname字段")
            
            if 'jungian_test_answers' not in columns:
                cursor.execute("ALTER TABLE user_profiles ADD COLUMN jungian_test_answers TEXT")
                print("✅ 添加jungian_test_answers字段")
                
            if 'is_onboarding_complete' not in columns:
                cursor.execute("ALTER TABLE user_profiles ADD COLUMN is_onboarding_complete INTEGER DEFAULT 0")
                print("✅ 添加is_onboarding_complete字段")
                
        except Exception as e:
            # 如果表不存在，忽略错误（将在后续CREATE TABLE中创建）
            print(f"📝 数据库迁移信息: {e}")
            pass
    
    # ========================================================================
    # 用户管理方法
    # ========================================================================
    
    def create_user(self, nickname: str) -> int:
        """
        创建新用户
        Args:
            nickname: 用户昵称
        Returns:
            int: 新用户的ID
        """
        with self._lock: # 🚨 关键修复：加锁，确保并发安全
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                now = datetime.datetime.now().isoformat()
                
                cursor.execute(
                    "INSERT INTO users (nickname, created_at, last_active_at) VALUES (?, ?, ?)",
                    (nickname, now, now)
                )
                user_id = cursor.lastrowid
                
                # 同时创建用户档案记录
                cursor.execute(
                    "INSERT INTO user_profiles (user_id) VALUES (?)",
                    (user_id,)
                )
                
                conn.commit() # 移除此行，由上下文管理器处理
                print(f"✅ 创建新用户: {nickname} (ID: {user_id})")
                return user_id
    
    def get_user(self, user_id: int) -> Optional[Dict]:
        """
        获取用户信息
        Args:
            user_id: 用户ID
        Returns:
            Dict: 用户信息字典，如果不存在返回None
        """
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
            row = cursor.fetchone()
            
            if row:
                return dict(row)
            return None
    
    def get_all_users(self) -> List[Dict]:
        """
        获取所有用户列表
        Returns:
            List[Dict]: 用户信息列表
        """
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT id, nickname, created_at FROM users ORDER BY last_active_at DESC")
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
    
    def update_user_state(self, user_id: int, state: str):
        """
        更新用户当前对话状态
        Args:
            user_id: 用户ID
            state: 新的对话状态
        """
        with self._lock: # 🚨 关键修复：加锁，确保并发安全
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                now = datetime.datetime.now().isoformat()
                
                cursor.execute(
                    "UPDATE users SET current_state = ?, last_active_at = ? WHERE id = ?",
                    (state, now, user_id)
                )
                # conn.commit() # 移除此行，由上下文管理器处理
    
    def delete_user(self, user_id: int) -> bool:
        """
        🗑️ 删除用户及其所有相关数据
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 删除成功返回True，失败返回False
        """
        try:
            with self._lock: # 🚨 关键修复：加锁，确保并发安全
                with self.get_db_connection() as conn:
                    cursor = conn.cursor()
                    
                    # 按依赖关系顺序删除数据
                    # 1. 删除对话历史
                    cursor.execute("DELETE FROM conversations WHERE user_id = ?", (user_id,))
                    
                    # 2. 删除荣格测试结果
                    cursor.execute("DELETE FROM jungian_results WHERE user_id = ?", (user_id,))
                    
                    # 3. 删除幸福日志
                    cursor.execute("DELETE FROM happiness_logs WHERE user_id = ?", (user_id,))
                    
                    # 4. 删除情绪日记
                    cursor.execute("DELETE FROM diary_entries WHERE user_id = ?", (user_id,))
                    
                    # 5. 删除用户喜好
                    cursor.execute("DELETE FROM preferences WHERE user_id = ?", (user_id,))
                    
                    # 6. 删除用户档案
                    cursor.execute("DELETE FROM user_profiles WHERE user_id = ?", (user_id,))
                    
                    # 7. 最后删除用户基本信息
                    cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))
                    
                    # 检查用户是否存在并被删除
                    if cursor.rowcount == 0:
                        print(f"⚠️ 用户ID {user_id} 不存在")
                        # 如果用户不存在，上下文管理器会自动回滚，无需手动 ROLLBACK
                        return False
                    
                    # 提交事务由上下文管理器处理
                    print(f"✅ 用户ID {user_id} 及其所有数据已成功删除")
                    return True
                    
        except Exception as e:
            print(f"❌ 删除用户ID {user_id} 失败: {e}")
            # 异常时回滚由上下文管理器处理
            return False
    
    def get_user_data_summary(self, user_id: int) -> Dict:
        """
        📊 获取用户数据摘要（删除前确认用）
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict: 用户数据摘要
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 获取基本信息
                cursor.execute("SELECT nickname, created_at FROM users WHERE id = ?", (user_id,))
                user_info = cursor.fetchone()
                
                if not user_info:
                    return {"error": "用户不存在"}
                
                # 统计各类数据数量
                stats = {}
                
                # 对话记录数量
                cursor.execute("SELECT COUNT(*) FROM conversations WHERE user_id = ?", (user_id,))
                stats["conversations"] = cursor.fetchone()[0]
                
                # 荣格测试数量
                cursor.execute("SELECT COUNT(*) FROM jungian_results WHERE user_id = ?", (user_id,))
                stats["jungian_tests"] = cursor.fetchone()[0]
                
                # 幸福日志数量
                cursor.execute("SELECT COUNT(*) FROM happiness_logs WHERE user_id = ?", (user_id,))
                stats["happiness_logs"] = cursor.fetchone()[0]
                
                # 情绪日记数量
                cursor.execute("SELECT COUNT(*) FROM diary_entries WHERE user_id = ?", (user_id,))
                stats["diary_entries"] = cursor.fetchone()[0]
                
                # 用户喜好数量
                cursor.execute("SELECT COUNT(*) FROM preferences WHERE user_id = ?", (user_id,))
                stats["preferences"] = cursor.fetchone()[0]
                
                return {
                    "user_id": user_id,
                    "nickname": user_info[0],
                    "created_at": user_info[1],
                    "data_stats": stats,
                    "total_records": sum(stats.values())
                }
            
        except Exception as e:
            print(f"❌ 获取用户数据摘要失败: {e}")
            return {"error": str(e)}
    
    # ========================================================================
    # 对话管理方法
    # ========================================================================
    
    def save_conversation(self, user_id: int, role: str, content: str):
        """
        保存对话记录
        Args:
            user_id: 用户ID
            role: 角色 ('user' 或 'assistant')
            content: 对话内容
        """
        with self._lock: # 🚨 关键修复：加锁，确保并发安全
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                now = datetime.datetime.now().isoformat()
                
                cursor.execute(
                    "INSERT INTO conversations (user_id, role, content, timestamp) VALUES (?, ?, ?, ?)",
                    (user_id, role, content, now)
                )
                # conn.commit() # 移除此行，由上下文管理器处理
    
    def get_conversation_history(self, user_id: int, limit: int = 10) -> List[Dict]:
        """
        获取用户对话历史
        Args:
            user_id: 用户ID
            limit: 获取的最大记录数
        Returns:
            List[Dict]: 对话历史列表
        """
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT role, content, timestamp FROM conversations WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?",
                (user_id, limit)
            )
            rows = cursor.fetchall()
            return [dict(row) for row in reversed(rows)]  # 反转顺序，最早的在前
    
    # ========================================================================
    # 用户档案管理方法
    # ========================================================================
    
    def update_user_profile(self, user_id: int, **kwargs):
        """
        更新用户档案信息
        Args:
            user_id: 用户ID
            **kwargs: 要更新的字段和值
        """
        with self._lock: # 🚨 关键修复：加锁，确保并发安全
            with self.get_db_connection() as conn:
                print(f"🔍 DB日志：更新用户档案开始，用户ID: {user_id}, 更新字段: {kwargs.keys()}") # 🚨 新增日志
                cursor = conn.cursor()
                
                # 构建UPDATE语句
                set_clauses = []
                values = []
                
                for key, value in kwargs.items():
                    if key in ['jungian_scores', 'jungian_test_answers', 'preferences', 'emotion_trend', 'action_history']:
                        # JSON字段
                        set_clauses.append(f"{key} = ?")
                        json_value = json.dumps(value) if value is not None else None
                        values.append(json_value)
                        
                        # 🚨 调试日志：显示保存的JSON数据
                        if key == 'jungian_scores':
                            print(f"✅ 保存用户{user_id}的荣格得分到数据库: {value} -> JSON: {json_value}")
                            
                    else:
                        # 普通字段
                        set_clauses.append(f"{key} = ?")
                        values.append(value)
                
                if set_clauses:
                    values.append(user_id)
                    query = f"UPDATE user_profiles SET {', '.join(set_clauses)} WHERE user_id = ?"
                    cursor.execute(query, values)
                    print(f"✅ DB日志：用户档案更新完成，用户ID: {user_id}") # 🚨 新增日志
                    # conn.commit() # 移除此行，由上下文管理器处理
    
    def get_user_profile(self, user_id: int) -> Dict:
        """
        获取用户档案信息，确保返回完整的用户档案数据
        Args:
            user_id: 用户ID
        Returns:
            Dict: 用户档案字典，保证包含所有必要字段
        """
        with self.get_db_connection() as conn:
            print(f"🔍 DB日志：获取用户档案开始，用户ID: {user_id}") # 🚨 新增日志
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM user_profiles WHERE user_id = ?", (user_id,))
            row = cursor.fetchone()
            
            # 创建默认档案模板
            default_profile = {
                'user_id': user_id,
                'jungian_scores': {},
                'jungian_test_answers': [],  # 🚨 关键修复：确保这个字段始终是数组
                'preferences': {},
                'emotion_trend': {},
                'action_history': {},
                'current_nickname': '同学',
                'current_emotion': None,
                'current_pressure_sources': None,
                'current_desire': None,
                'current_energy_ritual': None,
                'current_support_network': None,
                'is_onboarding_complete': False,
            }
            
            if row:
                profile = dict(row)
                # 🚨 关键修复：更健壮的JSON字段解析逻辑
                for key in ['jungian_scores', 'jungian_test_answers', 'preferences', 'emotion_trend', 'action_history']:
                    json_value = profile.get(key)
                    if json_value is not None and json_value != '' and json_value != 'null':
                        try:
                            parsed_value = json.loads(json_value)
                            profile[key] = parsed_value if parsed_value is not None else default_profile[key]
                            
                            # 🚨 调试日志：显示成功解析的JSON数据
                            if key == 'jungian_scores':
                                print(f"✅ 成功读取用户{user_id}的荣格得分 (JSON解析后): {parsed_value}")
                                
                        except (json.JSONDecodeError, TypeError) as e:
                            print(f"⚠️ 用户{user_id}的{key}字段JSON解析失败: {e}, 原始值: {json_value}")
                            profile[key] = default_profile[key]
                    else:
                        profile[key] = default_profile[key]
                        if key == 'jungian_scores':
                            print(f"⚠️ 用户{user_id}的荣格得分字段为空或无效 (从数据库读取): {json_value}")
                
                # 确保其他必要字段存在
                for key, default_value in default_profile.items():
                    if key not in profile or profile[key] is None:
                        profile[key] = default_value
                print(f"✅ DB日志：获取用户档案完成，用户ID: {user_id}, 档案包含 {len(profile)} 个字段。") # 🚨 新增日志
                return profile
            else:
                # 如果档案不存在，返回默认档案
                print(f"⚠️ 用户{user_id}档案不存在，返回默认档案")
                print(f"✅ DB日志：获取用户档案完成，用户ID: {user_id}, 返回默认档案。") # 🚨 新增日志
                return default_profile

    def get_summarized_user_profile(self, user_id: int) -> str:
        """
        获取用户档案的精简摘要，用于注入到大模型的Prompt中，提供长期记忆上下文。
        Args:
            user_id: 用户ID
        Returns:
            str: 用户档案的Markdown格式摘要
        """
        profile = self.get_user_profile(user_id)
        
        summary_parts = []
        
        nickname = profile.get('current_nickname', '同学')
        if nickname and nickname != '同学':
            summary_parts.append(f"- 昵称: {nickname}")
        
        current_emotion = profile.get('current_emotion')
        if current_emotion:
            summary_parts.append(f"- 当前心情: {current_emotion}")
            
        current_pressure_sources = profile.get('current_pressure_sources')
        if current_pressure_sources:
            summary_parts.append(f"- 主要压力源: {current_pressure_sources}")
            
        current_desire = profile.get('current_desire')
        if current_desire:
            summary_parts.append(f"- 核心需求: {current_desire}")
            
        jungian_scores = profile.get('jungian_scores')
        if jungian_scores and any(jungian_scores.values()):
            from conversation_flow_modelscope import calculate_jungian_type
            mbti_type = calculate_jungian_type(jungian_scores)
            summary_parts.append(f"- MBTI类型: {mbti_type}")
            
        # TODO: 可以根据需求添加更多字段，如幸福日志摘要、行动历史等
        
        if summary_parts:
            return "\n".join(["【用户核心档案】", *summary_parts, ""]) # 添加空行以保持格式
        return ""

    # ========================================================================
    # 荣格八维测试方法
    # ========================================================================
    
    def save_jungian_test_result(self, user_id: int, question_answers: List, 
                                dimension_scores: Dict, result_summary: str):
        """
        保存荣格八维测试结果
        Args:
            user_id: 用户ID
            question_answers: 问题答案列表
            dimension_scores: 维度得分字典
            result_summary: 结果总结
        """
        with self._lock: # 🚨 关键修复：加锁，确保并发安全
            with self.get_db_connection() as conn:
                print(f"🔍 DB日志：保存荣格测试结果开始，用户ID: {user_id}") # 🚨 新增日志
                print(f"🔍 DB日志：问题答案: {len(question_answers)} 个，维度得分: {dimension_scores}") # 🚨 新增日志
                cursor = conn.cursor()
                now = datetime.datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT INTO jungian_results 
                    (user_id, test_date, question_answers, dimension_scores, result_summary, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    user_id,
                    datetime.date.today().isoformat(),
                    json.dumps(question_answers),
                    json.dumps(dimension_scores),
                    result_summary,
                    now
                ))
                print(f"✅ DB日志：保存荣格测试结果到jungian_results表完成，用户ID: {user_id}") # 🚨 新增日志
                
                # 同时更新用户档案中的荣格得分
                # 🚨 关键修复：移除嵌套调用 update_user_profile，直接在此事务中更新 user_profiles
                cursor.execute(
                    "UPDATE user_profiles SET jungian_scores = ? WHERE user_id = ?",
                    (json.dumps(dimension_scores), user_id)
                )
                print(f"✅ 在save_jungian_test_result中直接更新用户{user_id}的档案得分: {dimension_scores}")
                # conn.commit() # 移除此行，由上下文管理器处理
    
    def get_jungian_test_results(self, user_id: int) -> List[Dict]:
        """
        获取用户的荣格八维测试结果
        Args:
            user_id: 用户ID
        Returns:
            List[Dict]: 测试结果列表
        """
        with self.get_db_connection() as conn:
            print(f"🔍 DB日志：获取荣格测试结果开始，用户ID: {user_id}") # 🚨 新增日志
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM jungian_results WHERE user_id = ? ORDER BY created_at DESC",
                (user_id,)
            )
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                result = dict(row)
                result['question_answers'] = json.loads(result['question_answers'])
                result['dimension_scores'] = json.loads(result['dimension_scores'])
                results.append(result)
            
            print(f"✅ DB日志：获取荣格测试结果完成，用户ID: {user_id}, 返回 {len(results)} 条记录。") # 🚨 新增日志
            return results
    
    # ========================================================================
    # 幸福日志方法
    # ========================================================================
    
    def save_happiness_log(self, user_id: int, date: str, content: str, 
                          level: int, source: str = "日常"):
        """
        保存幸福日志
        Args:
            user_id: 用户ID
            date: 日期
            content: 幸福内容
            level: 开心程度 (1-10)
            source: 幸福来源
        """
        with self._lock: # 🚨 关键修复：加锁，确保并发安全
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                now = datetime.datetime.now().isoformat()
            
                cursor.execute('''
                    INSERT INTO happiness_logs (user_id, date, content, level, timestamp, source)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user_id, date, content, level, now, source))
                # conn.commit() # 移除此行，由上下文管理器处理
    
    def get_happiness_logs(self, user_id: int, start_date: str = None, 
                          end_date: str = None) -> List[Dict]:
        """
        获取幸福日志
        Args:
            user_id: 用户ID
            start_date: 开始日期 (可选)
            end_date: 结束日期 (可选)
        Returns:
            List[Dict]: 幸福日志列表
        """
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM happiness_logs WHERE user_id = ?"
            params = [user_id]
            
            if start_date:
                query += " AND date >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND date <= ?"
                params.append(end_date)
            
            query += " ORDER BY date DESC, timestamp DESC"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
    
    # ========================================================================
    # 日记和喜好管理方法
    # ========================================================================
    
    def save_diary_entry(self, user_id: int, content: str, emotions: List[str]):
        """
        保存情绪日记
        Args:
            user_id: 用户ID
            content: 日记内容
            emotions: 情绪列表
        """
        with self._lock: # 🚨 关键修复：加锁，确保并发安全
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                today = datetime.date.today().isoformat()
            
                cursor.execute('''
                    INSERT INTO diary_entries (user_id, date, content, emotions)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, today, content, json.dumps(emotions)))
                # conn.commit() # 移除此行，由上下文管理器处理
    
    def get_diary_entries(self, user_id: int) -> List[Dict]:
        """
        获取情绪日记列表
        Args:
            user_id: 用户ID
        Returns:
            List[Dict]: 日记列表
        """
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM diary_entries WHERE user_id = ? ORDER BY date DESC",
                (user_id,)
            )
            rows = cursor.fetchall()
            
            entries = []
            for row in rows:
                entry = dict(row)
                entry['emotions'] = json.loads(entry['emotions'])
                entries.append(entry)
            
            return entries
    
    def save_preference(self, user_id: int, item: str, description: str):
        """
        保存用户喜好
        Args:
            user_id: 用户ID
            item: 喜好项目
            description: 描述
        """
        with self._lock: # 🚨 关键修复：加锁，确保并发安全
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                today = datetime.date.today().isoformat()
            
                cursor.execute('''
                    INSERT INTO preferences (user_id, date, item, description)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, today, item, description))
                # conn.commit() # 移除此行，由上下文管理器处理
    
    def get_preferences(self, user_id: int) -> List[Dict]:
        """
        获取用户喜好列表
        Args:
            user_id: 用户ID
        Returns:
            List[Dict]: 喜好列表
        """
        with self.get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM preferences WHERE user_id = ? ORDER BY date DESC",
                (user_id,)
            )
            rows = cursor.fetchall()
            return [dict(row) for row in rows]

# 全局数据库管理器实例
db_manager = DatabaseManager()

# 导出主要函数供其他模块使用
def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    return db_manager
