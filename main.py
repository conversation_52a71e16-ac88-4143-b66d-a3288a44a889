#!/usr/bin/env python3
"""
大学生心理小助手"喵呜" - 魔搭平台入口文件
===========================================

这是魔搭平台的标准入口文件，确保应用能够正确启动
"""

import os
import sys

# 设置Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置魔搭平台环境变量标识
os.environ["MODELSCOPE_ENVIRONMENT"] = "true"

# 导入并启动主应用
if __name__ == "__main__":
    try:
        # 导入主应用
        from app import demo
        
        print("🌸 魔搭平台启动 - 大学生心理小助手喵呜 🌸")
        print("🚀 正在初始化...")
        print("📦 新功能：八维认知功能深度探索")
        
        # 魔搭平台标准配置
        demo.queue().launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True,
            show_api=False
        )
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
