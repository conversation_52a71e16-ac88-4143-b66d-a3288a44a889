Edition: 0.0.4
Type: Application
Name: 大学生心理小助手喵呜-Camel-AI优化版
Provider:
  - 阿里云
Version: 2.1.0
Description: 基于荣格八维理论和Camel-AI技术的大学生心理健康评估与情绪支持系统。集成智能Agent、数据库持久化、状态管理、MCP协议等先进技术，提供专业心理测试、情绪疏导、社区互助和个性化陪伴服务
HomePage: https://github.com/psychology-assistant/miao-wu
Tags:
  - 心理健康
  - 大学生心理咨询
  - 荣格八维测试
  - 情绪支持
  - AI心理助手
  - 心理评估
  - 社区互助
  - MCP协议
  - camel-ai
  - gradio
  - sqlite
Category: 人工智能
License: Apache License 2.0
Runtime: Python 3.9
Service:
  函数计算:
    Runtime: Python 3.9
    Authorities:
      - 创建函数
      - 更新函数
      - 删除函数
    Memory: 1024MB
    Timeout: 600
    Port: 7860
Environment:
  - Name: PYTHONPATH
    Value: /code
  - Name: GRADIO_SERVER_NAME
    Value: "0.0.0.0"
  - Name: GRADIO_SERVER_PORT
    Value: "7860"
  - Name: ENV_APP_VERSION
    Value: "2.1.0"
  - Name: ENV_FILE_INNER_URI
    Value: "app:7860"
  - Name: GRADIO_TEMP_DIR
    Value: "/tmp/gradio"
Parameters:
  - Name: DASHSCOPE_API_KEY
    Type: String
    Description: 通义千问API密钥，用于AI对话和心理分析
    Required: true
    Default: ""
Dependencies:
  - gradio>=4.29.0
  - requests>=2.31.0
  - python-dotenv>=1.0.0
  - pandas>=2.0.0
  - numpy>=1.24.0
  - pillow>=10.0.0
Commands:
  Install: pip install -r requirements.txt
  Run: python app.py
Entrypoint: python app.py
