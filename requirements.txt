# 魔搭部署依赖文件 - MCP+Agent优化版 v2.1.0
# 符合2025年魔搭平台最新规范 + MCP协议支持

# 核心Web界面 - 最新稳定版本
gradio>=4.29.0

# API和网络请求 - 安全版本
requests>=2.31.0

# 环境配置 - 最新版本
python-dotenv>=1.0.0

# 数据处理 - 必需依赖
pandas>=2.0.0

# 数值计算 - 核心依赖
numpy>=1.24.0

# 图像处理 - 界面优化
pillow>=10.0.0

# 数据库
# sqlite3 (Python内置，无需安装)

# 🌐 MCP功能说明：
# Model Context Protocol (MCP) 服务器已完全重构：
# - 新增 mcp_web_search_server.py: 符合魔搭创空间规范的MCP Server实现
# - 使用 Gradio + Python 架构，真正的心理健康内容搜索服务
# - 环境变量MCP_ENABLED=true启用，完善的降级机制和频率控制
# - 提供 web_search 和 get_psychology_recommendations 两个核心MCP功能

# 🚨 注意：
# Camel-AI依赖已移除，避免魔搭环境兼容性问题
# 核心智能功能通过通义千问API实现，更稳定可靠