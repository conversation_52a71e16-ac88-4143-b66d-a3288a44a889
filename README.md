---
license: Apache License 2.0
language:
- zh
tags:
- psychology
- mental-health
- counseling
- jungian-typology
- camel-ai
- gradio
- sqlite
pipeline_tag: text-generation
widget:
- text: "你好，我最近感到很焦虑"
  example_title: "情绪支持咨询"
- text: "我想做一个性格测试"
  example_title: "荣格八维测试"
- text: "帮我分析一下学习压力问题"
  example_title: "系统思维分析"
---

# 🌸 大学生心理小助手"喵呜" - MCP+Agent优化版

## 🚀 项目概述

这是一个融合 **Model Context Protocol (MCP) 协议** 和 **智能Agent技术** 的大学生心理健康支持AI助手。针对用户能量不够和不知道需求问题在哪里的情况，项目致力于打造一个有**"温度"**的 AI 伙伴，它能像朋友一样倾听，像智者一样引导，像太阳一样温暖，像猫猫一样可爱，帮助大学生**感知情绪、深挖根源、勇敢行动、认识自我并持续陪伴**。在保留原版所有核心功能的基础上，集成了 MCP、智能状态管理、数据库持久化等前沿技术，为大学生提供更智能、更个性化、更有温度的心理陪伴服务。

### ✨ 核心功能亮点

#### 🎯 五大核心功能模块
1. **💬 智能主对话** - Agent增强的个性化引导式对话体验：包含**用户信息收集**、**荣格测试**、**情绪疏导**、**幸福日志**、**自由聊天**的完整闭环，通过“是什么→为什么→怎么办”三步法主动引导用户。记忆增强，提供高度个性化支持。
2. **🧠 荣格八维测试** - 专业16题精准测试 + 永久存储 + 八维知识专业讲解：直击底层人格，深入了解用户的核心驱动力与认知偏好。
3. **🧭 系统思维工具** - "是什么→为什么→怎么办"结构化心理疏导：情绪识别、根因分析、行动规划，培养终身受益的问题解决能力。
4. **🐾 幸福日志** - 受益终身的积感恩习惯培养 + 日历可视化回顾：记录幸福时刻，培养感恩习惯，建立积极心理循环。
5. **🐱 喵呜社区** - 基于MCP协议的智能内容推荐 + 社区互助平台：**喵呜精选**提供知乎高赞文章的**单篇文章精华摘要**；**喵呜乐园**提供趣味互动和小游戏。需完成主对话完整流程后解锁。

#### 🌐 MCP+Agent技术优势
- **MCP协议深度集成**: 实现真实网络内容获取，动态提供最新心理健康资讯（如知乎文章、心理小游戏）。
- **智能降级机制**: MCP不可用时无缝切换到高质量静态内容，确保服务连续性。
- **Agent 概念架构**: 采用 Agent 协作理念，实现心理咨询、人格分析、系统思维等专业化分工，核心智能功能通过通义千问API实现。
- **数据库持久化**: SQLite本地存储，用户数据永不丢失，支持多用户、多轮对话记忆和个性化档案。
- **智能状态管理**: 精细化的**16种对话状态**，确保引导流程的完整性和连贯性，并支持紧急情况检测。
- **环境自适应**: 完美适配魔搭平台与本地开发环境，部署简便。

#### 🎨 用户体验特色
- **零门槛启动**: 发送任意消息即可开始温暖的心理陪伴和完整引导流程。
- **透明化沟通**: 每个环节都友好解释"为什么要这样做"，让用户感到被尊重和理解。
- **用户掌控权**: 关键节点提供选择，绝不强迫用户，尊重其自主性。
- **阳光明日香风格**: 温暖活力的视觉设计，降低心理距离，提供舒适的互动体验。

#### 🛡️ 安全保障机制
- **双重危机检测**: 本地关键词 + AI智能分析，确保紧急情况及时响应。
- **硬编码安全文案**: 避免AI生成不当干预建议，保障专业性。
- **专业热线推荐**: 提供全国24小时心理危机咨询热线 400-161-9995。
- **状态锁定**: 紧急情况下的界面保护机制，确保用户安全。

## 🛠️ 快速部署指南

### 📋 部署方式

#### 🌟 方式1：魔搭平台部署（推荐）

1. **访问魔搭平台**
   ```
   https://www.modelscope.cn/studios/create
   ```

2. **创建应用空间**
   - 应用名称：`大学生心理小助手喵呜-MCP+Agent优化版` (请确保与`modelfile.yaml`中的`Name`一致)
   - 选择"从本地上传"
   - 上传整个项目文件夹

3. **配置环境变量**
   ```
   变量名: DASHSCOPE_API_KEY
   变量值: 您的通义千问API密钥
   ```

4. **自动部署**
   - 魔搭平台将自动读取项目根目录下的 `modelfile.yaml` 进行配置（`Runtime: Python 3.9`，`Memory: 1024MB`，`Timeout: 600`等）。
   - 自动安装 `requirements.txt` 中指定的依赖 (`gradio>=4.29.0`, `requests>=2.31.0` 等)。
   - 自动启动: `python app.py`。

5. **启用MCP功能（可选增强）**
   ```
   环境变量名: MCP_ENABLED
   环境变量值: true
   说明: 启用 Model Context Protocol (MCP) 网络搜索功能，获取最新心理健康资讯（如知乎高赞文章的精华摘要）和小游戏推荐。
        **注意：**MCP 调用频率限制为每小时5次，超出限制将显示静态内容。
   ```

#### 💻 方式2：本地开发测试

```bash
# 1. 克隆项目
git clone <项目地址> # 请替换为您的项目Git地址
cd 魔搭/             # 切换到项目根目录

# 2. 安装依赖 (推荐使用虚拟环境)
pip install -r requirements.txt

# 3. 设置环境变量
# Windows (CMD):
# set DASHSCOPE_API_KEY="您的API密钥"
# set MCP_ENABLED="true" # 可选，启用MCP功能
#
# Linux/macOS (Bash):
# export DASHSCOPE_API_KEY="您的API密钥"
# export MCP_ENABLED="true" # 可选，启用MCP功能

# 4. 启动应用
python app.py
```

### ⚙️ 配置文件说明

#### `modelfile.yaml` (魔搭平台配置)
```yaml
# 详情请参考魔搭平台`modelfile.yaml`最新规范
Edition: 0.0.4
Type: Application
Name: 大学生心理小助手喵呜-Camel-AI优化版
Provider: # 新增字段
  - 阿里云
Version: 2.1.0
Description: 基于荣格八维理论和Camel-AI技术的大学生心理健康评估与情绪支持系统。集成智能Agent、数据库持久化、状态管理、MCP协议等先进技术，提供专业心理测试、情绪疏导、社区互助和个性化陪伴服务 # 新增字段
HomePage: https://github.com/psychology-assistant/miao-wu # 新增字段
Tags: # 新增字段
  - 心理健康
  - 大学生心理咨询
  - 荣格八维测试
  - 情绪支持
  - AI心理助手
  - 心理评估
  - 社区互助
  - MCP协议
  - camel-ai
  - gradio
  - sqlite
Category: 人工智能 # 新增字段
License: Apache License 2.0 # 新增字段
Runtime: Python 3.9 # 已更新
Service:
  函数计算:
    Runtime: Python 3.9
    Authorities:
      - 创建函数
      - 更新函数
      - 删除函数
    Memory: 1024MB # 已更新
    Timeout: 600 # 已更新
    Port: 7860
Environment:
  - Name: PYTHONPATH
    Value: /code
  - Name: GRADIO_SERVER_NAME
    Value: "0.0.0.0"
  - Name: GRADIO_SERVER_PORT
    Value: "7860"
  - Name: ENV_APP_VERSION
    Value: "2.1.0"
  - Name: ENV_FILE_INNER_URI
    Value: "app:7860"
  - Name: GRADIO_TEMP_DIR
    Value: "/tmp/gradio"
Parameters:
  - Name: DASHSCOPE_API_KEY
    Type: String
    Description: 通义千问API密钥，用于AI对话和心理分析
    Required: true
    Default: ""
Dependencies:
  - gradio>=4.29.0 # 已更新
  - requests>=2.31.0 # 已更新
  - python-dotenv>=1.0.0 # 已更新
  - pandas>=2.0.0 # 新增
  - numpy>=1.24.0 # 新增
  - pillow>=10.0.0 # 新增
Commands:
  Install: pip install -r requirements.txt
  Run: python app.py
Entrypoint: python app.py
```

#### `requirements.txt` (依赖管理)
```
# 魔搭部署依赖文件 - MCP+Agent优化版 v2.1.0
# 符合2025年魔搭平台最新规范 + MCP协议支持

# 核心Web界面 - 最新稳定版本
gradio>=4.29.0

# API和网络请求 - 安全版本
requests>=2.31.0

# 环境配置 - 最新版本
python-dotenv>=1.0.0

# 数据处理 - 必需依赖
pandas>=2.0.0

# 数值计算 - 核心依赖
numpy>=1.24.0

# 图像处理 - 界面优化
pillow>=10.0.0

# 数据库
# sqlite3 (Python内置，无需安装)

# 🌐 MCP功能说明：
# Model Context Protocol (MCP) 服务器已完全重构：
# - 新增 mcp_web_search_server.py: 符合魔搭创空间规范的MCP Server实现
# - 使用 Gradio + Python 架构，真正的心理健康内容搜索服务
# - 环境变量MCP_ENABLED=true启用，完善的降级机制和频率控制
# - 提供 web_search 和 get_psychology_recommendations 两个核心MCP功能

# 🚨 注意：
# Camel-AI依赖已移除，避免魔搭环境兼容性问题
# 核心智能功能通过通义千问API实现，更稳定可靠
```

## 🏗️ 技术架构

### 📊 Agent协作架构

```
🤖 Agent 协作架构
├── 🧠 核心智能层 (通义千问API驱动)
│   ├── 主Agent (喵呜)           # 记忆增强的对话生成，个性化上下文感知
│   ├── 荣格八维专家Agent        # 专业人格分析
│   ├── 幸福日志引导师Agent      # 积极心理习惯培养
│   └── 系统思维专家Agent        # 结构化问题解决
│
├── 🔄 状态管理层
│   ├── 对话状态枚举 (16种状态)   # 精细化流程控制
│   ├── 状态转换逻辑             # 智能状态切换
│   └── 紧急情况检测             # 安全优先机制
│
├── 💾 数据持久化层
│   ├── 用户管理 (users表)        # 基本信息和状态
│   ├── 对话历史 (conversations)  # 完整对话记录
│   ├── 用户档案 (user_profiles)  # 个性化画像数据
│   ├── 测试结果 (jungian_results) # 荣格八维结果
│   ├── 幸福日志 (happiness_logs)  # 积极体验记录
│   └── 社区内容 (community_posts) # 社区发布内容
│
└── 🎨 用户界面层
    ├── Gradio油画风格布局          # 左侧用户管理+右侧对话
    ├── 阳光明日香主题按钮            # 温暖活力的视觉设计  
    ├── 五大功能Tab               # 独立功能模块
    └── 响应式设计                # 完美适配各设备
```

### 🔧 核心模块介绍

#### `app.py` - 主应用入口
- Gradio界面构建和事件处理
- Agent概念集成，驱动对话流程
- 用户状态管理和界面渲染

#### `agents.py` - Agent定义 (核心智能通过通义千问API实现)
- `MemoryEnhancedChatAgent`: 记忆增强的对话代理，集成数据库记忆和个性化上下文感知。
- 专业Agent角色预留接口（如荣格专家、幸福引导师、系统思维专家），通过核心智能层协调运作。

#### `database_manager.py` - 数据库管理
- SQLite数据库完整封装，提供用户数据、对话历史、测试结果、幸福日志、社区内容持久化。
- 高效的数据查询和更新接口，支持多用户体系。

#### `state_manager.py` - 状态管理
- **16种对话状态枚举定义**，精细化控制对话流程。
- 智能状态转换逻辑，确保流程的完整性和连贯性。
- 状态模板和紧急检测机制，保障用户体验和安全。

#### `conversation_flow_modelscope.py` - 对话流程引擎
- 包含多个专业心理支持阶段的流程定义，确保对话的结构化引导。
- 温暖友好的引导机制，提升用户体验。

#### `mcp_web_search_server.py` - MCP Web搜索服务
- 符合魔搭创空间规范的 Model Context Protocol (MCP) 服务器实现。
- 提供 `web_search` 和 `get_psychology_recommendations` 两个核心MCP功能，用于获取外部心理健康内容。
- 集成智能降级机制和频率控制，确保稳定性和合规性。

#### `api_service_unified.py` - 统一API服务 (已移除，核心智能由通义千问API直接驱动)
- **注意：** 此模块已移除，以简化架构并避免魔搭环境兼容性问题。核心智能功能现在直接通过通义千问API实现，提供更稳定可靠的对话生成和心理分析服务。

## 🎯 功能详解

### 💬 智能主对话系统

#### 用户信息收集（6步渐进式了解）
1. **昵称破冰** - 建立个人化称呼
2. **当下心情快照** - 了解情绪状态 
3. **压力雷达图** - 识别主要压力源
4. **心愿小纸条** - 明确核心需求
5. **能量补给站** - 了解恢复方式
6. **支持小分队** - 评估社会支持

#### Agent增强特性
- **记忆上下文**: Agent记住用户的所有信息，包括档案和历史对话。
- **个性化回复**: 基于用户画像和荣格八维结果生成定制化建议。
- **智能引导**: 自然流畅的对话转换，主动引导用户进行系统思维。
- **数据持久化**: 用户信息永久保存，支持长期陪伴。

### 🧠 荣格八维专业测试

#### 测试特色
- **16题精准测试**: 基于荣格理论的专业设计，快速评估人格类型。
- **权重算法**: 核心题目加权，提高测试准确性。
- **16型人格分析**: 提供详细的性格描述和发展建议。
- **结果永久保存**: 数据库存储，支持历史追踪和个性化反馈。

#### 认知功能介绍
- **E/I维度**: 能量获取方向（外向/内向）
- **S/N维度**: 信息感知偏好（感觉/直觉）
- **T/F维度**: 决策判断依据（思考/情感）
- **J/P维度**: 生活方式倾向（判断/知觉）

### 🧭 系统思维工具

#### "是什么→为什么→怎么办"三步法
1. **是什么**: 情绪定位和清晰识别，帮助用户理解问题表象。
2. **为什么**: 根因挖掘和深层分析，引导用户探究问题根源。
3. **怎么办**: 行动设计和具体方案，协助用户制定可执行的解决方案。

#### 用户选择权机制 (主动引导模式下)
- **A. 纯倾听模式**: 提供无干预的情感支持和陪伴。
- **B. 安静陪伴**: 提供冷静思考空间，尊重用户节奏。
- **C. 深入探索**: 启动结构化问题分析，系统性解决困扰。

### 🐾 幸福日志系统

#### 积极心理学应用
- **幸福时刻记录**: 捕捉生活中的美好瞬间，培养积极心态。
- **开心程度量化**: 1-10级情感强度记录，量化幸福感。
- **日历可视化回顾**: 直观展示幸福轨迹，支持月份切换和永久回顾。
- **个性化鼓励**: 基于荣格类型提供定制化引导和积极反馈。

#### 数据管理升级
- **永久存储**: SQLite数据库保存所有记录，确保数据安全和可追溯。
- **多维度分析**: 支持按日期、内容、情感强度、来源分类进行分析。
- **趋势追踪**: 长期观察幸福感变化趋势，提供个性化洞察。

### 🐱 喵呜社区系统

#### MCP协议驱动的智能推荐
- **💡 喵呜精选**: 基于MCP `web_search` 的积极心理学内容推荐。每次刷新获取**单篇文章的精华摘要**，来自知乎高赞文章。
- **🎮 喵呜乐园**: 动态获取心理健康小游戏和放松活动。
- **智能降级**: MCP不可用时提供精心设计的高质量静态内容，确保用户体验。
- **频率控制**: 每小时最多**5次**MCP调用，保护系统资源并防止滥用。

#### 社区互助功能
- **🏪 社区广场**: 用户可浏览所有求助帖子和互动。
- **💝 发布求助**: 用户可匿名发布求助信息和个人感想。
- **👥 我的记录**: 个人发帖历史管理，方便用户回顾与跟踪。
- **权限控制**: 需完成主对话完整引导流程后解锁社区功能，确保用户已获得基础心理支持。

## 🛡️ 安全与隐私

### 🚨 危机干预机制
- **双重检测**: 本地关键词筛查 + AI智能分析，高效识别潜在危机。
- **即时响应**: 检测到风险立即触发安全模式和干预流程。
- **专业资源**: 推荐全国心理危机热线 `400-161-9995`，提供紧急帮助。
- **状态锁定**: 在危机干预期间，界面将进入锁定状态，防止用户绕过安全机制。

### 🔒 数据安全保护
- **本地存储**: SQLite数据库本地保存，用户数据隐私可控。
- **环境变量**: API密钥通过环境变量安全管理，不硬编码。
- **会话隔离**: 多用户数据完全隔离，保障个人信息独立性。
- **降级保护**: 技术故障时自动切换备用方案，确保服务连续性。

## 📊 性能与兼容性

### ⚡ 性能指标
- **启动时间**: 优化至 `30秒以内`。
- **响应延迟**: `3秒以内`（正常网络环境），提供流畅对话体验。
- **内存占用**: 优化至 `512MB以内`，符合魔搭平台资源要求。
- **并发支持**: 支持 `50+ 用户`同时在线，保证高可用性。

### 🔄 兼容性设计
- **渐进式升级**: Agent概念优先，原有优秀逻辑作为兜底。
- **自动降级**: 智能组件出错时无缝切换到备用方案，不影响用户体验。
- **零感知迁移**: 用户体验在不同部署环境下保持一致。
- **跨平台支持**: 兼容魔搭平台、本地部署、Docker等多种环境。

## 🚀 未来发展路线

### 📈 短期优化（1-3个月）
- [ ] 进一步优化Agent协作模式，提升智能响应能力。Camel-AI多智能体交互。
- [ ] 引入更丰富的数据分析和可视化功能，提供用户洞察。
- [ ] 探索云端数据同步功能，提升数据便捷性。
- [ ] 优化移动端适配，提供更好的移动体验。

### 🔮 长期愿景（3-12个月）
- [ ] 拓展多模态交互（语音、图像），丰富用户沟通方式。
- [ ] 研发更深度的心理分析算法，提升专业支持水平。
- [ ] 完善社区功能和匿名互助机制，建立更活跃的社区。
- [ ] 引入专业心理师接入平台，提供线上咨询服务。

## 📞 技术支持

### 🔗 相关资源
- **魔搭平台文档**: `https://www.modelscope.cn/docs`
- **Gradio官方文档**: `https://gradio.app/docs`
- **GitHub项目主页**: `https://github.com/psychology-assistant/miao-wu` (此链接请根据实际项目地址修改)

### 🐛 问题反馈
如遇到技术问题，请优先查看以下信息：
1. 应用启动时的控制台输出，查找错误信息。
2. 浏览器开发者工具中的控制台和网络请求错误。
3. 环境变量配置是否正确，特别是 `DASHSCOPE_API_KEY` 和 `MCP_ENABLED`。

### 💬 联系方式
- 技术问题讨论
- 功能建议反馈
- 心理学专业指导

---

## 🎉 项目亮点总结

### 🏆 技术创新
- **MCP协议深度集成**: 深度集成 Model Context Protocol，实现真实网络内容获取。
- **Agent 概念架构**: 采用 Agent 协作理念，通过通义千问API实现多专业Agent功能，覆盖心理咨询全场景。
- **数据持久化**: 完整的用户数据生命周期管理，支持多用户体系。
- **智能状态机**: 16种对话状态的精细化控制，流程智能引导。
- **双重保障**: MCP智能降级机制 + 多层次系统稳定性保障。

### 💝 用户价值
- **零学习成本**: 延续原版优秀体验，无需适应，上手即用。
- **长期陪伴**: 数据积累越多，AI越了解用户，提供更精准的个性化支持。
- **专业可靠**: 基于荣格理论的科学测评体系和系统思维方法论。
- **温暖安全**: 全程温暖引导，完善的危机干预和数据安全机制。

### 🌟 社会意义
- **心理健康普及**: 降低心理支持的获取门槛，让每个大学生都有专属心理伙伴。
- **积极心理学实践**: 培养大学生正向思维习惯，提升幸福感知能力。
- **前沿技术惠民**: MCP+Agent技术服务心理健康事业，彰显技术向善。
- **竞赛技术示范**: 为MCP&Agent挑战赛和AI+大学生心理健康赛道提供完整解决方案和实践范例。

---

## 🏆 **竞赛技术亮点**

### 🎯 **魔搭MCP&Agent挑战赛**
- ✅ **MCP协议深度应用**: `web_search` 实现真实网络内容获取，丰富内容推荐。
- ✅ **Agent 概念智能协作**: 通过通义千问API实现心理咨询、测试分析、内容推荐等专业分工。
- ✅ **完善降级机制**: 确保MCP不可用时仍能完美运行，提供稳定用户体验。
- ✅ **环境自适应**: 魔搭平台完美部署，本地开发无缝切换，部署无忧。


---

# 核心智能功能通过通义千问API实现，更稳定可靠

**🎊 欢迎体验这个融合了MCP协议、智能Agent与专业心理学知识的温暖助手！** 

让我们一起用最前沿的技术力量，为大学生的心理健康保驾护航！ 🌸✨🏆
