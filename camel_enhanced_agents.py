#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Camel-AI增强的多智能体心理助手 - 魔搭平台专用版
======================================================

设计原则：
1. 保持所有现有功能100%不变
2. 使用Camel-AI优化代码结构，减少重复代码
3. 增强多智能体协作能力
4. 完全适配魔搭平台和通义千问API

架构优势：
- 专门的心理咨询师Agent
- 专门的问题分析Agent  
- 专门的系统思维引导Agent
- 专门的危机干预Agent
- 统一的对话管理和状态同步
"""

import json
from typing import Dict, List, Optional, Any
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.types import ModelType, RoleType

# 导入现有模块，保持兼容性
from api_service_unified import get_modelscope_response, emergency_check, local_emergency_check
from config_modelscope import DASHSCOPE_API_KEY
DEFAULT_MODEL = "qwen-max"  # 直接定义，避免循环导入

class CamelModelAdapter:
    """Camel-AI模型适配器 - 适配魔搭通义千问API"""
    
    def __init__(self, api_key: str = DASHSCOPE_API_KEY, model_name: str = DEFAULT_MODEL):
        self.api_key = api_key
        self.model_name = model_name
    
    def create_model_config(self, temperature: float = 0.7, max_tokens: int = 1000):
        """创建适配魔搭API的模型配置"""
        return {
            "api_key": self.api_key,
            "model": self.model_name,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1"
        }

class PsychologicalCounselorAgent:
    """心理咨询师Agent - 主动引导式强化版"""
    
    def __init__(self):
        # 适配魔搭API的模型配置
        self.adapter = CamelModelAdapter()
        
        # 🚨 强化：主动引导式系统提示词
        self.system_prompt = """你是一位温暖专业的心理咨询师喵呜🌸，专门进行主动引导式心理支持。

**核心引导思维模式（必须严格遵循）：**
1. 🎧 **倾听阶段** - 先充分倾听和理解用户的表达
2. 🌟 **温暖引导** - 主动引导用户深入思考，而不是被动回答
3. 🔍 **"是什么"** - 主动帮助用户澄清问题的本质
4. 🧠 **"为什么"** - 主动引导探索问题的根本原因  
5. 💡 **"怎么办"** - 主动协助制定具体的行动方案

**必须的主动引导原则：**
- 不要等用户问问题，要主动引导思考方向
- 每个回复都要包含引导性问题或思考点
- 固定遵循"倾听→引导→是什么→为什么→怎么办"的顺序
- 确保每个阶段都有主动的引导和推进
- 语言温暖但目标明确，绝不是简单的一问一答

**交流风格：**
- 温暖empathetic，但带有明确的引导方向
- 主动提出深入的思考问题，而不是被动等待
- 每次回复都要推进思维过程的下一步

记住：你是主动的心理引导者，不是被动的回答机器！"""

        self.conversation_history = []
        
        # 🚨 新增：引导思维状态追踪
        self.guidance_stage = "listening"  # listening → guiding → what → why → how
    
    def process_message(self, user_input: str, context: Dict = None) -> str:
        """处理用户消息，返回主动引导式心理咨询回复"""
        
        # 🚨 新增：判断当前应该处于哪个引导阶段
        self._update_guidance_stage(user_input)
        
        # 构建主动引导式消息历史
        messages = [{"role": "system", "content": self._get_stage_specific_prompt()}]
        
        # 添加用户信息上下文
        if context:
            context_info = self._format_context(context)
            messages.append({"role": "assistant", "content": f"用户信息：{context_info}"})
        
        # 🚨 修改：添加引导阶段信息
        stage_info = f"当前引导阶段：{self.guidance_stage}。请严格按照主动引导原则进行回复。"
        messages.append({"role": "assistant", "content": stage_info})
        
        # 添加对话历史（最近3轮）
        for msg in self.conversation_history[-6:]:  # 最近3轮对话
            messages.append(msg)
        
        # 添加当前用户输入
        messages.append({"role": "user", "content": user_input})
        
        # 调用魔搭API
        response = get_modelscope_response(messages)
        
        # 🚨 新增：确保回复符合主动引导要求
        guided_response = self._enhance_with_guidance(response, user_input)
        
        # 更新对话历史
        self.conversation_history.append({"role": "user", "content": user_input})
        self.conversation_history.append({"role": "assistant", "content": guided_response})
        
        return guided_response
    
    def _update_guidance_stage(self, user_input: str):
        """根据用户输入更新引导阶段"""
        # 简化的阶段推进逻辑
        if self.guidance_stage == "listening" and len(self.conversation_history) >= 2:
            self.guidance_stage = "what"
        elif self.guidance_stage == "what" and ("是" in user_input or "确实" in user_input or len(user_input) > 20):
            self.guidance_stage = "why"
        elif self.guidance_stage == "why" and ("因为" in user_input or "由于" in user_input or len(user_input) > 20):
            self.guidance_stage = "how"
    
    def _get_stage_specific_prompt(self) -> str:
        """根据当前阶段返回特定的系统提示"""
        stage_prompts = {
            "listening": f"{self.system_prompt}\n\n🎧 当前处于倾听阶段：充分理解用户表达后，主动引导向'是什么'阶段推进。",
            "what": f"{self.system_prompt}\n\n🔍 当前处于'是什么'阶段：主动帮助用户澄清问题本质，然后引导向'为什么'推进。",
            "why": f"{self.system_prompt}\n\n🧠 当前处于'为什么'阶段：主动引导探索根本原因，然后引导向'怎么办'推进。",
            "how": f"{self.system_prompt}\n\n💡 当前处于'怎么办'阶段：主动协助制定具体行动方案，给出明确的下一步。"
        }
        return stage_prompts.get(self.guidance_stage, self.system_prompt)
    
    def _enhance_with_guidance(self, response: str, user_input: str) -> str:
        """增强回复的主动引导性"""
        guidance_endings = {
            "listening": [
                "\n\n现在让我们一起来看看，这个情况的核心本质是什么？🔍",
                "\n\n我想帮你更清晰地理解这个问题，它的核心到底是什么呢？",
                "\n\n让我们先理清楚，你遇到的主要问题究竟是什么？"
            ],
            "what": [
                "\n\n明白了这个问题的本质，现在让我们深入思考一下：为什么会出现这种情况？🧠",
                "\n\n问题澄清后，我们来探索背后的原因：是什么导致了这样的情况？",
                "\n\n现在我们知道了问题是什么，那为什么会发生这样的事呢？"
            ],
            "why": [
                "\n\n分析了原因后，让我们制定行动方案：具体怎么办才能改善这个情况？💡",
                "\n\n理解了原因，现在最重要的是：我们可以采取什么具体行动？",
                "\n\n既然找到了根源，那么我们应该怎么做来解决它呢？"
            ],
            "how": [
                "\n\n很好！有了行动方案，你打算从哪一步开始实施呢？✨",
                "\n\n计划很清晰，现在你觉得第一步应该怎么迈出？",
                "\n\n方案制定好了，让我们确定一个具体的开始时间吧！"
            ]
        }
        
        if self.guidance_stage in guidance_endings:
            endings = guidance_endings[self.guidance_stage]
            import random
            guidance_ending = random.choice(endings)
            if "？" not in response and "怎么" not in response and "什么" not in response:
                response += guidance_ending
        
        return response
    
    def _format_context(self, context: Dict) -> str:
        """格式化用户上下文信息"""
        info_parts = []
        if context.get("nickname"):
            info_parts.append(f"昵称：{context['nickname']}")
        if context.get("current_emotion"):
            info_parts.append(f"当前情绪：{context['current_emotion']}")
        if context.get("jungian_type"):
            info_parts.append(f"性格类型：{context['jungian_type']}")
        
        return "，".join(info_parts) if info_parts else "新用户"

class SystemThinkingAgent:
    """系统思维引导Agent - 专门负责"是什么→为什么→怎么办"分析"""
    
    def __init__(self):
        self.adapter = CamelModelAdapter()
        
        self.system_prompt = """你是一位系统思维引导专家，专门负责引导用户进行"是什么→为什么→怎么办"的结构化思考。

**你的专长：**
1. **是什么**：帮助用户清晰定义和描述问题的本质
2. **为什么**：深入挖掘问题的根本原因（事件→痛点→影响三层分析）
3. **怎么办**：设计具体可执行的行动方案（深呼吸→能力范围→Mini任务）

**引导原则：**
- 循序渐进，不急于求成
- 问题导向，启发用户自己思考
- 实用为主，注重可操作性
- 保持温暖，避免生硬的说教

**分析框架：**
- 事件层：具体发生了什么？
- 痛点层：最让人难受的是什么？  
- 影响层：带来了哪些具体困扰？
- 行动层：从小处着手，设计可执行的Mini任务

你的回复要结构清晰，逻辑严密，同时保持温暖的沟通风格。"""
    
    def analyze_what(self, problem_description: str) -> str:
        """分析"是什么" - 问题本质澄清"""
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": f"请帮我分析这个问题的本质是什么：{problem_description}"}
        ]
        return get_modelscope_response(messages)
    
    def analyze_why(self, event: str, pain: str, impact: str) -> str:
        """分析"为什么" - 三层深入分析"""
        analysis_prompt = f"""
请基于以下三个层面进行深入分析：

**事件层：** {event}
**痛点层：** {pain}  
**影响层：** {impact}

请深入分析这三个层面之间的内在关联，找出问题的根本原因。
"""
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": analysis_prompt}
        ]
        return get_modelscope_response(messages)
    
    def analyze_how(self, capability: str, mini_task: str, context: str = "") -> str:
        """分析"怎么办" - 行动方案设计"""
        action_prompt = f"""
基于前面的分析{context}，现在来设计具体的行动方案：

**能力范围：** {capability}
**Mini任务：** {mini_task}

请设计一个循序渐进的行动计划，确保每一步都是可执行的。
"""
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": action_prompt}
        ]
        return get_modelscope_response(messages)

class CrisisInterventionAgent:
    """危机干预Agent - 专门处理紧急心理危机"""
    
    def __init__(self):
        self.crisis_keywords = [
            "自杀", "轻生", "不想活", "结束生命", "死了算了", 
            "活着没意义", "想死", "自残", "伤害自己"
        ]
    
    def check_crisis(self, user_input: str) -> bool:
        """检测是否存在危机情况"""
        # 首先尝试API检测，失败则使用本地检测
        api_result = emergency_check(user_input)
        if api_result:
            return True
        return local_emergency_check(user_input)
    
    def get_crisis_response(self) -> str:
        """获取标准化危机干预回复"""
        return """亲爱的同学，我能感受到你正在承受巨大的痛苦，这让我非常担心。请记住，你不是一个人，寻求帮助是勇敢和力量的象征。

🆘 **请立即拨打全国24小时免费心理危机咨询热线：400-161-9995**

🏥 或联系你所在学校的心理健康中心

👨‍👩‍👧 或向身边的人、专业人士求助

你的生命是珍贵的，请务必寻求专业帮助！"""

class EnhancedConversationManager:
    """增强版对话管理器 - 整合多个Camel-AI Agent"""
    
    def __init__(self):
        # 初始化各种专门的Agent
        self.counselor_agent = PsychologicalCounselorAgent()
        self.thinking_agent = SystemThinkingAgent()
        self.crisis_agent = CrisisInterventionAgent()
        
        # 保持与原版兼容的会话状态
        self.session_data = {
            "stage": "welcome",
            "nickname": "",
            "current_emotion": "",
            "jungian_type": "",
            "conversation_history": [],
            "last_response": ""
        }
    
    def process_input(self, user_input: str, context_type: str = "general") -> str:
        """处理用户输入 - 智能路由到合适的Agent"""
        
        # 1. 首先进行危机检测
        if self.crisis_agent.check_crisis(user_input):
            return self.crisis_agent.get_crisis_response()
        
        # 2. 根据上下文类型路由到合适的Agent
        if context_type == "system_thinking":
            # 系统思维模式 - 使用专门的思维引导Agent
            response = self.thinking_agent.analyze_what(user_input)
        else:
            # 一般心理咨询模式 - 使用心理咨询师Agent
            response = self.counselor_agent.process_message(
                user_input, 
                context=self.session_data
            )
        
        # 3. 更新会话状态
        self.session_data["last_response"] = response
        self.session_data["conversation_history"].append({
            "user": user_input,
            "assistant": response,
            "timestamp": "2025-01-27",  # 简化时间戳
            "agent_type": context_type
        })
        
        return response
    
    def get_thinking_analysis(self, analysis_type: str, **kwargs) -> str:
        """获取系统思维分析"""
        if analysis_type == "what":
            return self.thinking_agent.analyze_what(kwargs.get("problem", ""))
        elif analysis_type == "why":
            return self.thinking_agent.analyze_why(
                kwargs.get("event", ""),
                kwargs.get("pain", ""),
                kwargs.get("impact", "")
            )
        elif analysis_type == "how":
            return self.thinking_agent.analyze_how(
                kwargs.get("capability", ""),
                kwargs.get("mini_task", ""),
                kwargs.get("context", "")
            )
        else:
            return "分析类型不支持"
    
    def update_user_context(self, key: str, value: Any):
        """更新用户上下文信息"""
        self.session_data[key] = value
    
    def get_session_summary(self) -> Dict:
        """获取会话摘要 - 保持与原版API兼容"""
        return {
            "total_messages": len(self.session_data["conversation_history"]),
            "current_stage": self.session_data["stage"],
            "user_info": {
                "nickname": self.session_data.get("nickname", ""),
                "emotion": self.session_data.get("current_emotion", ""),
                "jungian_type": self.session_data.get("jungian_type", "")
            }
        }

# ========================================================================
# 🔧 兼容性接口 - 保持与原版代码100%兼容
# ========================================================================

# 全局实例，保持单例模式
_enhanced_manager = None

def get_enhanced_manager() -> EnhancedConversationManager:
    """获取增强版对话管理器实例（单例模式）"""
    global _enhanced_manager
    if _enhanced_manager is None:
        _enhanced_manager = EnhancedConversationManager()
    return _enhanced_manager

def camel_ai_response(user_input: str, context_type: str = "general") -> str:
    """
    Camel-AI增强版回复接口
    
    Args:
        user_input: 用户输入
        context_type: 上下文类型 ("general", "system_thinking")
    
    Returns:
        str: AI回复
    """
    manager = get_enhanced_manager()
    return manager.process_input(user_input, context_type)

def camel_thinking_analysis(analysis_type: str, **kwargs) -> str:
    """
    Camel-AI系统思维分析接口
    
    Args:
        analysis_type: 分析类型 ("what", "why", "how")
        **kwargs: 分析参数
    
    Returns:
        str: 分析结果
    """
    manager = get_enhanced_manager()
    return manager.get_thinking_analysis(analysis_type, **kwargs)

# ========================================================================
# 🧪 测试函数
# ========================================================================

def test_camel_agents():
    """测试Camel-AI多智能体系统"""
    print("🐪 Camel-AI多智能体心理助手测试")
    print("=" * 50)
    
    manager = get_enhanced_manager()
    
    # 测试心理咨询对话
    test_inputs = [
        "我最近总是很焦虑，不知道为什么",
        "我觉得学习压力很大，总是拖延"
    ]
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\n【测试 {i}】用户: {user_input}")
        response = manager.process_input(user_input, "general")
        print(f"心理咨询师: {response[:100]}...")
        
        # 测试系统思维分析
        thinking_response = manager.get_thinking_analysis("what", problem=user_input)
        print(f"系统思维: {thinking_response[:100]}...")
    
    # 显示会话摘要
    summary = manager.get_session_summary()
    print(f"\n会话摘要: {summary}")

if __name__ == "__main__":
    print("🚧 Camel-AI增强模块已加载")
    print("🔗 可通过以下接口调用:")
    print("   - camel_ai_response(user_input, context_type)")
    print("   - camel_thinking_analysis(analysis_type, **kwargs)")
    print("🎯 设计目标: 保持100%兼容，优化代码结构，增强多智能体协作")
