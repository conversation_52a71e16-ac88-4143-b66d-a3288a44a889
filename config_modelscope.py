"""
魔搭部署专用配置文件 - 保持原版优秀配置
========================================

完全保持原版config.py的所有配置和逻辑
只在API密钥管理方面适配魔搭平台

设计原则：保持原版逻辑，只适配部署
"""

import os
from typing import List, Dict

# ========================================================================
# 🔧 API配置区域 - 使用您提供的密钥
# ========================================================================

# 使用您提供的通义千问API密钥 - 支持环境变量优先级
DASHSCOPE_API_KEY = os.getenv(
    "DASHSCOPE_API_KEY", 
    "sk-7e74c669384c4c83bd75673d93a492b3"  # 用户提供的密钥作为默认值
)

# 兼容原版的变量名
MODEL_SCOPE_API_KEY = DASHSCOPE_API_KEY

print("🔑 通义千问API已配置")
print(f"   密钥预览: {DASHSCOPE_API_KEY[:10]}...{DASHSCOPE_API_KEY[-4:]}")

# API服务配置 - 与原版相同
API_URLS = [
    "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",  # 主要URL
    "https://dashscope-intl.aliyuncs.com/compatible-mode/v1/chat/completions",  # 国际版
    "https://dashscope-cn-beijing.aliyuncs.com/compatible-mode/v1/chat/completions"  # 北京节点
]
MODEL_SCOPE_API_URL = API_URLS[0]

# 支持多个模型，自动降级 - 与原版相同
AVAILABLE_MODELS = [
    "qwen-max",           # 最新最强模型
    "qwen-plus",          # 次强模型
    "qwen-turbo",         # 快速模型
    "qwen2.5-72b-instruct",  # 备用模型
    "qwen2-7b-instruct"   # 最轻量模型
]
RECOMMENDED_MODEL = AVAILABLE_MODELS[0]

print(f"🌐 API配置:")
print(f"   主要URL: {MODEL_SCOPE_API_URL}")
print(f"   默认模型: {RECOMMENDED_MODEL}")
print(f"   备用URL数量: {len(API_URLS)}")
print(f"   备用模型数量: {len(AVAILABLE_MODELS)}")

# API请求配置 - 与原版相同
API_TIMEOUT = 30
API_MAX_TOKENS = 800
API_TEMPERATURE = 0.7
API_TOP_P = 0.8

# 对话记忆配置
RECENT_CONVERSATION_LIMIT = 5 # 限制短期对话历史为最近N轮

# ========================================================================
# 🎨 界面配置 - 与原版相同
# ========================================================================

# 应用标题和描述
APP_TITLE = "大学生心理小助手喵呜"
APP_DESCRIPTION = "嗨～ 同学你好呀！我是你的校园小树洞喵呜🌳，来跟我聊聊心里话吧～"

# 界面样式配置
CHAT_HEIGHT = 550
DEFAULT_PORT = 7860

# ========================================================================
# 🧠 心理测试配置 - 与原版相同
# ========================================================================

# 荣格八维测试配置 - 保持8题
JUNGIAN_QUESTIONS_COUNT = 8
JUNGIAN_QUESTION_WEIGHT = 2

# 情绪类型选项
EMOTION_TYPES = [
    "⬇️低落", "😡生气", "😰焦虑", "😶‍🌫️迷茫", 
    "🥱疲惫", "😓负担", "😔内疚", "😨害怕"
]

# ========================================================================
# 🔒 安全配置 - 与原版相同
# ========================================================================

# 紧急检测配置
EMERGENCY_DETECTION_ENABLED = True
LOCAL_DETECTION_PRIORITY = True  # 优先使用本地检测

# 会话管理配置
MAX_SESSION_HISTORY = 100
SESSION_TIMEOUT = 3600  # 1小时

# ========================================================================
# 📊 数据结构配置 - 与原版相同
# ========================================================================

# 默认会话状态
DEFAULT_SESSION_DATA = {
    "stage": "welcome",
    "user_nickname": "同学",
    "current_emotion": "",
    "pressure_sources": "",
    "core_need": "",
    "energy_recovery": "",
    "support_network": "",
    "jungian_answers": [],
    "jungian_index": 0,
    "jungian_scores": {"E": 0, "I": 0, "S": 0, "N": 0, "T": 0, "F": 0, "J": 0, "P": 0},
    "last_user_reason": "",
    "last_user_action": "",
    "conversation_history": []
}

# ========================================================================
# 🎯 功能开关配置 - 与原版相同
# ========================================================================

# 功能模块开关
FEATURES = {
    "emergency_detection": True,
    "jungian_test": True,
    "comfort_system": True,
    "happiness_log": True,
    "personalized_chat": True
}

# 调试模式
DEBUG_MODE = False

# ========================================================================
# 📝 日志配置 - 与原版相同
# ========================================================================

# 日志级别
LOG_LEVEL = "INFO"

# 日志格式
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# ========================================================================
# 🚀 启动配置 - 适配魔搭平台
# ========================================================================

# 服务器配置
SERVER_HOST = "0.0.0.0"  # 魔搭平台要求监听所有地址
SERVER_PORT = 7860       # 标准Gradio端口
SERVER_SHARE = False     # 魔搭平台内部分享
SERVER_DEBUG = False     # 生产环境关闭调试
SERVER_SHOW_ERROR = True # 显示错误信息便于调试

# 自动端口查找
AUTO_FIND_PORT = False   # 魔搭平台使用固定端口
PORT_RANGE_START = 7860
PORT_RANGE_END = 8000

print(f"⚙️ 魔搭平台配置完成")
print(f"   功能模块: {sum(FEATURES.values())}/{len(FEATURES)} 已启用")
print(f"   服务器配置: {SERVER_HOST}:{SERVER_PORT}")
print(f"   安全设置: 已启用危机检测")