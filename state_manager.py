"""
对话状态管理模块 - Camel-AI 优化版
===============================

功能：
1. 定义所有对话状态枚举
2. 状态转换逻辑管理
3. 对话流程控制
4. 与原有流程完美兼容

设计原则：
- 完全兼容原有对话流程
- 支持更精细的状态管理
- 为Camel-AI Agent提供状态上下文
- 保持所有原有功能不变
"""

from enum import Enum
from typing import Dict, Any, Optional
import re

# 荣格八维测试题目定义
JUNGIAN_TEST_QUESTIONS = [
    {
        "question": "聚会散场后，你更倾向于：",
        "options": {
            "A": "继续找朋友聊天或去二场，延续这种社交的兴奋感",
            "B": "回家独处充电，享受安静的个人时光",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"E": 1}, "B": {"I": 1}, "C": {}}
    },
    {
        "question": "周末空闲时，你更喜欢：",
        "options": {
            "A": "和朋友一起出去玩，参加各种社交活动",
            "B": "独自在家看书、听音乐或做自己的事",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"E": 1}, "B": {"I": 1}, "C": {}}
    },
    {
        "question": "面对新项目时，你倾向于：",
        "options": {
            "A": "先收集大量具体信息和案例，再开始规划",
            "B": "先构思整体框架和创新可能性，再补充细节",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"S": 1}, "B": {"N": 1}, "C": {}}
    },
    {
        "question": "在学习新知识时，你更注重：",
        "options": {
            "A": "掌握具体的事实、数据和实用技能",
            "B": "理解背后的原理、模式和未来应用",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"S": 1}, "B": {"N": 1}, "C": {}}
    },
    {
        "question": "朋友向你倾诉烦恼时，你的第一反应是：",
        "options": {
            "A": "分析问题原因，提供解决方案和建议",
            "B": "倾听并给予情感支持，让朋友感受到被理解",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"T": 1}, "B": {"F": 1}, "C": {}}
    },
    {
        "question": "做重要决定时，你主要依据：",
        "options": {
            "A": "客观分析利弊，基于逻辑和效率考虑",
            "B": "考虑对自己和他人的情感影响，重视价值观",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"T": 1}, "B": {"F": 1}, "C": {}}
    },
    {
        "question": "对于计划和时间安排，你更倾向于：",
        "options": {
            "A": "制定详细计划并严格执行，喜欢确定性",
            "B": "保持灵活性，根据情况随时调整和改变",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"J": 1}, "B": {"P": 1}, "C": {}}
    },
    {
        "question": "在处理任务时，你更喜欢：",
        "options": {
            "A": "提前完成，有充足时间检查和完善",
            "B": "在截止日期前完成，享受最后冲刺的感觉",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"J": 1}, "B": {"P": 1}, "C": {}}
    },
    # 新增8题以达到16题
    {
        "question": "参加社交活动后，你通常会：",
        "options": {
            "A": "感到精力充沛，甚至想继续下一个活动",
            "B": "感到有些疲惫，需要独处时间恢复精力",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"E": 1}, "B": {"I": 1}, "C": {}}
    },
    {
        "question": "在全新的环境中，你倾向于：",
        "options": {
            "A": "主动与新朋友交流，快速适应",
            "B": "先观察和思考，逐渐融入",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"E": 1}, "B": {"I": 1}, "C": {}}
    },
    {
        "question": "阅读一本书时，你更喜欢：",
        "options": {
            "A": "关注故事情节和具体细节，享受代入感",
            "B": "思考书中的深层含义和哲学思想",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"S": 1}, "B": {"N": 1}, "C": {}}
    },
    {
        "question": "你更喜欢哪种类型的电影或游戏？",
        "options": {
            "A": "情节写实、操作具体，能带来真实体验的",
            "B": "充满奇幻、富有想象力，能探索未知世界的",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"S": 1}, "B": {"N": 1}, "C": {}}
    },
    {
        "question": "朋友情绪低落时，你如何回应？",
        "options": {
            "A": "帮他分析原因，给出解决问题的具体方法",
            "B": "安慰和支持，让他感受到被关心和理解",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"T": 1}, "B": {"F": 1}, "C": {}}
    },
    {
        "question": "在团队协作中，你更看重：",
        "options": {
            "A": "任务的效率和成果，确保按时完成目标",
            "B": "团队的氛围和成员的感受，保持良好关系",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"T": 1}, "B": {"F": 1}, "C": {}}
    },
    {
        "question": "你的房间通常是：",
        "options": {
            "A": "整洁有序，物品各归其位",
            "B": "随性舒适，充满个人特色但不一定井井有条",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"J": 1}, "B": {"P": 1}, "C": {}}
    },
    {
        "question": "面对假期安排，你更倾向于：",
        "options": {
            "A": "提前做好详细的旅行计划和日程表",
            "B": "随心所欲，说走就走，享受探索的乐趣",
            "C": "不确定 / 中立"
        },
        "scoring": {"A": {"J": 1}, "B": {"P": 1}, "C": {}}
    }
]

class ConversationState(Enum):
    """
    对话状态枚举
    定义用户在整个对话流程中的所有可能状态
    """
    # 初始状态
    INITIAL_GREETING = "initial_greeting"
    
    # 用户信息收集阶段
    COLLECTING_NICKNAME = "collecting_nickname"
    COLLECTING_MOOD = "collecting_mood"
    COLLECTING_PRESSURE = "collecting_pressure"
    COLLECTING_DESIRE = "collecting_desire"
    COLLECTING_ENERGY_RITUAL = "collecting_energy_ritual"
    COLLECTING_SUPPORT_NETWORK = "collecting_support_network"
    
    # 荣格八维测试阶段
    INTRO_JUNGIAN_TEST = "intro_jungian_test"
    JUNGIAN_CHOICE = "jungian_choice"  # 新增：用户选择是否进行荣格测试
    IN_JUNGIAN_TEST = "in_jungian_test"
    JUNGIAN_TEST_COMPLETED = "jungian_test_completed"
    
    # 情绪疏导阶段
    INTRO_EMOTION_GUIDANCE = "intro_emotion_guidance"
    EMOTION_CHOICE = "emotion_choice"  # 新增：用户选择是否进行系统思维
    EMOTION_WHAT = "emotion_what"
    EMOTION_COMPANION_CHOICE = "emotion_companion_choice"
    EMOTION_WHY = "emotion_why"
    EMOTION_HOW = "emotion_how"
    
    # 🚨 新增：自由聊天中主动引导情绪分析的状态
    GUIDED_EMOTION_WHAT = "guided_emotion_what"
    GUIDED_EMOTION_WHY = "guided_emotion_why"
    GUIDED_EMOTION_HOW = "guided_emotion_how"
    
    # 自由聊天和特殊状态
    FREE_CHAT = "free_chat"
    EMERGENCY_MODE = "emergency_mode"
    
    # 功能模块状态
    HAPPINESS_LOG_INPUT = "happiness_log_input"
    DIARY_INPUT = "diary_input"
    PREFERENCE_INPUT = "preference_input"

def get_jungian_question_template(nickname: str, question_index: int) -> str:
    """
    获取荣格八维测试题目模板
    
    Args:
        nickname: 用户昵称
        question_index: 题目索引 (0-15)
        
    Returns:
        str: 格式化的题目模板
    """
    if question_index >= len(JUNGIAN_TEST_QUESTIONS):
        return f"测试已完成！{nickname}，让我为你分析结果～"
    
    question_data = JUNGIAN_TEST_QUESTIONS[question_index]
    
    return f"""
继续测试中！{nickname} 💪

---

**第{question_index + 1}题 / {len(JUNGIAN_TEST_QUESTIONS)}题** 🎯

**{question_data['question']}**

**A.** {question_data['options']['A']}
**B.** {question_data['options']['B']}
**C.** {question_data['options']['C']}

请输入 A、B 或 C：
    """.strip()

def calculate_jungian_result(answers: list) -> dict:
    """
    计算荣格八维测试结果
    
    Args:
        answers: 用户答案列表，如 ['A', 'B', 'C', ...]
        
    Returns:
        dict: 包含维度得分和MBTI类型的结果
    """
    scores = {"E": 0, "I": 0, "S": 0, "N": 0, "T": 0, "F": 0, "J": 0, "P": 0}
    
    for i, answer in enumerate(answers):
        # 🚨 修复：处理C选项，不添加分数
        if i < len(JUNGIAN_TEST_QUESTIONS) and answer.upper() in ['A', 'B']:
            question = JUNGIAN_TEST_QUESTIONS[i]
            scoring = question['scoring'][answer.upper()]
            for dimension, points in scoring.items():
                scores[dimension] += points
    
    # 确定MBTI类型
    mbti_type = ""
    # 如果某个维度得分相同，默认倾向于第一个字母，例如E/I得分相同倾向E
    mbti_type += "E" if scores["E"] >= scores["I"] else "I"
    mbti_type += "S" if scores["S"] >= scores["N"] else "N"
    mbti_type += "T" if scores["T"] >= scores["F"] else "F"
    mbti_type += "J" if scores["J"] >= scores["P"] else "P"
    
    return {
        "scores": scores,
        "mbti_type": mbti_type,
        "answers": answers
    }

class StateTransitionManager:
    """
    状态转换管理器
    负责根据用户输入和当前状态决定下一个状态
    """
    
    def __init__(self):
        """初始化状态转换管理器"""
        self.emergency_keywords = [
            '自杀', '轻生', '不想活', '想死', '结束生命', '自我伤害',
            '割腕', '跳楼', '服药', '上吊', '自尽', '寻死',
            '没有意义', '活着太痛苦', '解脱', '一了百了'
        ]
        # 🚨 新增：情绪触发关键词
        self.emotion_trigger_keywords = [
            '难过', '伤心', '痛苦', '沮丧', '低落', '不开心', '郁闷',
            '烦躁', '焦虑', '紧张', '压力大', '困扰', '迷茫', '不知所措',
            '孤独', '无聊', '空虚', '累', '疲惫', '失落', '绝望', '压抑',
            '委屈', '生气', '愤怒', '恐惧', '害怕', '担心', '不安',
            '迷失', '彷徨', '挣扎', '糟糕', '难受', '心烦', '烦恼',
            '不顺', '不公', '委屈', '崩溃', '爆炸', '不行了', '受不了',
            '很差劲', '没用', '失败', '无助', '拖延', '内耗', '想哭',
            '不被理解', '很辛苦', '煎熬', '想逃避', '被抛弃', '心痛', '窒息',
            '失眠', '胃疼', '头痛', '生病', '不舒服', '身体不适', '没精神',
            '厌学', '退学', '挂科', '学习差', '考试焦虑', '不想学习',
            '人际关系', '朋友', '室友', '同学', '老师', '社交恐惧', '吵架',
            '感情', '恋爱', '分手', '表白', '暗恋', '失恋', '情侣',
            '未来', '毕业', '工作', '考研', '出国', '人生', '方向', '出路',
            '家庭', '父母', '亲戚', '争吵', '矛盾', '压力'
        ]
        # 🚨 新增：排除词，避免误判，例如“笑死了”
        self.emotion_exclude_patterns = ['笑死', '气死', '累死', '热死', '冷死', '忙死', '急死', '太难了']
    
    def get_next_state(self, current_state: ConversationState, 
                      user_input: str, user_profile_data: Dict) -> ConversationState:
        """
        根据当前状态、用户输入和用户档案确定下一个状态
        
        Args:
            current_state: 当前对话状态
            user_input: 用户输入内容
            user_profile_data: 用户档案数据
            
        Returns:
            ConversationState: 下一个状态
        """
        # 首先检查紧急情况
        if self._is_emergency(user_input):
            return ConversationState.EMERGENCY_MODE
        
        # 根据当前状态决定下一状态
        if current_state == ConversationState.INITIAL_GREETING:
            return ConversationState.COLLECTING_NICKNAME
        
        elif current_state == ConversationState.COLLECTING_NICKNAME:
            return ConversationState.COLLECTING_MOOD
        
        elif current_state == ConversationState.COLLECTING_MOOD:
            return ConversationState.COLLECTING_PRESSURE
        
        elif current_state == ConversationState.COLLECTING_PRESSURE:
            return ConversationState.COLLECTING_DESIRE
        
        elif current_state == ConversationState.COLLECTING_DESIRE:
            return ConversationState.COLLECTING_ENERGY_RITUAL
        
        elif current_state == ConversationState.COLLECTING_ENERGY_RITUAL:
            return ConversationState.COLLECTING_SUPPORT_NETWORK
        
        elif current_state == ConversationState.COLLECTING_SUPPORT_NETWORK:
            return ConversationState.JUNGIAN_CHOICE
        
        elif current_state == ConversationState.JUNGIAN_CHOICE:
            # 检查用户是否选择进行测试
            if self._wants_to_skip(user_input):
                return ConversationState.EMOTION_CHOICE
            else:
                return ConversationState.IN_JUNGIAN_TEST
        
        elif current_state == ConversationState.INTRO_JUNGIAN_TEST:
            return ConversationState.IN_JUNGIAN_TEST
        
        elif current_state == ConversationState.IN_JUNGIAN_TEST:
            # 🚨 修复：检查用户是否想要跳过测试
            if self._wants_to_skip(user_input):
                return ConversationState.EMOTION_CHOICE
            
            # 🚨 关键修复：基于当前已保存的答案数量来判断状态
            # 注意：这里不修改数据，只读取已保存的答案
            test_answers = user_profile_data.get('jungian_test_answers', [])
            current_question_count = len(test_answers)
            
            # 如果用户刚输入了有效答案，下一个状态应该基于新的题目数量
            if user_input.upper() in ['A', 'B']:
                # 预测保存后的题目数量
                next_question_count = current_question_count + 1
                
                # 检查是否完成8题测试
                if next_question_count > len(JUNGIAN_TEST_QUESTIONS):
                    return ConversationState.JUNGIAN_TEST_COMPLETED
                else:
                    # 继续测试，保持当前状态（会显示下一题）
                    return ConversationState.IN_JUNGIAN_TEST
            else:
                # 用户输入无效，保持当前状态
                return ConversationState.IN_JUNGIAN_TEST
        
        elif current_state == ConversationState.JUNGIAN_TEST_COMPLETED:
            return ConversationState.EMOTION_CHOICE
        
        elif current_state == ConversationState.EMOTION_CHOICE:
            # 检查用户是否选择进行系统思维
            if self._wants_to_skip(user_input):
                return ConversationState.FREE_CHAT
            else:
                return ConversationState.EMOTION_WHAT
        
        elif current_state == ConversationState.INTRO_EMOTION_GUIDANCE:
            # 根据用户选择决定
            if 'A' in user_input.upper() or '开始' in user_input:
                return ConversationState.EMOTION_WHAT
            elif 'B' in user_input.upper() or '随便聊' in user_input:
                return ConversationState.FREE_CHAT
            else:
                return ConversationState.FREE_CHAT
        
        elif current_state == ConversationState.EMOTION_WHAT:
            # 用户描述了问题后，进入陪伴选择
            return ConversationState.EMOTION_COMPANION_CHOICE
        
        elif current_state == ConversationState.EMOTION_COMPANION_CHOICE:
            # 根据用户选择决定下一步
            if 'A' in user_input.upper():
                return ConversationState.FREE_CHAT  # 纯倾听模式进入自由聊天
            elif 'B' in user_input.upper():
                return ConversationState.FREE_CHAT  # 静默模式进入自由聊天
            elif 'C' in user_input.upper():
                return ConversationState.EMOTION_WHY  # 继续深入分析
            else:
                return ConversationState.EMOTION_WHY  # 默认继续分析
        
        elif current_state == ConversationState.EMOTION_WHY:
            # 分析完原因后，进入解决方案
            return ConversationState.EMOTION_HOW
        
        elif current_state == ConversationState.EMOTION_HOW:
            # 完成系统思维训练，进入自由聊天
            return ConversationState.FREE_CHAT
        
        # 🚨 新增：自由聊天模式下的主动引导状态转换
        elif current_state == ConversationState.FREE_CHAT:
            if self._has_emotion_trigger(user_input):
                return ConversationState.GUIDED_EMOTION_WHAT # 触发主动引导
            else:
                return ConversationState.FREE_CHAT # 仍然是自由聊天
                
        elif current_state == ConversationState.GUIDED_EMOTION_WHAT:
            # 用户描述问题后，进入引导的“为什么”阶段
            return ConversationState.GUIDED_EMOTION_WHY
            
        elif current_state == ConversationState.GUIDED_EMOTION_WHY:
            # 用户描述原因后，进入引导的“怎么办”阶段
            return ConversationState.GUIDED_EMOTION_HOW
            
        elif current_state == ConversationState.GUIDED_EMOTION_HOW:
            # 用户完成行动计划后，回到自由聊天
            return ConversationState.FREE_CHAT
        
        else:
            # 默认保持当前状态或进入自由聊天
            return ConversationState.FREE_CHAT
    
    def _is_emergency(self, user_input: str) -> bool:
        """
        检测用户输入是否包含紧急关键词
        
        Args:
            user_input: 用户输入
            
        Returns:
            bool: 是否为紧急情况
        """
        user_input_lower = user_input.lower()
        return any(keyword in user_input_lower for keyword in self.emergency_keywords)
    
    def _wants_to_skip(self, user_input: str) -> bool:
        """
        检测用户是否想要跳过当前环节
        
        Args:
            user_input: 用户输入
            
        Returns:
            bool: 是否想要跳过
        """
        skip_patterns = ['跳过', '不想', '算了', '直接聊', '先不']
        user_input_lower = user_input.lower()
        return any(pattern in user_input_lower for pattern in skip_patterns)
    
    def _has_emotion_trigger(self, user_input: str) -> bool:
        """
        检测用户输入是否包含情绪触发关键词，同时排除特定模式
        
        Args:
            user_input: 用户输入
            
        Returns:
            bool: 是否包含情绪触发关键词
        """
        user_input_lower = user_input.lower()
        
        # 排除模式检查
        for exclude_pattern in self.emotion_exclude_patterns:
            if exclude_pattern in user_input_lower:
                return False
        
        # 情绪关键词检查
        for keyword in self.emotion_trigger_keywords:
            # 使用正则表达式进行更精准的匹配，避免匹配到词语的一部分
            if re.search(r'\b' + re.escape(keyword) + r'\b', user_input_lower):
                return True
        return False
    
    def _get_jungian_test_template(self, nickname: str, user_profile_data: Dict) -> str:
        """
        获取荣格八维测试的动态模板
        
        Args:
            nickname: 用户昵称
            user_profile_data: 用户档案数据
            
        Returns:
            str: 测试模板
        """
        # 🚨 关键修复：确保数据安全性，防止None值错误
        if user_profile_data is None:
            user_profile_data = {}
        
        test_answers = user_profile_data.get('jungian_test_answers', []) or []
        question_index = len(test_answers) if test_answers else 0
        
        if question_index == 0:
            # 第一题，显示测试介绍
            return f"""
太好了！{nickname}，让我们开始荣格八维测试！🎉

这是{len(JUNGIAN_TEST_QUESTIONS)}题精准测试，每题都是日常生活中的选择，没有对错之分，选择最符合你直觉的答案就好～

**测试说明：**
- 每题有A、B、C三个选项
- 请直接输入"A"、"B"或"C"
- 相信第一直觉，不要过度思考

让我们开始吧！

---

**第1题 / {len(JUNGIAN_TEST_QUESTIONS)}题** 🎯

**{JUNGIAN_TEST_QUESTIONS[0]['question']}**

**A.** {JUNGIAN_TEST_QUESTIONS[0]['options']['A']}
**B.** {JUNGIAN_TEST_QUESTIONS[0]['options']['B']}
**C.** {JUNGIAN_TEST_QUESTIONS[0]['options']['C']}

请输入 A、B 或 C：
            """.strip()
        elif question_index < len(JUNGIAN_TEST_QUESTIONS):
            # 后续题目
            question_data = JUNGIAN_TEST_QUESTIONS[question_index]
            return f"""
继续测试中！{nickname} 💪

---

**第{question_index + 1}题 / {len(JUNGIAN_TEST_QUESTIONS)}题** 🎯

**{question_data['question']}**

**A.** {question_data['options']['A']}
**B.** {question_data['options']['B']}
**C.** {question_data['options']['C']}

请输入 A、B 或 C：
            """.strip()
        else:
            # 测试完成
            return f"测试已完成！{nickname}，让我为你分析结果～"
    
    def get_state_prompt_template(self, state: ConversationState, 
                                 user_profile_data: Dict) -> str:
        """
        根据状态获取对应的提示词模板
        
        Args:
            state: 当前状态
            user_profile_data: 用户档案数据
            
        Returns:
            str: 提示词模板
        """
        nickname = user_profile_data.get('current_nickname', '同学')
        
        templates = {
            ConversationState.INITIAL_GREETING: """
🌟 遇见你真是一件美好的事～我是你的心灵伙伴喵呜🌱！你的到来让我心里暖暖的！✨

为了更好地陪伴你，我想先了解你一点点～这样我们的交流就能更加贴心有温度！

**为什么要这样做呢？** 💫
就像朋友初次见面，彼此了解能让对话更有深度，我也能给你更走心的回应～

放心，这里很安全，不想回答的随时可以说"跳过"哦！我们慢慢开始吧～ 

首先，怎么称呼会让你感觉更自在呢？(比如：小琳、阿哲、XX同学～)
            """.strip(),
            
            # 🚨 修复：COLLECTING_NICKNAME状态应该询问昵称，不是心情
            ConversationState.COLLECTING_NICKNAME: """
很高兴遇到你！现在想了解一下怎么称呼你比较自在呢？

**为什么要问这个呢？** 💭
就像朋友初次见面，知道怎么称呼你，能让我们的交流更有温度，更加贴心！

(比如：小琳、阿哲、XX同学～ 或者叫我取个专属昵称也行！)
            """.strip(),
            
            ConversationState.COLLECTING_MOOD: f"""
{nickname}，好喜欢这个名字！🌷现在想轻轻问问你此刻的心情～

**为什么要了解心情呢？** 🌈
情绪是我们内心的天气预报，知道你的"天气"，我就能调整陪伴的方式，让我们的交流更舒适～

用1-2个词或小表情形容此刻的心情吧～ 
比如：😌平静 / 🌧️有些低落 / 🥱疲惫 / 🌪️思绪纷乱 / ✨小期待 / 🌈还不错～

(用自己的话描述也很欢迎哦！)
            """.strip(),
            
            ConversationState.COLLECTING_PRESSURE: f"""
嗯，我感受到你的心情了～ {nickname}💖

接下来想更深入地认识你，这样我就能给你更有针对性的支持！✨

**为什么需要了解这些呢？** 💭
就像绘制心灵地图，了解你的处境能让我更精准地陪伴你，而不是泛泛而谈哦！

**最近有什么让你感到"心头沉甸甸"或"需要喘口气"的事情吗？**
比如：📚期末/论文DDL追杀、👥小组合作心累、🏠想家/宿舍小摩擦、💼实习/未来焦虑、💤单纯睡不够... 或者暂时没有特别的事？) 

选1-2件最想说的分享给我吧～ (如果暂时没有特别压力，也可以说"最近还好")
            """.strip(),
            
            ConversationState.COLLECTING_DESIRE: f"""
嗯嗯，谢谢你分享这些，{nickname}！现在，现在有个很重要的问题想问你～

**悄悄告诉我，这次来找喵呜，最希望得到什么样的支持呢？** 💝

**为什么要问这个呢？** 💡
这个是很重要的！了解你来找小树洞最希望我帮你点啥，能让我更清楚地知道你的核心需求，更好地指导我如何陪伴你！

比如：
👂 **有人安静听你说说** - 我会做专注的听众，不打断不建议
🌿 **找点解压小妙招** - 分享实用的小技巧和放松方式  
🤔 **聊聊某个纠结的选择** - 我们一起梳理思路，看清方向
📅 **解锁学习&时间管理技能** - 提供高效学习与生活的方法
💡 **需要点灵感鼓励** - 给你温暖的鼓励和正能量
🎈  **纯粹想放松聊聊天～** - 像朋友一样自在交流

你此刻最需要的是哪种呢？可以直接告诉我或选择上面的描述～
            """.strip(),
            
            ConversationState.COLLECTING_ENERGY_RITUAL: f"""
好的，我明白你的心愿了，{nickname}！接下来，咱们聊点轻松的～

**当你感到"能量耗尽"时，有什么专属的充电方式吗？** 🔋

**为什么要问这个呢？** 💭
了解你平时怎么'回血'，能帮我给你提供更适合你的放松建议，让你电量满满！

比如:
 🎧 单曲循环某首歌 / 🐱 撸猫/狗 / 🏃 暴汗运动 / 🌳 自然中散步 / 🍜 吃顿好的 / 📺 刷沙雕视频  / 📖 沉浸阅读 / ☕ 发呆放空... 

 还是正在寻找适合自己的恢复方式？说说你的能量恢复秘诀吧～
            """.strip(),
            
            ConversationState.COLLECTING_SUPPORT_NETWORK: f"""
这种充电方式听起来好治愈！🌼
谢谢你分享回血小仪式，{nickname}！最后温暖的小问题～

**当你需要支持时，身边有可以依靠的人吗？** 🫂

**为什么要问这个呢？** 💭
了解你的社会支持系统，能让我知道在你需要帮助时，身边还有哪些温暖的力量，我们一起帮你构建更坚实的后盾！

比如：
👫 **有几位铁杆好友** - 有几个知心朋友
👨‍👩‍👧 **家人超给力 ** - 家人给予力量  
🧑‍🏫 **有信任的老师** - 师长提供指导
🫂 **有但不太想打扰** - 不好意思麻烦别人
😶 **还在组建小分队中...** - 慢慢拓展人际网络

你的情况更接近哪一种呢？
            """.strip(),
            
            ConversationState.JUNGIAN_CHOICE: f"""
太棒啦！{nickname}，谢谢你愿意分享这些～ 喵呜已经对你有了基本的了解～ ✨

现在想邀请你参加一个超有趣的心理小测试！🎮

**这是什么呢？** 
荣格八维性格测试 - 只需要{len(JUNGIAN_TEST_QUESTIONS)}个简单的选择题，就能深入了解你的思维方式和行为偏好！

**对我有什么好处？** 💫
- 让我能提供更符合你性格的陪伴
- 帮助你发现自己的独特优势  
- 让我们的交流更有深度和效果

**我可以选择吗？** 🤗
当然可以！你可以：
🎯 **参加测试** - 输入"开始测试"或"好的"
🎈 **直接聊天** - 输入"跳过"或"直接聊天"

无论你选择什么，我都会全力陪伴你！但如果你愿意花几分钟，我就能更懂你，给你更贴心的支持～
**你想怎么做呢？**
            """.strip(),
            
            ConversationState.IN_JUNGIAN_TEST: self._get_jungian_test_template(nickname, user_profile_data),
            
            ConversationState.JUNGIAN_TEST_COMPLETED: self._get_jungian_analysis_template(nickname, user_profile_data),
            
            ConversationState.INTRO_JUNGIAN_TEST: f"""
好的，{nickname}！喵呜已经了解你的心情啦～ 现在想和你玩个超有趣的小游戏！✨

**为什么要玩这个游戏呢？** 🎮
这是专业的荣格八维性格测试，能帮我更深入地了解你的思维方式和行为偏好！

**这样做有什么好处呢？** 💫
- 让我能给你更贴合你性格的建议
- 帮你更好地认识自己的优势和特点
- 让我们后面的聊天更有针对性和效果

**会很复杂吗？** 🤔
完全不会！就是{len(JUNGIAN_TEST_QUESTIONS)}个超简单的选择题，比如'聚会结束你是想继续嗨还是回家充电'这样的日常选择题～

**不想做可以吗？** 🤗
当然可以跳过！但如果你愿意花2-3分钟，我就能更懂你，给你更好的陪伴哦！

准备好开始了吗？还是想直接聊聊你的心情？(输入'开始测试'或'跳过'都可以～)
            """.strip(),
            
            ConversationState.EMOTION_CHOICE: f"""
棒极了！{nickname}，现在我们来到了情绪探索的环节～ 💝

**系统思维训练是什么呢？** 🤔
这是一套超实用的"是什么→为什么→怎么办"思维方法，不仅能帮你处理情绪问题，生活中遇到的各种挑战都能用！

**对我有什么好处？** ✨
- 学会更好地理解自己的感受
- 掌握终身受益的问题解决能力
- 让负面情绪成为帮助你成长的老师

**我可以选择吗？** 🤗
当然可以！你可以：
🧠 **开始系统思维训练** - 输入"开始训练"或"好的"
🎈 **直接自由聊天** - 输入"跳过"或"自由聊天"

无论你选择什么，我都会全心陪伴你！你想怎么做呢？
            """.strip(),
            
            ConversationState.EMOTION_WHAT: f"""
太好了！{nickname}，让我们开始系统思维的第一步：**是什么** 🔍

**为什么要先搞清楚"是什么"呢？** 💡
就像给感受贴个标签一样，当我们能准确地说出"我现在是焦虑"而不是"我就是不舒服"，情绪就不再那么可怕了！

现在，让我们一起来"看见"你的情绪～

**请描述一下：**
最近有什么事情让你感到困扰、不舒服或者想要改变的吗？

可以是学习、人际、情感、未来规划等任何方面～尽量具体地描述一下情况。

比如："最近总是拖延作业，明明知道deadline要到了，但还是忍不住刷手机..."

*没关系，想到什么就说什么，我会认真听的～*
            """.strip(),
            
            ConversationState.EMOTION_COMPANION_CHOICE: f"""
听到你的困扰，我的心里也有点心疼呢... 🌸

{nickname}，现在你可以选择接下来我们要怎么做：

**A. 我在这里，你只管说，我陪着你听** 👂
就是纯粹的倾听，你想说什么就说什么，我会安静地陪伴你

**B. 暂时什么都不想，我们一起安静一会儿** ☕
有时候什么都不想，静静地待着，也是很好的

**C. 准备好啦，我们来深入聊聊"为什么"会这样** 🔍
一起探索问题背后的原因，找到解决的方向

你想选择哪一个呢？输入 A、B 或 C 就可以～
            """.strip(),
            
            ConversationState.EMOTION_WHY: f"""
很好！{nickname}，让我们进入系统思维的第二步：**为什么** 🤔

**为什么要深挖原因呢？** 💡
理解"为什么"是找到解决之道的前提，也是培养批判性思维的关键！

让我们从三个层面来深入分析：

**📅 第一层：事件层**
发生了什么让这个问题登场？想想具体的触发事件，是什么时候、什么情况下开始出现这个困扰的？

**💔 第二层：痛点层**  
最扎心的是哪一幕？在整个过程中，哪个瞬间或哪句话最让你难受？什么最戳中了你的痛点？

**🌊 第三层：影响层**
带来了哪些具体困扰？这件事对你的生活、学习、心情产生了什么具体的影响？

*慢慢来，想到哪个层面就先说哪个，我们一步步梳理～*
            """.strip(),
            
            ConversationState.EMOTION_HOW: f"""
太棒了！{nickname}，现在我们来到系统思维的第三步：**怎么办** 💡

**让我们先深呼吸10秒～** 🌬️
慢慢地用鼻子吸气，数到4... 1、2、3、4
轻轻地用嘴巴呼气，数到6... 1、2、3、4、5、6
感受身体的放松，感受思维的清晰～

**现在想想：现在的你能做什么？** 💪
记住，**即使是很小的事情都可以**！

**🎯 思考能力范围**
针对前面分析的原因，你现在能做什么来改善情况？从小处着手，降低行动门槛，想想最容易开始的小行动。

**📌 Mini任务设计**  
给明早的自己贴个mini任务贴纸，写啥？（≤15字越好）

将行动与原因挂钩，设计一个具体可执行的小任务。

*当你开始行动就已经走在胜利的道路上！行动比完美更重要～*

**请分享一下你的行动计划～**
            """.strip(),
            
            ConversationState.FREE_CHAT: f"""
🎉 太棒了！{nickname}，我们完成了一次完整的心理成长之旅！

**回顾一下我们的收获：**
✨ **深度了解** - 通过信息收集，我更懂你了
🧠 **性格洞察** - 荣格八维测试揭示了你的思维特点  
🛠️ **思维工具** - 系统思维"是什么→为什么→怎么办"现在是你的终身技能

**现在我们进入自由聊天模式！** 💬

你可以和我聊任何话题：
- 日常的开心事或烦恼
- 学习生活中的困惑
- 未来规划的想法
- 或者就是想找个人说说话

我会结合对你的了解，提供更贴心、更个性化的陪伴和建议～

有什么想聊的吗？
            """.strip(),
            
            # 🚨 新增：主动引导情绪分析状态的提示模板
            ConversationState.GUIDED_EMOTION_WHAT: f"""
喵呜感受到了你可能有一些情绪需要梳理呢～ 🌸

**要不要我们用"是什么→为什么→怎么办"的系统思维方式，一起来看看它呢？**

这能帮你更清晰地认识和处理自己的感受。你想尝试吗？
（输入'好的'开始，或者'暂时不用'继续自由聊天～）
            """.strip(),
            
            ConversationState.GUIDED_EMOTION_WHY: f"""
很好！既然你愿意探索，那我们进入第二步：**为什么** 🤔

**是什么让你产生了这种感受呢？**

试着回忆一下，是哪个具体事件、什么情况或哪些想法，让你产生了这种情绪？

*慢慢来，想到什么就说什么，我会认真听的～*
            """.strip(),
            
            ConversationState.GUIDED_EMOTION_HOW: f"""
嗯嗯，我理解了这些原因。现在，让我们一起思考：**怎么办** 💡

**针对你描述的情况和原因，现在你能做什么来改善它呢？**

即使是很小很小的事情，也值得尝试哦！比如：
- 找朋友聊聊
- 听听音乐放松一下
- 写下自己的感受
- 改变一个小的习惯

*分享一下你的想法吧，无论大小，都代表着你积极改变的勇气！💪*
            """.strip()
        }
        
        return templates.get(state, "")
    
    def _calculate_simple_mbti_type(self, scores: Dict[str, int]) -> str:
        """计算简单的MBTI类型（主对话流程专用）"""
        e_score = scores.get('E', 0)
        i_score = scores.get('I', 0)
        s_score = scores.get('S', 0)
        n_score = scores.get('N', 0)
        t_score = scores.get('T', 0)
        f_score = scores.get('F', 0)
        j_score = scores.get('J', 0)
        p_score = scores.get('P', 0)
        
        mbti_type = ""
        mbti_type += 'E' if e_score >= i_score else 'I'
        mbti_type += 'S' if s_score >= n_score else 'N'
        mbti_type += 'T' if t_score >= f_score else 'F'
        mbti_type += 'J' if j_score >= p_score else 'P'
        
        return mbti_type
    
    def _get_jungian_analysis_template(self, nickname: str, user_profile_data: Dict) -> str:
        """
        🚨 关键修复：生成包含完整MBTI分析的测试完成模板
        """
        try:
            # 从用户档案获取荣格得分
            jungian_scores = user_profile_data.get('jungian_scores', {})
            
            if not jungian_scores or not any(jungian_scores.values()):
                # 如果没有得分，返回基础完成信息
                return f"""
🎉 太棒了！{nickname}，你已经完成了荣格八维测试！

我已经获得了你的详细性格分析，这将帮助我更好地理解你、陪伴你！

现在让我们继续下一个环节～ 准备好了吗？

**接下来我们要做什么呢？** 💝
系统思维训练 - 学习"是什么→为什么→怎么办"的问题解决方法，这不仅能帮你处理情绪问题，生活中的各种挑战都能用到！

你愿意尝试吗？输入"好的"开始，或者"跳过"直接进入自由聊天～
                """.strip()
            
            # 🚨 主对话流程使用简单MBTI结果  
            mbti_type = self._calculate_simple_mbti_type(jungian_scores)
            
            # 简单MBTI类型描述（主对话专用）
            mbti_simple_descriptions = {
                "INTJ": {"name": "建筑师", "description": "理性的思想家，具有强烈的独立性和创新能力"},
                "INTP": {"name": "思想家", "description": "创新的发明家，对知识有着不可遏制的渴望"},
                "ENTJ": {"name": "指挥官", "description": "大胆、富有想象力、意志强烈的领导者"},
                "ENTP": {"name": "辩论家", "description": "聪明好奇的思想家，不会拒绝智力上的挑战"},
                "INFJ": {"name": "提倡者", "description": "安静而神秘，鼓舞人心且不知疲倦的理想主义者"},
                "INFP": {"name": "调停者", "description": "诗意而善良，总是乐于助人的利他主义者"},
                "ENFJ": {"name": "主人公", "description": "富有魅力且鼓舞人心的领导者，能够使听众着迷"},
                "ENFP": {"name": "竞选者", "description": "热情、有创造性、善于社交的自由精神"},
                "ISTJ": {"name": "物流师", "description": "实用主义的实干家，可靠性无可挑剔"},
                "ISFJ": {"name": "守护者", "description": "非常专注、温暖的守护者，时刻准备保护爱人"},
                "ESTJ": {"name": "总经理", "description": "出色的管理者，在管理事物或人员方面无与伦比"},
                "ESFJ": {"name": "执政官", "description": "非常关心他人、善于社交、广受欢迎的人"},
                "ISTP": {"name": "鉴赏家", "description": "大胆而实际的实验家，擅长使用各种工具"},
                "ISFP": {"name": "探险家", "description": "灵活、有魅力的艺术家，时刻准备探索新的可能性"},
                "ESTP": {"name": "企业家", "description": "聪明、精力充沛、善于感知的人，真正享受生活在边缘"},
                "ESFP": {"name": "表演者", "description": "自发性、精力充沛、热情的人，生活永远不会无聊"}
            }
            
            mbti_info = mbti_simple_descriptions.get(mbti_type, {"name": "独特类型", "description": "你拥有独特的个性组合"})
            
            # 生成主对话专用的详细分析
            detailed_analysis = f"""
🎯 **你的MBTI人格类型是：{mbti_type} - {mbti_info['name']}**

{mbti_info['description']}

你天生具备独特的个性优势，这次测试帮助我更好地了解你，让我能够为你提供更个性化的建议和陪伴！

**具体八维功能详解可在"荣格八维测试"这个选项卡进行具体测试后查看**
            """.strip()
            
            return f"""
🎉 **{nickname}，你的荣格八维测试完成啦！**

{detailed_analysis}

---

🌟 **现在让我们继续下一个环节～**

**接下来我们要做什么呢？** 💝
系统思维训练 - 学习"是什么→为什么→怎么办"的问题解决方法，这不仅能帮你处理情绪问题，生活中的各种挑战都能用到！

你愿意尝试吗？输入"好的"开始，或者"跳过"直接进入自由聊天～
            """.strip()
            
        except Exception as e:
            print(f"⚠️ 生成荣格分析模板失败: {e}")
            # 降级处理
            return f"""
🎉 太棒了！{nickname}，你已经完成了荣格八维测试！

我已经为你生成了详细的性格分析报告！你可以通过右侧的"荣格八维测试"标签页查看完整结果。

现在让我们继续下一个环节～ 准备好了吗？

**接下来我们要做什么呢？** 💝
系统思维训练 - 学习"是什么→为什么→怎么办"的问题解决方法，这不仅能帮你处理情绪问题，生活中的各种挑战都能用到！

你愿意尝试吗？输入"好的"开始，或者"跳过"直接进入自由聊天～
            """.strip()
    
    def is_terminal_state(self, state: ConversationState) -> bool:
        """
        判断是否为终止状态（不需要进一步引导的状态）
        
        Args:
            state: 对话状态
            
        Returns:
            bool: 是否为终止状态
        """
        terminal_states = {
            ConversationState.FREE_CHAT,
            ConversationState.EMERGENCY_MODE
        }
        return state in terminal_states
    
    def get_state_description(self, state: ConversationState) -> str:
        """
        获取状态的中文描述
        
        Args:
            state: 对话状态
            
        Returns:
            str: 状态描述
        """
        descriptions = {
            ConversationState.INITIAL_GREETING: "初始问候",
            ConversationState.COLLECTING_NICKNAME: "收集昵称",
            ConversationState.COLLECTING_MOOD: "了解心情",
            ConversationState.COLLECTING_PRESSURE: "了解压力源",
            ConversationState.COLLECTING_DESIRE: "了解核心需求",
            ConversationState.COLLECTING_ENERGY_RITUAL: "了解恢复方式",
            ConversationState.COLLECTING_SUPPORT_NETWORK: "了解支持网络",
            ConversationState.JUNGIAN_CHOICE: "荣格测试选择",
            ConversationState.INTRO_JUNGIAN_TEST: "引入荣格测试",
            ConversationState.IN_JUNGIAN_TEST: "进行荣格测试",
            ConversationState.JUNGIAN_TEST_COMPLETED: "荣格测试完成",
            ConversationState.EMOTION_CHOICE: "系统思维选择",
            ConversationState.INTRO_EMOTION_GUIDANCE: "引入情绪疏导",
            ConversationState.EMOTION_WHAT: "情绪识别",
            ConversationState.EMOTION_COMPANION_CHOICE: "陪伴选择",
            ConversationState.EMOTION_WHY: "原因探索",
            ConversationState.EMOTION_HOW: "解决方案",
            ConversationState.FREE_CHAT: "自由聊天",
            ConversationState.EMERGENCY_MODE: "紧急模式",
            # 🚨 新增：主动引导情绪分析状态的描述
            ConversationState.GUIDED_EMOTION_WHAT: "主动引导-是什么",
            ConversationState.GUIDED_EMOTION_WHY: "主动引导-为什么",
            ConversationState.GUIDED_EMOTION_HOW: "主动引导-怎么办"
        }
        return descriptions.get(state, "未知状态")

# 全局状态转换管理器实例
state_manager = StateTransitionManager()

# 导出主要函数供其他模块使用
def get_state_manager() -> StateTransitionManager:
    """获取状态转换管理器实例"""
    return state_manager

def get_conversation_state_from_string(state_string: str) -> ConversationState:
    """
    从字符串转换为对话状态枚举
    
    Args:
        state_string: 状态字符串
        
    Returns:
        ConversationState: 对话状态枚举
    """
    try:
        return ConversationState(state_string)
    except ValueError:
        return ConversationState.INITIAL_GREETING
