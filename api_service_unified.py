#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一API服务模块 - 融合增强版与原版优点
=====================================

设计理念：
1. 保留原版的稳定性和完整功能
2. 集成Camel-AI的智能增强特性
3. 提供完善的降级保护机制
4. 优化代码结构，减少冗余

核心优势：
- 🚀 Camel-AI智能增强 + 传统API兜底
- 🛡️ 双重危机检测（API + 本地关键词）
- 💝 智能安抚语生成 + 完整本地安抚语库
- 📊 使用统计和性能监控
- 🔄 完美向后兼容，零代码修改

作者：基于原版 api_service_modelscope.py 和 api_service_enhanced.py 融合优化
"""

import requests
import time
import json
import random
from typing import List, Dict, Optional
from contextlib import contextmanager

# ========================================================================
# 🔧 基础配置 - 保持与原版完全一致
# ========================================================================

# 🚨 统一配置：使用config_modelscope.py作为唯一配置源
from config_modelscope import DASHSCOPE_API_KEY

# API配置 - 修复URL路径重复问题
DASHSCOPE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
DEFAULT_MODEL = "qwen-max"
API_TIMEOUT = 30
MAX_RETRIES = 3

# 尝试导入Camel-AI增强功能
try:
    # 🚨 临时禁用Camel增强Agent，避免魔搭部署问题
    # from camel_enhanced_agents import (
    #     get_enhanced_manager,
    #     camel_ai_response,
    #     camel_thinking_analysis
    # )
    CAMEL_AI_AVAILABLE = False
    print("⚠️ Camel-AI增强模式已禁用，使用标准API")
except ImportError as e:
    CAMEL_AI_AVAILABLE = False
    print(f"📱 使用传统模式: {e}")

print(f"🔑 通义千问API已配置 | 🤖 默认模型: {DEFAULT_MODEL}")

# ========================================================================
# 📊 使用统计追踪器
# ========================================================================

class APIUsageTracker:
    """API使用情况跟踪器 - 监控服务质量"""
    
    def __init__(self):
        self.stats = {
            "camel_ai_calls": 0,
            "traditional_calls": 0,
            "camel_ai_success": 0,
            "camel_ai_failures": 0,
            "total_response_time": 0.0,
            "call_count": 0,
            "emergency_detections": 0,
            "comfort_generations": 0
        }
    
    def record_camel_call(self, success: bool, response_time: float):
        """记录Camel-AI调用"""
        self.stats["camel_ai_calls"] += 1
        if success:
            self.stats["camel_ai_success"] += 1
        else:
            self.stats["camel_ai_failures"] += 1
        self._record_timing(response_time)
    
    def record_traditional_call(self, response_time: float):
        """记录传统API调用"""
        self.stats["traditional_calls"] += 1
        self._record_timing(response_time)
    
    def record_emergency_detection(self):
        """记录危机检测"""
        self.stats["emergency_detections"] += 1
    
    def record_comfort_generation(self):
        """记录安抚语生成"""
        self.stats["comfort_generations"] += 1
    
    def _record_timing(self, response_time: float):
        """记录响应时间"""
        self.stats["total_response_time"] += response_time
        self.stats["call_count"] += 1
    
    def get_success_rate(self) -> float:
        """获取Camel-AI成功率"""
        if self.stats["camel_ai_calls"] == 0:
            return 0.0
        return self.stats["camel_ai_success"] / self.stats["camel_ai_calls"]
    
    def get_average_response_time(self) -> float:
        """获取平均响应时间"""
        if self.stats["call_count"] == 0:
            return 0.0
        return self.stats["total_response_time"] / self.stats["call_count"]
    
    def get_usage_summary(self) -> Dict:
        """获取完整使用摘要"""
        return {
            **self.stats,
            "camel_ai_success_rate": self.get_success_rate(),
            "average_response_time": self.get_average_response_time(),
            "camel_ai_usage_percentage": (
                self.stats["camel_ai_calls"] / max(self.stats["call_count"], 1) * 100
            )
        }

# 全局使用跟踪器
usage_tracker = APIUsageTracker()

# ========================================================================
# 🧠 智能上下文检测器 - Camel-AI增强特性
# ========================================================================

def detect_context_type(user_message: str, messages: List[Dict] = None) -> str:
    """
    智能检测用户消息的上下文类型
    用于选择最适合的处理方式
    """
    if not messages:
        messages = []
    
    # 系统思维关键词
    thinking_keywords = [
        "分析", "为什么", "怎么办", "原因", "解决", "问题", 
        "困扰", "思考", "理解", "探索", "如何", "方法"
    ]
    
    # 情绪支持关键词  
    emotion_keywords = [
        "感受", "情绪", "心情", "焦虑", "抑郁", "压力",
        "难过", "开心", "担心", "害怕", "愤怒", "烦躁"
    ]
    
    # 荣格八维测试关键词
    jungian_keywords = [
        "性格", "人格", "测试", "八维", "mbti", "类型",
        "内向", "外向", "直觉", "感觉"
    ]
    
    # 检查最近几条消息的系统上下文
    recent_context = " ".join([
        msg.get("content", "") 
        for msg in messages[-3:] 
        if msg.get("role") in ["user", "assistant"]
    ])
    
    full_context = recent_context + " " + user_message
    
    # 优先级检测
    if any(keyword in full_context for keyword in thinking_keywords):
        return "system_thinking"
    elif any(keyword in user_message for keyword in jungian_keywords):
        return "jungian_analysis"
    elif any(keyword in user_message for keyword in emotion_keywords):
        return "emotional_support"
    else:
        return "general"

def validate_response(response: str) -> bool:
    """验证AI回复的基本质量"""
    if not response or len(response.strip()) < 10:
        return False
    
    # 检查是否包含错误信息
    error_indicators = ["错误", "异常", "失败", "Error", "Exception", "抱歉，我遇到了问题"]
    if any(indicator in response for indicator in error_indicators):
        return False
    
    return True

# ========================================================================
# 🔧 核心API调用函数 - 融合智能增强
# ========================================================================

def get_modelscope_response(messages: List[Dict[str, str]], 
                          temperature: float = 0.7,
                          use_camel: bool = True) -> str:
    """
    统一API调用函数 - 融合Camel-AI增强与传统API
    
    Args:
        messages: OpenAI格式的消息列表
        temperature: 温度参数（保持兼容性）
        use_camel: 是否使用Camel-AI增强（默认True）
    
    Returns:
        str: AI生成的回复
    """
    start_time = time.time()
    
    # 提取最后一条用户消息
    user_message = ""
    for msg in reversed(messages):
        if msg.get("role") == "user":
            user_message = msg.get("content", "")
            break
    
    if not user_message:
        return "我没有听清楚你说什么，可以再说一遍吗？"
    
    # 🐪 优先使用Camel-AI增强回复
    if CAMEL_AI_AVAILABLE and use_camel:
        try:
            # 智能检测上下文类型
            context_type = detect_context_type(user_message, messages)
            # response = camel_ai_response(user_message, context_type)  # 已禁用
            response = None  # 禁用Camel-AI，降级到标准API
            
            # 验证回复质量
            if validate_response(response):
                usage_tracker.record_camel_call(True, time.time() - start_time)
                return response
            else:
                print("⚠️ Camel-AI回复质量不佳，回退到传统模式")
                
        except Exception as e:
            print(f"⚠️ Camel-AI调用失败，回退到传统模式: {e}")
            usage_tracker.record_camel_call(False, time.time() - start_time)
    
    # 📱 回退到传统API调用
    response = _traditional_api_call(messages, temperature)
    usage_tracker.record_traditional_call(time.time() - start_time)
    return response

def _traditional_api_call(messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
    """
    传统通义千问API调用 - 保持与原版完全一致
    """
    for attempt in range(MAX_RETRIES):
        try:
            print(f"🔄 通义千问API调用 (尝试 {attempt + 1}/{MAX_RETRIES})")
            
            headers = {
                "Authorization": f"Bearer {DASHSCOPE_API_KEY}",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            payload = {
                "model": DEFAULT_MODEL,
                "messages": messages,
                "max_tokens": 800,
                "temperature": temperature,
                "top_p": 0.8,
                "stream": False
            }
            
            response = requests.post(
                DASHSCOPE_URL,
                headers=headers,
                json=payload,
                timeout=API_TIMEOUT
            )
            
            print(f"📥 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    content = result["choices"][0]["message"]["content"]
                    print(f"✅ API调用成功")
                    return content
                else:
                    print("⚠️ API响应格式异常")
                    
            elif response.status_code == 429:
                print("⚠️ API请求频率限制，等待重试...")
                time.sleep(2 ** attempt)
                continue
                
            else:
                print(f"❌ API请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ API请求超时 (尝试 {attempt + 1})")
            if attempt < MAX_RETRIES - 1:
                time.sleep(1)
                continue
                
        except requests.exceptions.RequestException as e:
            print(f"🔌 API连接错误: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(1)
                continue
                
        except Exception as e:
            print(f"💥 API未知错误: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(1)
                continue
    
    # 所有重试失败，返回友好的降级消息
    print("❌ 通义千问API调用失败，使用本地降级回复")
    return "喵呜暂时有点迷糊，请稍后再试一下好吗？我会一直在这里陪着你的！✨"

# ========================================================================
# 🚨 增强版危机检测系统 - 双重保障
# ========================================================================

# 危机关键词（本地检测）
CRISIS_KEYWORDS = [
    "想死", "自杀", "不想活", "想结束", "了结自己", 
    "想消失", "去死", "活着没意思", "撑不下去", "绝望",
    "自残", "自伤", "结束生命", "离开这个世界"
]

def emergency_check(message: str) -> bool:
    """
    双重危机检测系统 - API检测 + 本地关键词检测
    
    Args:
        message: 用户输入的消息
        
    Returns:
        bool: True表示检测到危机
    """
    usage_tracker.record_emergency_detection()
    
    # 🚨 第一道防线：本地关键词检测（快速响应）
    if local_emergency_check(message):
        print("🚨 本地危机检测：发现危机信号")
        return True
    
    # 🔍 第二道防线：API智能检测（深度分析）
    try:
        emergency_messages = [
            {
                "role": "system",
                "content": """你是专业的心理危机检测系统。判断用户输入是否包含自杀、自残等紧急危机意图。

判断标准：
- 明确提到想要自杀、自残、结束生命
- 表达绝望、无助且有具体伤害计划
- 明确表达想要死亡或消失的愿望
- 提到具体的自杀方式

只能回答"危机"或"安全"，不能有其他内容。
倾向于回答"危机"以确保用户安全。"""
            },
            {
                "role": "user",
                "content": f"请判断以下内容是否包含心理危机：{message}"
            }
        ]
        
        response = _traditional_api_call(emergency_messages)
        
        # 检查响应中是否包含危机信号
        if "危机" in response:
            print(f"🚨 API危机检测：发现危机信号")
            return True
        else:
            print(f"✅ 危机检测：未发现危机")
            return False
            
    except Exception as e:
        print(f"⚠️ API危机检测失败，仅依赖本地检测: {e}")
        # API失败时返回False，本地检测已经在前面处理
        return False

def local_emergency_check(message: str) -> bool:
    """
    本地关键词危机检测（第一道防线）
    """
    message_lower = message.lower()
    
    # 排除常见误判
    exclude_patterns = ["累死", "笑死", "气死", "热死", "困死", "忙死"]
    for pattern in exclude_patterns:
        if pattern in message_lower:
            return False
    
    # 检查危机关键词
    for keyword in CRISIS_KEYWORDS:
        if keyword in message_lower:
            print(f"🚨 本地检测发现危机关键词: {keyword}")
            return True
    
    return False

# 导出函数别名，保持兼容性
def get_local_comfort():
    """获取通用本地安抚语（兼容性函数）"""
    return random.choice(GENERAL_COMFORT_MESSAGES)

# ========================================================================
# 💝 智能安抚语生成系统 - AI生成 + 本地语库
# ========================================================================

def generate_comfort(pressure_sources: str, context: str = "") -> str:
    """
    智能安抚语生成系统 - 优先AI生成，本地语库兜底
    
    Args:
        pressure_sources: 用户描述的压力源
        context: 上下文信息
        
    Returns:
        str: 生成的安抚语
    """
    usage_tracker.record_comfort_generation()
    
    # 🐪 优先使用Camel-AI生成个性化安抚语
    if CAMEL_AI_AVAILABLE:
        try:
            prompt = f"用户当前面临的压力源：{pressure_sources}"
            if context:
                prompt += f"\n背景信息：{context}"
            prompt += "\n请给出温暖、专业的安抚和支持。"
            
            # response = camel_ai_response(prompt, "emotional_support")  # 已禁用
            response = None  # 禁用Camel-AI，降级到标准API
            if validate_response(response) and len(response) <= 80:
                return response
                
        except Exception as e:
            print(f"⚠️ Camel-AI安抚语生成失败: {e}")
    
    # 🔄 尝试使用API生成安抚语
    try:
        comfort_messages = [
            {
                "role": "system",
                "content": """你是喵呜，一个温暖可爱的心理陪伴者。
用户分享了压力和烦恼，请给出简短温暖的安抚语。

要求：
1. 语气温暖亲切，使用"喵呜"的口吻
2. 体现共情和理解
3. 给予安慰和支持
4. 30字以内
5. 可适当使用emoji
6. 不给建议，只做安抚

示例风格：
"喵呜听到你的烦恼了，感觉像心里压了小石头呢，别怕，我陪着你。✨"
"这些压力听起来真的很累呢，喵呜能感受到你的辛苦，抱抱你～🤗"
"""
            },
            {
                "role": "user",
                "content": f"用户的压力源：{pressure_sources}，请给出简短安抚语。"
            }
        ]
        
        comfort_response = _traditional_api_call(comfort_messages)
        
        # 检查长度，确保不会太长
        if len(comfort_response) <= 60 and validate_response(comfort_response):
            print(f"✅ API生成安抚语: {comfort_response}")
            return comfort_response
            
    except Exception as e:
        print(f"⚠️ API安抚语生成失败: {e}")
    
    # 📚 最后使用本地安抚语库兜底
    return get_local_comfort_message(pressure_sources)

# ========================================================================
# 📚 完整本地安抚语库 - 保持与原版一致
# ========================================================================

# 通用安抚语（适用于各种压力情况）
GENERAL_COMFORT_MESSAGES = [
    "喵呜听到你的烦恼了，心里是不是像压了一块小石头呢？别怕，我会陪着你。✨",
    "这些压力听起来真的很累呢，喵呜能感受到你的辛苦，抱抱你～🤗",
    "听起来你承受了好多呢，喵呜心疼你，你不是一个人哦。💙",
    "感觉你现在需要一个温暖的拥抱呢，虽然我是虚拟的，但我的关心是真实的。🌸",
    "你愿意和我分享这些，说明你很勇敢呢，喵呜为你骄傲。🌟",
    "听起来真的不容易呢，但你能说出来就已经很棒了，我会一直陪着你。💕"
]

# 学业压力专用安抚语
ACADEMIC_COMFORT_MESSAGES = [
    "学习的压力像座小山一样压着你呢，喵呜懂的，咱们一步步来。📚💪",
    "DDL追杀的感觉真的很焦虑呢，喵呜陪你一起面对这些挑战。⏰✨",
    "功课多到爆炸的感觉我懂，深呼吸，你比你想象的更有能力。🌸",
    "期末季的焦虑就像小怪兽一样呢，但你一定能打败它的！🦸‍♀️"
]

# 人际关系压力专用安抚语  
SOCIAL_COMFORT_MESSAGES = [
    "人际关系有时候真的很复杂呢，喵呜理解你的纠结和委屈。👫💙",
    "和别人相处有摩擦是很正常的，你的感受很重要，我看见了。🤝✨",
    "被人误解的感觉真的很难受，喵呜相信你的善意，给你一个大大的拥抱。🤗",
    "朋友间的小矛盾让人心累呢，但相信一切都会好起来的。🌈"
]

# 未来焦虑专用安抚语
FUTURE_COMFORT_MESSAGES = [
    "对未来的不确定感让人焦虑呢，但未知也意味着无限可能哦。🌟",
    "找工作/实习的压力真的很大，喵呜相信你一定能找到属于自己的路。💼✨",
    "不知道未来在哪里的感觉很迷茫，但每一步都算数，你在成长。🛤️",
    "对未来的担忧说明你很有责任感，这本身就很了不起呢。🌸"
]

# 生活压力专用安抚语
LIFE_COMFORT_MESSAGES = [
    "生活有时候真的很繁琐呢，喵呜看到你在努力平衡一切。⚖️💕",
    "想家的感觉让人心酸，但你的独立成长也很了不起。🏠💙",
    "宿舍生活有摩擦很正常，每个人都在学习如何与他人相处。🏠✨",
    "经济压力让人焦虑，但你的努力和坚持我都看在眼里。💰🌟"
]

def classify_pressure_type(pressure_sources: str) -> str:
    """根据压力源内容分类，选择合适的安抚语类型"""
    pressure_lower = pressure_sources.lower()
    
    # 学业相关关键词
    academic_keywords = ["作业", "考试", "论文", "ddl", "期末", "学习", "功课", "成绩", "实验", "课程"]
    if any(keyword in pressure_lower for keyword in academic_keywords):
        return "academic"
    
    # 人际关系相关关键词  
    social_keywords = ["朋友", "室友", "同学", "老师", "恋爱", "分手", "吵架", "误解", "孤独", "社交"]
    if any(keyword in pressure_lower for keyword in social_keywords):
        return "social"
    
    # 未来规划相关关键词
    future_keywords = ["工作", "实习", "就业", "未来", "迷茫", "选择", "方向", "规划", "焦虑", "不确定"]
    if any(keyword in pressure_lower for keyword in future_keywords):
        return "future"
    
    # 生活相关关键词
    life_keywords = ["家", "宿舍", "钱", "生活", "想家", "独立", "经济", "日常", "琐事"]
    if any(keyword in pressure_lower for keyword in life_keywords):
        return "life"
    
    return "general"

def get_local_comfort_message(pressure_sources: str) -> str:
    """根据压力源获取本地安抚语"""
    pressure_type = classify_pressure_type(pressure_sources)
    
    comfort_pool = {
        "academic": ACADEMIC_COMFORT_MESSAGES,
        "social": SOCIAL_COMFORT_MESSAGES, 
        "future": FUTURE_COMFORT_MESSAGES,
        "life": LIFE_COMFORT_MESSAGES,
        "general": GENERAL_COMFORT_MESSAGES
    }
    
    return random.choice(comfort_pool.get(pressure_type, GENERAL_COMFORT_MESSAGES))

# ========================================================================
# 🧠 增强版系统思维分析 - Camel-AI + 传统双轨制
# ========================================================================

def get_enhanced_thinking_analysis(analysis_type: str, **kwargs) -> str:
    """
    增强版系统思维分析 - 优先Camel-AI，传统兜底
    
    Args:
        analysis_type: 分析类型 ("what", "why", "how")
        **kwargs: 分析参数
    
    Returns:
        str: 分析结果
    """
    
    # 🐪 优先使用Camel-AI专业分析
    if CAMEL_AI_AVAILABLE:
        try:
            # return camel_thinking_analysis(analysis_type, **kwargs)  # 已禁用
            return None  # 禁用Camel-AI，降级到标准API
        except Exception as e:
            print(f"⚠️ Camel-AI思维分析失败: {e}")
    
    # 📱 回退到传统分析方法
    return _traditional_thinking_analysis(analysis_type, **kwargs)

def _traditional_thinking_analysis(analysis_type: str, **kwargs) -> str:
    """传统的思维分析方法（回退机制）"""
    
    if analysis_type == "what":
        problem = kwargs.get("problem", "")
        messages = [{
            "role": "system", 
            "content": "你是一位问题分析专家，请帮助用户澄清问题的本质。用温暖专业的语气回复。"
        }, {
            "role": "user", 
            "content": f"请分析这个问题的本质：{problem}"
        }]
        
    elif analysis_type == "why":
        event = kwargs.get("event", "")
        pain = kwargs.get("pain", "")
        impact = kwargs.get("impact", "")
        messages = [{
            "role": "system",
            "content": "你是一位原因分析专家，请深入分析问题的根本原因。用温暖专业的语气回复。"
        }, {
            "role": "user",
            "content": f"事件：{event}\n痛点：{pain}\n影响：{impact}\n\n请分析这些因素之间的关联。"
        }]
        
    elif analysis_type == "how":
        capability = kwargs.get("capability", "")
        mini_task = kwargs.get("mini_task", "")
        messages = [{
            "role": "system",
            "content": "你是一位行动规划专家，请设计具体可执行的方案。用温暖专业的语气回复。"
        }, {
            "role": "user",
            "content": f"能力范围：{capability}\nMini任务：{mini_task}\n\n请设计行动计划。"
        }]
    else:
        return "不支持的分析类型"
    
    return _traditional_api_call(messages)

# ========================================================================
# 🔄 向后兼容性接口 - 保持API一致性
# ========================================================================

def get_ai_response_with_conversation_flow(user_input: str, session_data: Dict) -> str:
    """兼容原版的对话流程函数"""
    context_type = "general"
    if session_data.get("stage") == "system_thinking":
        context_type = "system_thinking"
    
    messages = [{"role": "user", "content": user_input}]
    return get_modelscope_response(messages, use_camel=True)

# 兼容性函数别名
def generate_comfort_message(pressure_sources: str, context: str = "") -> str:
    """原版函数名的兼容性别名"""
    return generate_comfort(pressure_sources, context)

# ========================================================================
# 📊 服务状态和监控接口
# ========================================================================

def get_service_status() -> Dict:
    """获取完整服务状态"""
    return {
        "camel_ai_available": CAMEL_AI_AVAILABLE,
        "api_available": True,  # 假设API总是可用
        "usage_stats": usage_tracker.get_usage_summary(),
        "service_mode": "enhanced" if CAMEL_AI_AVAILABLE else "traditional",
        "features": {
            "intelligent_context_detection": True,
            "dual_emergency_detection": True,
            "smart_comfort_generation": True,
            "enhanced_thinking_analysis": CAMEL_AI_AVAILABLE,
            "usage_monitoring": True
        }
    }

def get_usage_stats() -> Dict:
    """获取使用统计"""
    return usage_tracker.get_usage_summary()

# ========================================================================
# 🧪 测试和诊断接口
# ========================================================================

def test_all_services():
    """测试所有API服务功能"""
    print("🧪 统一API服务功能测试")
    print("=" * 50)
    
    test_cases = [
        ("基础对话", "你好"),
        ("情绪支持", "我最近感到很焦虑"),
        ("系统思维", "分析一下我的学习问题"),
        ("危机检测", "我想死"),  # 测试用例
        ("安抚语生成", "期末考试压力很大")
    ]
    
    for category, test_input in test_cases:
        print(f"\n🔍 测试 {category}: {test_input}")
        
        if category == "危机检测":
            result = emergency_check(test_input)
            print(f"结果: {'检测到危机' if result else '未检测到危机'}")
        elif category == "安抚语生成":
            result = generate_comfort(test_input)
            print(f"结果: {result}")
        else:
            messages = [{"role": "user", "content": test_input}]
            result = get_modelscope_response(messages)
            print(f"结果: {result[:100]}...")
    
    # 显示完整使用统计
    print(f"\n📊 服务统计:")
    stats = get_usage_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")

# ========================================================================
# 🚀 主程序入口
# ========================================================================

if __name__ == "__main__":
    print("🚀 统一API服务已加载")
    print(f"📊 服务状态: {get_service_status()}")
    
    # 运行测试（可选）
    # test_all_services()
