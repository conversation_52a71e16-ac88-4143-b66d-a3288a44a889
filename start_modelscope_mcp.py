#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
魔搭平台专用MCP启用启动脚本
确保在ModelScope环境中正确启用MCP Web搜索功能
"""

import os
import sys
import logging
from pathlib import Path

def setup_modelscope_mcp():
    """
    为魔搭平台设置MCP环境
    """
    print("🚀 启动心理小助手喵呜 - 魔搭MCP增强版")
    print("=" * 50)
    print()
    
    # 📍 确保工作目录正确
    script_dir = Path(__file__).parent.absolute()
    os.chdir(script_dir)
    print(f"📂 工作目录: {script_dir}")
    
    # 🌐 设置MCP环境变量
    os.environ['MCP_ENABLED'] = 'true'
    print("✅ MCP Web搜索功能已启用")
    
    # 🌟 测试MCP Server可用性
    try:
        # 确保当前目录在Python路径中
        sys.path.insert(0, str(script_dir))
        
        from mcp_web_search_server import web_search
        test_result = web_search("测试MCP", 1)
        if test_result.get("success"):
            print("🎉 MCP Server预检测成功！")
        else:
            print(f"⚠️ MCP Server预检测失败: {test_result.get('error')}")
    except Exception as e:
        print(f"⚠️ MCP Server预检测异常: {e}")
        print("   系统将自动降级到静态内容模式")
    
    # 🔧 魔搭平台环境检查
    if 'MODELSCOPE_STUDIO' in os.environ:
        print("🏢 检测到魔搭Studio环境")
        # 魔搭Studio特定配置
        os.environ['SERVER_HOST'] = '0.0.0.0'
        os.environ['SERVER_PORT'] = '7860'
        os.environ['SERVER_SHARE'] = 'false'
    else:
        print("💻 本地环境模式")
    
    # 🎯 启动配置摘要
    print("\n🎯 启动配置:")
    print(f"   - MCP Web搜索: {'✅ 启用' if os.getenv('MCP_ENABLED') == 'true' else '❌ 禁用'}")
    print(f"   - 主机: {os.getenv('SERVER_HOST', '127.0.0.1')}")
    print(f"   - 端口: {os.getenv('SERVER_PORT', '7860')}")
    print(f"   - 环境: {'魔搭Studio' if 'MODELSCOPE_STUDIO' in os.environ else '本地'}")
    print()
    
    # 🚨 重要提醒
    print("💡 MCP功能说明:")
    print("   - 真实网络搜索心理健康内容")
    print("   - 频率限制: 每小时最多2次调用")
    print("   - 自动降级: 失败时使用高质量静态内容")
    print("   - 完全安全: 不影响核心功能")
    print()
    
    return True

if __name__ == "__main__":
    try:
        # 设置MCP环境
        setup_modelscope_mcp()
        
        # 导入并启动主应用
        print("🐱 启动喵呜心理助手...")
        import app
        app.launch_app()
        
    except ImportError as e:
        print(f"❌ 模块导入错误: {e}")
        print("💡 请确保在正确的目录中运行此脚本")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
