"""
喵呜社区管理器 - MVP版本
===========================

功能：
1. 社区帖子发布和浏览
2. 基于MBTI的简单匹配推荐
3. 用户准入权限控制
4. 匿名保护机制

设计原则：
- 遵循MVP原则，功能简洁实用
- 完整对话流程前置检查
- 安全的匿名机制
- 易于扩展的架构
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import random
import json
from enum import Enum
import os

# 导入API服务
from api_service_unified import get_modelscope_response

# 导入外部工具（Web Search）
# web_search 是一个内置工具，直接调用即可

class PostType(Enum):
    """帖子类型枚举"""
    HELP_REQUEST = "help_request"  # 求助帖
    EXPERIENCE_SHARE = "experience_share"  # 经验分享
    MOOD_TALK = "mood_talk"  # 心情倾诉

class PostStatus(Enum):
    """帖子状态枚举"""
    ACTIVE = "active"  # 活跃
    RESOLVED = "resolved"  # 已解决
    ARCHIVED = "archived"  # 已归档

class CommunityManager:
    """
    喵呜社区管理器
    处理社区相关的所有功能
    """
    
    def __init__(self, database_manager):
        """
        初始化社区管理器 - MVP资源优化版
        
        Args:
            database_manager: 数据库管理器实例
        """
        self.db_manager = database_manager
        self.setup_community_tables()
        
        # 🚨 MVP资源控制
        self.max_posts_per_page = 5  # 限制每页帖子数量
        self.cache_timeout = 300  # 5分钟缓存超时
        self.mcp_call_limit = 5  # 每小时MCP调用限制 🚨 更改为每小时5次
        self.last_mcp_calls = []  # 记录MCP调用时间
        
        # MBTI兼容性规则（简化版）
        self.mbti_compatibility_rules = {
            'same': 2,      # 相同维度 +2分
            'complement': 1, # 互补维度 +1分
            'conflict': 0    # 冲突维度 +0分
        }
        
        # 匿名昵称生成模板
        self.anonymous_templates = [
            "温暖的{mbti}小伙伴",
            "善良的{mbti}同学", 
            "可爱的{mbti}朋友",
            "阳光的{mbti}宝贝",
            "治愈系{mbti}小天使"
        ]
    
    def setup_community_tables(self):
        """设置社区相关数据库表"""
        try:
            with self.db_manager.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 社区帖子表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS community_posts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        title TEXT NOT NULL,
                        content TEXT NOT NULL,
                        post_type TEXT NOT NULL,
                        status TEXT DEFAULT 'active',
                        anonymous_name TEXT,
                        user_mbti TEXT,
                        tags TEXT,  -- JSON格式存储标签
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        view_count INTEGER DEFAULT 0,
                        help_count INTEGER DEFAULT 0,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)
                
                # 社区回复表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS community_replies (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        post_id INTEGER NOT NULL,
                        user_id INTEGER NOT NULL,
                        content TEXT NOT NULL,
                        anonymous_name TEXT,
                        user_mbti TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_helpful INTEGER DEFAULT 0,  -- 是否被标记为有帮助
                        FOREIGN KEY (post_id) REFERENCES community_posts (id),
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)
                
                conn.commit()
                print("✅ 社区数据库表创建成功")
            
        except Exception as e:
            print(f"❌ 社区数据库表创建失败: {e}")
    
    def check_community_access(self, user_id: int) -> Tuple[bool, str]:
        """
        检查用户是否有权限访问社区功能
        
        Args:
            user_id: 用户ID
            
        Returns:
            Tuple[bool, str]: (是否有权限, 提示信息)
        """
        try:
            user_info = self.db_manager.get_user(user_id)
            if not user_info:
                return False, "用户不存在"
            
            current_state = user_info.get('current_state', 'initial_greeting')
            
            # 检查是否完成了完整对话流程
            allowed_states = ['free_chat', 'jungian_test_completed', 'emotion_how']
            
            if current_state in allowed_states:
                return True, "欢迎来到喵呜社区！"
            else:
                return False, """
🚫 **社区功能暂未解锁** 

亲爱的同学，喵呜社区是为了给大家提供更有针对性的互助体验而设计的！

**为了确保社区互动的质量和效果，需要先完成以下步骤：**

1. 🗣️ **完整对话体验** - 与喵呜进行一次完整的对话流程
2. 🧠 **性格测试** - 完成荣格八维测试（可选但推荐）
3. 💝 **情绪梳理** - 体验情绪疏导功能

**为什么需要这样做？** 🤔
- 📝 让喵呜更好地了解你，提供个性化的社区推荐
- 🎯 确保你在社区中能给出和接受更有针对性的帮助
- 🛡️ 营造一个理解彼此、相互支持的温暖社区氛围

请先在"💬 主对话"中与喵呜聊聊，完成流程后就能解锁社区功能啦！✨
                """.strip()
            
        except Exception as e:
            return False, f"权限检查失败: {e}"
    
    def generate_anonymous_name(self, user_mbti: str) -> str:
        """
        生成匿名昵称
        
        Args:
            user_mbti: 用户的MBTI类型
            
        Returns:
            str: 匿名昵称
        """
        if not user_mbti or user_mbti == "未知":
            user_mbti = "暖心"
        
        template = random.choice(self.anonymous_templates)
        return template.format(mbti=user_mbti)
    
    def publish_post(self, user_id: int, title: str, content: str, 
                    post_type: str = "help_request", tags: List[str] = None) -> Dict[str, Any]:
        """
        发布社区帖子
        
        Args:
            user_id: 用户ID
            title: 帖子标题
            content: 帖子内容
            post_type: 帖子类型
            tags: 标签列表
            
        Returns:
            Dict[str, Any]: 发布结果
        """
        try:
            # 检查权限
            has_access, message = self.check_community_access(user_id)
            if not has_access:
                return {"success": False, "message": message}
            
            # 获取用户MBTI信息
            user_profile = self.db_manager.get_user_profile(user_id)
            user_mbti = user_profile.get('jungian_type', '未知')
            
            # 生成匿名昵称
            anonymous_name = self.generate_anonymous_name(user_mbti)
            
            # 准备数据
            tags_json = json.dumps(tags or [], ensure_ascii=False)
            
            with self.db_manager.get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO community_posts 
                    (user_id, title, content, post_type, anonymous_name, user_mbti, tags)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (user_id, title, content, post_type, anonymous_name, user_mbti, tags_json))
                
                post_id = cursor.lastrowid
                conn.commit()
                
                return {
                    "success": True,
                    "message": f"帖子发布成功！以 '{anonymous_name}' 的身份发布",
                    "post_id": post_id
                }
            
        except Exception as e:
            return {"success": False, "message": f"发布失败: {e}"}

    def get_psychology_content_recommendations(self, user_id, limit=5):
        """
        @JSDoc
        @description 获取积极心理学文章或互动视频推荐（使用MCP web_search）
        @param {int} user_id - 用户ID (目前未使用，未来可用于个性化)
        @param {int} limit - 推荐数量限制
        @returns {str} 格式化的Markdown内容展示积极心理学推荐
        """
        print(f"🔍 正在为用户 {user_id} 通过MCP web_search搜索知乎高质量积极心理学内容推荐...") # 🚨 修改日志
        
        # 🚨 MVP优化：资源控制的MCP调用
        search_results = None
        
        # 🌐 检查是否启用MCP功能
        import os
        mcp_enabled = os.getenv('MCP_ENABLED', 'false').lower() == 'true'
        
        if mcp_enabled and self._can_make_mcp_call():
            try:
                # 🚨 关键修复：更具体地 targeting 知乎和高质量内容
                search_query = "知乎 高质量 积极心理学 精华 文章推荐 大学生 心理健康 情绪管理"
                print(f"🔍 尝试MCP web_search调用: {search_query}")
                
                # 🚨 启用真实MCP调用 - 使用新的MCP Server
                try:
                    # 🌐 MCP工具调用 - 使用符合魔搭规范的MCP Server
                    search_results = None
                    
                    try:
                        # 导入新的MCP Server
                        from mcp_web_search_server import web_search
                        
                        # 调用MCP搜索
                        mcp_response = web_search(search_query, max_results=1) # 🚨 关键修改：只获取一篇文章
                        
                        if mcp_response.get("success"):
                            search_results = mcp_response.get("results", [])
                            print(f"✅ 新MCP Server调用成功，获得{len(search_results)}个结果")
                        else:
                            print(f"⚠️ MCP Server返回错误: {mcp_response.get('error', 'Unknown error')}")
                        
                    except ImportError as e:
                        print(f"ℹ️ MCP Server模块未找到: {e}，使用高质量静态内容")
                    except Exception as mcp_error:
                        print(f"⚠️ MCP Server调用失败: {mcp_error}")
                        
                    self._record_mcp_call()
                    if not search_results:
                        print("💡 降级到静态内容，依然为用户提供优质体验")
                        
                except Exception as mcp_error:
                    print(f"⚠️ MCP调用失败: {mcp_error}")
                    
            except Exception as e:
                print(f"⚠️ MCP调用过程失败，使用静态内容: {e}")
        else:
            if not mcp_enabled:
                print("🔧 MCP功能未启用，使用高质量静态内容")
            else:
                print("⚠️ MCP调用频率限制，使用静态内容")
        
        # 如果MCP搜索成功，直接展示真实结果
        if search_results:
            try:
                # 🌟 获取并格式化MCP搜索的真实结果
                # 由于max_results=1，这里预期只有一个item
                if search_results:
                    item = search_results[0]
                    
                    # 🚨 关键修改：调用大模型进行精华内容概括
                    from api_service_unified import get_modelscope_response
                    summarization_prompt = f"""请你作为心理学专家，将以下知乎文章内容概括为一篇约200-300字的精华摘要。要求语言温暖、专业，突出文章的核心观点和对大学生心理健康的启发。\n\n文章标题：{item['title']}\n文章内容：{item['content']}"""
                    
                    summarized_content = get_modelscope_response([{"role": "user", "content": summarization_prompt}], temperature=0.7, use_camel=False)
                    
                    formatted_recommendations = f"""### 🌟 **{item['title']}**

{summarized_content}

👨‍⚕️ **来源**: {item['author']} | 📅 **发布**: {item['publish_date']}  
⭐ **相关度**: {item['relevance_score']} | 📄 **类型**: {item['content_type']}

---


"""
                else:
                    formatted_recommendations = "喵呜暂时没有找到新的知乎文章推荐，请稍后再试或点击刷新。"
                
                return f"""## 💡 喵呜精选：知乎高质量积极心理学推荐

{formatted_recommendations}

**🌐 MCP实时搜索成功！** 以上内容通过MCP Web搜索服务实时获取  
*🔄 点击刷新获取更多优质内容*
"""
            except Exception as e:
                print(f"⚠️ AI处理搜索结果失败: {e}")
                # 降级到静态内容
            
        # 降级到精心设计的静态样本展示（用于演示MCP功能概念）
        return """## 💡 喵呜精选：积极心理学推荐

### 🌟 **积极心理学基础：培养内在力量**
积极心理学是研究人类幸福和积极品质的科学。通过了解自己的性格优势，我们可以更好地发挥潜能，建立更积极的生活态度。

**推荐理由：** 帮助大学生从科学角度理解幸福，建立积极心态

---

### 🧠 **情绪调节的实用技巧**  
学会识别、理解和管理自己的情绪是心理健康的重要技能。正确的情绪调节方法可以帮助我们在面对压力时保持平衡。

**推荐理由：** 提供具体可操作的情绪管理方法，适合日常练习

---

### 🌸 **正念练习入门指南**
正念练习可以帮助我们更好地觉察当下，减少焦虑和压力。简单的呼吸练习和冥想技巧就能带来明显的身心放松效果。

**推荐理由：** 简单易学的减压方法，特别适合学习压力大的大学生

---
*📍 以上内容展示MCP web_search功能效果 | 🔄 点击刷新获取实时网络内容*
"""

    def get_mental_health_game_recommendations(self, user_id, limit=3):
        """
        @JSDoc
        @description 获取心理健康小游戏推荐（使用MCP web_search）
        @param {int} user_id - 用户ID (目前未使用，未来可用于个性化)
        @param {int} limit - 推荐数量限制
        @returns {str} 格式化的Markdown内容展示心理健康小游戏推荐
        """
        print(f"🎮 正在为用户 {user_id} 通过MCP web_search搜索心理健康小游戏推荐...")
        
        # 🚨 MVP优化：资源控制的MCP调用
        search_results = None
        
        # 🚨 真实MCP调用逻辑 - 渐进式启用
        mcp_search_success = False
        
        # 🌐 检查MCP功能启用状态
        import os
        mcp_enabled = os.getenv('MCP_ENABLED', 'false').lower() == 'true'
        
        if mcp_enabled:
            try:
                search_query = "心理健康小游戏 大学生 放松解压 文字游戏"
                print(f"🔍 尝试MCP web_search调用: {search_query}")
                
                # 🚨 启用真实MCP调用 - 使用新的MCP Server
                try:
                    # 🌐 MCP工具调用 - 使用符合魔搭规范的MCP Server
                    search_results = None
                    mcp_search_success = False
                    
                    try:
                        # 导入新的MCP Server
                        from mcp_web_search_server import web_search
                        
                        # 调用MCP搜索
                        mcp_response = web_search(search_query, max_results=3)
                        
                        if mcp_response.get("success"):
                            search_results = mcp_response.get("results", [])
                            mcp_search_success = True
                            print(f"✅ 新MCP Server调用成功，获得{len(search_results)}个结果")
                        else:
                            print(f"⚠️ MCP Server返回错误: {mcp_response.get('error', 'Unknown error')}")
                        
                    except ImportError as e:
                        print(f"ℹ️ MCP Server模块未找到: {e}，使用高质量静态内容")
                    except Exception as mcp_error:
                        print(f"⚠️ MCP Server调用失败: {mcp_error}")
                        
                    self._record_mcp_call()
                    if not search_results:
                        print("💡 降级到静态内容，依然为用户提供优质体验")
                        
                except Exception as mcp_error:
                    print(f"⚠️ MCP调用失败: {mcp_error}")
                    
            except Exception as e:
                print(f"⚠️ MCP web_search调用失败: {e}")
        else:
            print("🔧 MCP功能未启用，使用高质量静态内容")
            
        # 如果MCP调用成功但没有返回有效结果，则降级
        
        # 如果MCP搜索成功，直接展示真实结果
        if search_results:
            try:
                # 🌟 直接格式化MCP搜索的真实结果
                formatted_games = ""
                for i, item in enumerate(search_results, 1):
                    formatted_games += f"""### {i}. **{item['title']}**
{item['content']}

🔗 **阅读详情**: [{item['url'].replace('https://', '').replace('http://', '')}]({item['url']})  
👨‍⚕️ **来源**: {item['author']} | 📅 **发布**: {item['publish_date']}  
⭐ **相关度**: {item['relevance_score']} | 📄 **类型**: {item['content_type']}

---

"""
                
                return f"""## 🎮 喵呜乐园：心理健康小游戏

{formatted_games}

**🌐 MCP实时搜索成功！** 以上内容通过MCP Web搜索服务实时获取  
*🔄 点击刷新获取更多精彩小游戏*
"""
            except Exception as e:
                print(f"⚠️ AI处理游戏搜索结果失败: {e}")
                # 降级到静态内容
        
        # 降级到精心设计的静态样本展示（用于演示MCP功能概念）
        return """## 🎮 喵呜乐园：心理健康小游戏

### 🌟 **情绪天气预报**
**玩法简介：** 每天早上花2分钟，把自己当天的情绪想象成天气。比如"今天内心是多云转晴，有轻微的焦虑小雨，但下午会有自信的阳光"。写下这个天气预报，晚上再看看预报准不准。

**推荐理由：** 有趣的情绪识别方式，帮助大学生更好地觉察和表达内心状态

---

### 🧠 **三分钟感恩接龙**
**玩法简介：** 设置3分钟计时器，快速写下今天感恩的事情，每写一个就要接龙写下"因为这个，我还感恩..."。看看3分钟能写出多少个感恩链条。可以一个人玩，也可以和室友一起比赛。

**推荐理由：** 快速培养积极心态，增强幸福感，还能增进室友关系

---

### 🎯 **压力气球游戏**
**玩法简介：** 把今天的压力写在纸上，然后想象把这些压力装进气球。写下"我把XX压力装进红色气球，轻轻放飞"，想象气球飞走的画面，深呼吸三次。可以给不同压力选择不同颜色的气球。

**推荐理由：** 可视化的压力释放方法，简单有效的心理疏导技巧

---
*📍 以上游戏展示MCP web_search功能效果 | 🔄 点击刷新获取更多放松小游戏*
"""
    
    def get_community_posts(self, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取社区帖子列表
        
        Args:
            limit: 获取数量限制
            offset: 偏移量
            
        Returns:
            List[Dict[str, Any]]: 帖子列表
        """
        try:
            with self.db_manager.get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT id, title, content, post_type, status, anonymous_name, 
                           user_mbti, tags, created_at, view_count, help_count
                    FROM community_posts 
                    WHERE status = 'active'
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?
                """, (limit, offset))
                
                posts = []
                for row in cursor.fetchall():
                    post = dict(row)
                    post['tags'] = json.loads(post['tags'] or '[]')
                    post['time_ago'] = self._format_time_ago(post['created_at'])
                    posts.append(post)
                
                return posts
            
        except Exception as e:
            print(f"获取帖子列表失败: {e}")
            return []
    
    def get_user_posts(self, user_id: int) -> List[Dict[str, Any]]:
        """
        获取用户发布的帖子
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[Dict[str, Any]]: 用户帖子列表
        """
        try:
            with self.db_manager.get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT id, title, content, post_type, status, anonymous_name,
                           created_at, view_count, help_count
                    FROM community_posts 
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                """, (user_id,))
                
                posts = []
                for row in cursor.fetchall():
                    post = dict(row)
                    post['time_ago'] = self._format_time_ago(post['created_at'])
                    posts.append(post)
                
                return posts
            
        except Exception as e:
            print(f"获取用户帖子失败: {e}")
            return []
    
    def calculate_mbti_compatibility(self, user_mbti: str, target_mbti: str) -> int:
        """
        计算MBTI兼容性得分
        
        Args:
            user_mbti: 用户MBTI类型
            target_mbti: 目标MBTI类型
            
        Returns:
            int: 兼容性得分 (0-8分)
        """
        if not user_mbti or not target_mbti or len(user_mbti) != 4 or len(target_mbti) != 4:
            return 0
        
        score = 0
        
        for i in range(4):
            user_char = user_mbti[i].upper()
            target_char = target_mbti[i].upper()
            
            if user_char == target_char:
                score += self.mbti_compatibility_rules['same']
            elif self._is_complementary(user_char, target_char, i):
                score += self.mbti_compatibility_rules['complement']
            else:
                score += self.mbti_compatibility_rules['conflict']
        
        return score
    
    def _is_complementary(self, char1: str, char2: str, dimension: int) -> bool:
        """
        判断两个MBTI字符是否互补
        
        Args:
            char1: 第一个字符
            char2: 第二个字符
            dimension: 维度索引 (0:E/I, 1:S/N, 2:T/F, 3:J/P)
            
        Returns:
            bool: 是否互补
        """
        complementary_pairs = {
            0: [('E', 'I'), ('I', 'E')],  # 外向-内向
            1: [('S', 'N'), ('N', 'S')],  # 实感-直觉
            2: [('T', 'F'), ('F', 'T')],  # 思考-情感
            3: [('J', 'P'), ('P', 'J')]   # 判断-感知
        }
        
        return (char1, char2) in complementary_pairs.get(dimension, [])
    
    def get_recommended_posts_for_user(self, user_id: int, limit: int = 5) -> List[Dict[str, Any]]:
        """
        为用户推荐相关帖子（基于MBTI兼容性）
        
        Args:
            user_id: 用户ID
            limit: 推荐数量
            
        Returns:
            List[Dict[str, Any]]: 推荐帖子列表
        """
        try:
            # 获取用户MBTI
            user_profile = self.db_manager.get_user_profile(user_id)
            user_mbti = user_profile.get('jungian_type', '')
            
            if not user_mbti:
                # 如果没有MBTI信息，返回最新帖子
                return self.get_community_posts(limit=limit)
            
            # 获取所有活跃帖子
            all_posts = self.get_community_posts(limit=50)  # 获取更多帖子进行筛选
            
            # 计算兼容性得分并排序
            scored_posts = []
            for post in all_posts:
                post_mbti = post.get('user_mbti', '')
                compatibility_score = self.calculate_mbti_compatibility(user_mbti, post_mbti)
                scored_posts.append((post, compatibility_score))
            
            # 按兼容性得分排序（同分按时间排序）
            scored_posts.sort(key=lambda x: (x[1], x[0]['created_at']), reverse=True)
            
            # 返回前N个推荐帖子
            return [post for post, score in scored_posts[:limit]]
            
        except Exception as e:
            print(f"获取推荐帖子失败: {e}")
            return self.get_community_posts(limit=limit)
    
    def get_matching_recommendations(self, user_id: int, limit: int = 5) -> str:
        """
        为用户生成格式化的MBTI匹配推荐
        
        Args:
            user_id: 用户ID
            limit: 推荐数量
            
        Returns:
            str: 格式化的Markdown内容展示匹配推荐
        """
        try:
            user_profile = self.db_manager.get_user_profile(user_id)
            user_mbti = user_profile.get('jungian_type', '')
            
            if not user_mbti:
                return """## 🤝 广场匹配推荐

喵~ 要进行MBTI匹配推荐，请先在"主对话"或"荣格八维测试"选项卡中完成MBTI测试哦！✨
"""
            
            recommended_posts = self.get_recommended_posts_for_user(user_id, limit)
            
            if not recommended_posts:
                return f"""## 🤝 广场匹配推荐

喵~ 暂时没有找到适合 {user_mbti} 的推荐帖子呢。你可以多去社区广场看看，或者发布你的求助！✨
"""
            
            formatted_recommendations = f"""## 🤝 广场匹配推荐 (为你量身定制！)

根据你的MBTI类型 **{user_mbti}**，喵呜为你找到了这些可能感兴趣的帖子：

"""
            for i, post in enumerate(recommended_posts, 1):
                formatted_recommendations += f"""### {i}. **{post['title']}**
**类型**: {post.get('user_mbti', '未知')} | **发帖人**: {post['anonymous_name']}
**内容**: {post['content'][:100]}...
**发布时间**: {post['time_ago']}
---

"""
            
            return formatted_recommendations
        except Exception as e:
            print(f"生成匹配推荐失败: {e}")
            return f"## ⚠️ 匹配推荐获取失败\n\n抱歉，获取匹配推荐时发生错误：{e}\n\n请稍后重试。"

    def get_my_help_requests(self, user_id: int, limit: int = 5) -> str:
        """
        获取用户自己发布的求助帖子并格式化
        
        Args:
            user_id: 用户ID
            limit: 帖子数量限制
            
        Returns:
            str: 格式化的Markdown内容展示用户求助帖
        """
        try:
            user_posts = self.get_user_posts(user_id)
            help_requests = [p for p in user_posts if p.get('post_type') == PostType.HELP_REQUEST.value][:limit]
            
            if not help_requests:
                return """## 📝 我的求助

喵~ 你还没有发布过求助帖子哦！快去"发布求助"，让大家帮你解决困扰吧！✨
"""
            
            formatted_requests = """## 📝 我的求助

"""
            for i, post in enumerate(help_requests, 1):
                status_emoji = "✅" if post.get('status') == 'resolved' else "⏳"
                formatted_requests += f"""### {i}. {status_emoji} **{post['title']}**
**类型**: {post.get('post_type', '求助')} | **状态**: {post.get('status', '活跃')}
**内容**: {post['content'][:100]}...
**发布时间**: {post['time_ago']}
---

"""
            return formatted_requests
        except Exception as e:
            print(f"获取用户求助帖子失败: {e}")
            return f"""## ⚠️ 我的求助获取失败

抱歉，获取你的求助帖子时发生错误：{e}

请稍后重试。"""

    def increment_post_view(self, post_id: int):
        """
        增加帖子浏览次数
        
        Args:
            post_id: 帖子ID
        """
        try:
            with self.db_manager.get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE community_posts 
                    SET view_count = view_count + 1
                    WHERE id = ?
                """, (post_id,))
                
                conn.commit()
            
        except Exception as e:
            print(f"更新浏览次数失败: {e}")
    
    def _format_time_ago(self, timestamp: str) -> str:
        """
        格式化时间为相对时间
        
        Args:
            timestamp: 时间戳字符串
            
        Returns:
            str: 相对时间描述
        """
        try:
            created_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            now = datetime.now()
            delta = now - created_time
            
            if delta.days > 0:
                return f"{delta.days}天前"
            elif delta.seconds > 3600:
                hours = delta.seconds // 3600
                return f"{hours}小时前"
            elif delta.seconds > 60:
                minutes = delta.seconds // 60
                return f"{minutes}分钟前"
            else:
                return "刚刚"
                
        except Exception as e:
            print(f"⚠️ 格式化时间失败: {e}")
            return "未知时间"
    
    def _can_make_mcp_call(self) -> bool:
        """
        检查是否可以进行MCP调用（资源控制）- 渐进式启用版
        
        Returns:
            bool: 是否允许MCP调用
        """
        # 清理过期的调用记录
        current_time = datetime.now()
        self.last_mcp_calls = [
            call_time for call_time in self.last_mcp_calls 
            if (current_time - call_time).total_seconds() < 3600  # 保留1小时内的调用记录
        ]
        
        # 🚨 渐进式MCP启用策略
        # 1. 严格的调用频率限制（每小时最多5次）🚨 更新为每小时5次
        if len(self.last_mcp_calls) >= 5:
            print("⚠️ MCP调用频率限制，使用静态内容")
            return False
            
        # 2. 检查MCP工具可用性
        try:
            # 尝试导入MCP相关模块（渐进式检测）
            import sys
            import os
            
            # 检查环境变量或配置文件
            mcp_enabled = os.getenv('MCP_ENABLED', 'false').lower() == 'true'
            if not mcp_enabled:
                print("🔧 MCP功能未启用，使用静态内容 (设置环境变量 MCP_ENABLED=true 启用)")
                return False
            
            # 检查MCP工具是否真正可用
            # 这里可以添加更具体的MCP可用性检测
            print("✅ MCP调用条件满足，准备尝试真实调用")
            return True
            
        except Exception as e:
            print(f"⚠️ MCP可用性检测失败: {e}")
            return False
    
    def _record_mcp_call(self):
        """记录MCP调用时间"""
        self.last_mcp_calls.append(datetime.now())

# 全局社区管理器实例（延迟初始化）
_community_manager = None

def get_community_manager(database_manager=None):
    """
    获取社区管理器实例
    
    Args:
        database_manager: 数据库管理器实例
        
    Returns:
        CommunityManager: 社区管理器实例
    """
    global _community_manager
    if _community_manager is None and database_manager is not None:
        _community_manager = CommunityManager(database_manager)
    return _community_manager
