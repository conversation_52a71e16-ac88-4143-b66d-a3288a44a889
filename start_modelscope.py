#!/usr/bin/env python3
"""
魔搭平台启动脚本
===============

专为魔搭平台优化的启动脚本
确保环境检查和优雅启动

功能：
1. 环境检查
2. 依赖验证
3. API连接测试
4. 应用启动

适配要求：
- 支持魔搭平台标准部署
- 自动端口检测
- 健康检查
"""

import sys
import os
import subprocess
from pathlib import Path

def check_environment():
    """检查运行环境"""
    print("🔍 正在检查魔搭部署环境...")
    
    # 检查Python版本
    if sys.version_info < (3.8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = ['gradio', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n📦 正在安装缺失的包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败")
            return False
    
    return True

def check_api_configuration():
    """检查API配置"""
    print("\n🔑 正在检查API密钥配置...")
    
    # 硬编码密钥（您提供的）
    api_key = "sk-7e74c669384c4c83bd75673d93a492b3"
    
    if api_key and len(api_key) > 20:
        print("✅ 通义千问API密钥已配置")
        print(f"   密钥预览: {api_key[:10]}...{api_key[-4:]}")
        return True
    else:
        print("❌ API密钥配置有误")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n🌐 正在测试API连接...")
    
    try:
        # 简单的API连接测试
        import requests
        
        headers = {
            "Authorization": f"Bearer sk-7e74c669384c4c83bd75673d93a492b3",
            "Content-Type": "application/json"
        }
        
        # 简单的测试请求
        test_payload = {
            "model": "qwen-max",
            "messages": [{"role": "user", "content": "测试连接"}],
            "max_tokens": 10
        }
        
        response = requests.post(
            "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
            headers=headers,
            json=test_payload,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ API连接测试成功")
            return True
        else:
            print(f"⚠️ API响应状态: {response.status_code}")
            print("🔄 继续启动，运行时再处理")
            return True  # 非关键错误，继续启动
            
    except Exception as e:
        print(f"⚠️ API连接测试失败: {e}")
        print("🔄 继续启动，运行时再处理")
        return True  # 非关键错误，继续启动

def start_application():
    """启动应用"""
    print("\n🚀 正在启动心理小助手喵呜...")
    print("=" * 60)
    print("🌸 大学生心理小助手 喵呜 - 魔搭部署版 🌸")
    print("=" * 60)
    print("✨ 特性亮点:")
    print("  🛡️ 专业危机检测系统")
    print("  💝 个性化心理支持") 
    print("  🧠 荣格八维性格测试")
    print("  🎭 八维认知功能深度探索")
    print("  🌈 积极心理学应用")
    print("  🚀 魔搭平台原生支持")
    print("=" * 60)
    
    try:
        # 导入并启动应用
        from app import demo
        print("✅ 应用模块加载成功！")
        print("🌐 正在启动Web服务...")
        
        # 魔搭平台标准配置启动 - 保持CSS配置
        demo.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True,
            show_api=False,
            # 确保CSS不被覆盖
            css=None
        )
        
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        print("💡 请检查错误信息并确保所有文件完整")
        return False
    
    return True

def main():
    """主函数"""
    print("🌸 心理小助手喵呜 - 魔搭启动向导 🌸\n")
    
    # 步骤1: 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请修复后重试")
        return 1
    
    # 步骤2: 检查API配置
    if not check_api_configuration():
        print("\n❌ API配置检查失败，请检查密钥")
        return 1
    
    # 步骤3: 测试API连接
    test_api_connection()
    
    # 步骤4: 启动应用
    if not start_application():
        print("\n❌ 应用启动失败")
        return 1
    
    print("\n🎉 启动完成！享受与喵呜的温暖对话吧！")
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 再见！感谢使用心理小助手喵呜！")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        sys.exit(1)
